import { Component, OnInit, ViewChild, Input } from "@angular/core";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { UserService } from "../../_services/user.service";
import { ObligacionService } from "../obligacion.service";
import { DetalleObligacionService } from "./detalle_obligacion.service";
import { DetalleCompromisoPago } from "./detalle-compromiso-pago";
import { CompromisoPago } from "../compromiso-pago";

import { global } from "../../_services/global";
import swal from "sweetalert2";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { FormularioCompromisoPagoComponent } from "./formulario/formulario-compromiso-pago.component";
import { Location } from "@angular/common";

@Component({
  selector: "app-detalle-compromiso-pago",
  templateUrl: "./detalle-compromiso-pago.component.html",
  styleUrls: ["../compromiso-pago.component.css"],
  providers: [UserService, ObligacionService, DetalleObligacionService],
})
export class DetalleCompromisoPagoComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public isDisabled: boolean = true;

  public dataSource = null;
  public datosObligacion: CompromisoPago;
  public nombreCliente: string;
  public nombreIntermediario: string;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _route: ActivatedRoute,
    private _userService: UserService,
    private _obligacionService: ObligacionService,
    private _detalleObligacionService: DetalleObligacionService,

    private dialog: MatDialog,
    private _location: Location
  ) {
    this.page_title = "Detalle de la Obligación";
    this.identity = this._userService.getIdentity();
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "id",
    "codclaegre",
    "codcptoegre",
    "desccptoegre",
    "mtodetegrelocal",
    "mtodetegremoneda",
    "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getDetalleObligacion();
  }

  getDetalleObligacion() {
    console.log("getDetalleObligacion");
    let detalleObligacion: any[] = [];
    let id: number;
    this._route.params.subscribe((params) => {
      id = +params["id"];
    });
    //Obtiene los valores de la obligacion
    this._obligacionService.getObligacion(id).subscribe(
      (response) => {
        if (response.status == "success") {
          this.datosObligacion = response.obligacion;
          this.nombreCliente = response.obligacion.cliente.nombre
            ? response.obligacion.cliente.nombre
            : null;
          //this.nombreIntermediario = response.obligacion.intermediario.nombre ? response.obligacion.intermediario.nombre : null;

          if (response.obligacion.stsoblig == "VAL") this.isDisabled = false;
          else this.isDisabled = true;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
    //Obtiene los valores del detalle de la obligacion
    this._detalleObligacionService.getDetalleObligacion(id).subscribe(
      (response) => {
        if (response.status == "success") {
          response.obligacion.forEach((dato) => {
            detalleObligacion.push({
              id: dato.id,
              codclaegre: dato.codclaegre,
              codcptoegre: dato.codcptoegre,
              desccptoegre: dato.concepto_egreso.desccptoegre,
              mtodetegrelocal: dato.mtodetegrelocal,
              mtodetegremoneda: dato.mtodetegremoneda,
            });
          });
        }
        this.dataSource = new MatTableDataSource<DetalleCompromisoPago>(
          detalleObligacion
        );
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  onDelete(id) {
    //console.log('Eliminar', id);
    swal
      .fire({
        title: "¿Estas seguro?",
        text: "Una vez borrado no podrás recuperarlo!",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete) {
          this._detalleObligacionService.delete(id).subscribe(
            (response) => {
              swal.fire({
                text: "Registro borrado exitosamente!",
                icon: "success",
              });
              this.getDetalleObligacion();
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }

  onEdit(element) {
    console.log("onEdit");
    this.openDialogo(element, "edit");
  }

  onNew(element) {
    console.log("onNew");
    this.openDialogo(element, "new");
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  openDialogo(element: CompromisoPago = null, tipo: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      title:
        tipo == "new"
          ? `Nuevo Detalle Obligacion No.${element.id}`
          : `Modificacion de Datos de la Obligacion No. ${this.datosObligacion.id}, Detalle No. ${element.id}`,
      obligacion: this.datosObligacion,
      element: tipo == "new" ? null : element,
    };
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "710px";
    dialogConfig.width = "550px";

    const dialogRef = this.dialog.open(FormularioCompromisoPagoComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      console.log(`Dialog result ${result}`);
      this.getDetalleObligacion();
    });
  }

  onBack() {
    this._location.back();
  }

  onDisabledBtnActivated() {
    return this.isDisabled;
  }
}
