import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ActivatedRoute } from "@angular/router";
import { BehaviorSubject, Observable } from "rxjs";
import { map, switchMap } from "rxjs/operators";
import { TableColumn } from "src/app/components/icore-table/table";
import { IReclamoTerceroPayload, ITerceroReclamo } from "src/app/interface";
import { ListClienteComponent } from "src/app/components/list-cliente/list-cliente.component";
import { ModalService } from "src/app/_services/modal.service";
import Swal from "sweetalert2";
import { ReclamosService } from "../../services/reclamos.service";
import { ReclamoFacade } from "../../+state/reclamos.facade";

@Component({
  selector: "app-afectados-terceros",
  templateUrl: "./afectados-terceros.component.html",
  styleUrls: ["./afectados-terceros.component.css"],
})
export class AfectadosTercerosComponent implements OnInit {
  pageTitle = "Terceros";
  displayedColumns: TableColumn<ITerceroReclamo>[] = [
    {
      columnDef: "idreclamotercero",
      header: "#",
      cell: (element: Record<string, any>) => `${element["idreclamotercero"]}`,
    },
    {
      columnDef: "idcliente",
      header: "id cliente",
      cell: (element: Record<string, any>) => `${element["idcliente"]}`,
    },
    {
      columnDef: "nombre",
      header: "Cliente",
      cell: (element: Record<string, any>) => `${element["nombre"]}`,
    },
    {
      columnDef: "tipo",
      header: "Tipo",
      cell: (element: Record<string, any>) => `${element["tipo"]}`,
    },
    {
      columnDef: "actions",
      header: "",
      actions: [
        {
          color: "primary",
          icon: "edit",
          tooltipText: "Editar",
          onClick: ({ row }) => this.onUpdate(row.idreclamotercero.toString()),
        },
        {
          color: "accent", // pasar class danger (bg-danger ml-2)
          icon: "delete",
          tooltipText: "Eliminar",
          onClick: ({ row }) =>
            this.deleteTercero(row.idreclamotercero.toString()),
        },
      ],
    },
  ];

  title$ = this.facade.loadTitle$.pipe(
    map((entity) =>
      entity.filter((e) => e.idreclamo.toString() === this.idreclamo)
    )
  );

  refreshData$ = new BehaviorSubject<void>(null);
  data$ = this._terceros();
  marcas$ = this.reclamosService.getMarcas();
  tiposCli$ = this.reclamosService.getTipoCliTerc();
  lineas$: Observable<any>;
  isLesionadoSelect: boolean = false;

  idreclamo: string = "";
  formTercero = new FormGroup({
    idcliente: new FormControl("", [Validators.required]),
    tipo: new FormControl("", [Validators.required]),
    IndTerceroCulpable: new FormControl(false),
    Descripcion: new FormControl(""),
    placa: new FormControl(""),
    chasis: new FormControl(""),
    motor: new FormControl(""),
    marca: new FormControl(""),
    modelo: new FormControl(""),
    anio: new FormControl(""),
    version: new FormControl(""),
    datosbien: new FormControl(""),
    isAsegurado: new FormControl(false),
    telefonoLesionado: new FormControl(""),
    naturalezaLesiones: new FormControl(""),
    llevadoHospital: new FormControl(false),
    hospital: new FormControl(""),
  });
  updating: boolean = false;
  textTooltip = "Crear";
  idreclamotercero: string;

  constructor(
    private router: ActivatedRoute,
    private reclamosService: ReclamosService,
    private modalService: ModalService,
    private facade: ReclamoFacade,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.router.paramMap.subscribe((param) => {
      this.idreclamo = param.get("id");
    });
    this.refreshData$.next();

    this.formTercero.get("tipo").valueChanges.subscribe((option) => {
      if (option === "1" || option === "2") {
        this.formTercero.get("placa").setValidators(Validators.required);
        this.formTercero.get("placa").updateValueAndValidity();

        this.formTercero.get("chasis").setValidators(Validators.required);
        this.formTercero.get("chasis").updateValueAndValidity();

        this.formTercero.get("marca").setValidators(Validators.required);
        this.formTercero.get("marca").updateValueAndValidity();

        this.formTercero.get("modelo").setValidators(Validators.required);
        this.formTercero.get("modelo").updateValueAndValidity();

        this.formTercero.get("anio").setValidators(Validators.required);
        this.formTercero.get("anio").updateValueAndValidity();
      } else {
        this.formTercero.get("placa").clearValidators();
        this.formTercero.get("placa").updateValueAndValidity();

        this.formTercero.get("chasis").clearValidators();
        this.formTercero.get("chasis").updateValueAndValidity();

        this.formTercero.get("marca").clearValidators();
        this.formTercero.get("marca").updateValueAndValidity();

        this.formTercero.get("modelo").clearValidators();
        this.formTercero.get("modelo").updateValueAndValidity();

        this.formTercero.get("anio").clearValidators();
        this.formTercero.get("anio").updateValueAndValidity();
      }

      if (option === "3") {
        this.isLesionadoSelect = true;

        this.formTercero
          .get("telefonoLesionado")
          .setValidators(Validators.required);
        this.formTercero.get("telefonoLesionado").updateValueAndValidity();

        this.formTercero
          .get("naturalezaLesiones")
          .setValidators(Validators.required);
        this.formTercero.get("naturalezaLesiones").updateValueAndValidity();
      } else {
        this.formTercero.get("telefonoLesionado").clearValidators();
        this.formTercero.get("telefonoLesionado").updateValueAndValidity();

        this.formTercero.get("naturalezaLesiones").clearValidators();
        this.formTercero.get("naturalezaLesiones").updateValueAndValidity();
        this.isLesionadoSelect = false;
      }
    });
  }

  selectMarca(marca: string) {
    this.lineas$ = this.reclamosService.getLineas(marca);
  }

  submitTercero() {
    const idcliente = this.formTercero.controls["idcliente"].value.split("|");
    let payload: IReclamoTerceroPayload = {
      ...this.formTercero.value,
      idreclamo: this.idreclamo,
      idcliente: idcliente[0],
    };

    if (!this.updating) {
      this.saveTercero(payload);
    } else {
      this.updateTercero(payload);
    }
  }

  saveTercero(payload: IReclamoTerceroPayload) {
    this.reclamosService.createTercero(payload).subscribe({
      next: () => {
        this.snackBar.open("Guardado", "", {
          duration: 5 * 1000,
          horizontalPosition: "start",
        });
        this.formTercero.reset();
        this.refreshData$.next();
      },
      error: () => {
        Swal.fire("Error", "Error guardando", "error");
      },
    });
  }

  onClientes() {
    const close = {
      next: (data) => {
        this.formTercero.controls["idcliente"].setValue(
          `${data.idcliente} | ${data.nombre}`
        );
        console.log(data);
      },
      error: (err: Error) => console.error("Observer got an error: ", err),
    };
    this.modalService.openDialog<ListClienteComponent>({
      title: "Listado de clientes",
      component: ListClienteComponent,
      actionClose: close,
    });
  }

  updateTercero(payload: IReclamoTerceroPayload) {
    this.reclamosService
      .updateTercero(payload, this.idreclamotercero)
      .subscribe({
        next: () => {
          this.snackBar.open("Editado", "", {
            duration: 5 * 1000,
            horizontalPosition: "start",
          });
          this.formTercero.reset();
          this.refreshData$.next();
          this.updating = false;
          this.textTooltip = "Crear";
        },
        error: () => {
          Swal.fire("Error", "error al actualizar", "error");
        },
      });
  }

  onUpdate(idreclamoTercero: string) {
    this.idreclamotercero = idreclamoTercero;
    this.reclamosService.getTerceroById(idreclamoTercero).subscribe({
      next: (tercero) => {
        this.selectMarca(tercero.marca);
        tercero.tipo === "3"
          ? (this.isLesionadoSelect = true)
          : (this.isLesionadoSelect = false);
        this.formTercero.setValue({
          idcliente: `${tercero.idcliente} | ${tercero.nombre}`,
          tipo: tercero.tipo,
          IndTerceroCulpable: Number.parseInt(tercero.IndTerceroCulpable),
          Descripcion: tercero.Descripcion,
          placa: tercero.placa,
          chasis: tercero.chasis,
          motor: tercero.motor,
          marca: tercero.marca,
          modelo: tercero.modelo,
          anio: tercero.anio,
          version: tercero.version,
          datosbien: tercero.datosbien,
          isAsegurado: tercero.is_asegurado,
          telefonoLesionado: tercero.telefono_lesionado,
          naturalezaLesiones: tercero.naturaleza_lesiones,
          llevadoHospital: tercero.llevado_hospital,
          hospital: tercero.hospital,
        });
        this.updating = true;
        this.textTooltip = "Editar";
      },
    });
  }

  deleteTercero(idreclamotercero: string) {
    Swal.fire({
      title: "¿Desea eliminar el tercero?",
      text: "Esta accion no se puede revertir.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Si, eliminar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        this.reclamosService.deleteTercero(idreclamotercero).subscribe({
          next: () => {
            this.snackBar.open("Eliminado", "", {
              duration: 3 * 1000,
              horizontalPosition: "start",
            });
            this.refreshData$.next();
          },
        });
      }
    });
  }

  private _terceros() {
    return this.refreshData$.pipe(
      switchMap((_) => this.reclamosService.getTercero(this.idreclamo))
    );
  }
}
