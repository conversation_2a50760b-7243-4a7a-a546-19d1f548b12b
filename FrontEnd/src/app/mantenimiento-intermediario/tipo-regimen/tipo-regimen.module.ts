import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TipoRegimenRoutingModule } from './tipo-regimen-routing.module';
import { TipoRegimenComponent } from './tipo-regimen.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { ComponentsModule } from 'src/app/components/components.module';
import { FormularioTipoRegimenComponent } from './modal-regimen/tipo-regimen/formulario-tipo-regimen/formulario-tipo-regimen.component';
import { SharedModule } from 'src/app/shared/shared.module';


@NgModule({
  declarations: [
    TipoRegimenComponent,
    FormularioTipoRegimenComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TipoRegimenRoutingModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    NgxSkeletonLoaderModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    ComponentsModule,
    SharedModule,
  ]
})
export class TipoRegimenModule { }
