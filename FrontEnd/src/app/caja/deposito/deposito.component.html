<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{page_title}}</h1>
        </div>          
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <table mat-table [dataSource]="dataSource" matSort class="table-responsive mat-elevation-z8" perfectScrollbar>   

           <!-- id Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Deposito </th>
            <td mat-cell  width="5%" *matCellDef="let element"> {{element.id}} </td>
          </ng-container>

          <!-- Estado Column -->
          <ng-container matColumnDef="stsdep">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Estado </th>
            <td mat-cell width="5%"  *matCellDef="let element"> {{element.stsdep}} </td>
          </ng-container>

          <!-- NumeroDeposito Column -->
          <ng-container matColumnDef="fecconfirma">
            <th mat-header-cell *matHeaderCellDef mat-sort-header >Fecha Confirmacion</th>
            <td mat-cell  width="5%" *matCellDef="let element"> {{element.fecconfirma}} </td>
          </ng-container>          

          <!-- Entidad Financiera Column -->
          <ng-container matColumnDef="identfinan">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Entidad Financiera </th>
            <td mat-cell width="5%"  *matCellDef="let element"> {{element.identfinan}} </td>
          </ng-container>

          <ng-container matColumnDef="idcuentabancaria">
            <th mat-header-cell text-align ="right"  *matHeaderCellDef mat-sort-header >Numero de cuenta</th>
            <td mat-cell align="right" width="7%"  *matCellDef="let element" > {{element.numero_cuenta}} </td>
          </ng-container>

          <ng-container matColumnDef="numero_boleta">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Numero de Boleta</th>
            <td mat-cell width="5%" *matCellDef="let element"> {{element.numero_boleta}} </td>
          </ng-container>

          <!-- Moneda Column -->
          <ng-container matColumnDef="codmoneda">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>    Moneda </th>
            <td mat-cell  width="5%"  *matCellDef="let element"> {{element.codmoneda}} </td>
          </ng-container>

           <!-- Fecha Column -->
         <ng-container matColumnDef="fecsts">
           <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha</th>
           <td mat-cell width="5%" *matCellDef="let element"> {{element.fecsts}} </td>
         </ng-container>

           <!-- Monto Column
           <ng-container matColumnDef="mtodeplocal">
            <th mat-header-cell  *matHeaderCellDef mat-sort-header> Monto</th>
            <td mat-cell align="center"  width="5%" *matCellDef="let element"> {{element.mtodeplocal | currency :' ':'code'}} </td>
          </ng-container> -->

           <!-- Monto Moneda Column -->
           <ng-container matColumnDef="mtodepmoneda">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto Moneda </th>
            <td mat-cell width="5%" *matCellDef="let element"> {{element.mtodepmoneda | currency :' ':'code'}} </td>
          </ng-container>

          <ng-container matColumnDef="textmotvanul" >
            <th mat-header-cell *matHeaderCellDef mat-sort-header style="visibility: hidden; display: none;"> Descripción</th>
            <td mat-cell *matCellDef="let element" style="visibility: hidden; display: none;"> {{element.textmotvanul}} </td>
          </ng-container>

          <!-- Botones Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element"> 
              
              <button mat-raised-button color="primary" 
              (click)="onEdit(element)" 
              [disabled]="(element.stsdep == 'ACT' || element.stsdep == 'CON' || element.stsdep == 'ANU') ? true:false"
              matTooltip="Editar Deposito"
              matTooltipClass="tooltip-red"> <mat-icon>edit</mat-icon></button>
              &nbsp;

              <button mat-raised-button color="accent"
              (click)="onDelete(element.id)" 
              [disabled]="(element.stsdep == 'ACT' || element.stsdep == 'CON' || element.stsdep == 'ANU') ? true:false"
              matTooltip="Eliminar Deposito"
              matTooltipClass="tooltip-red">Eliminar<mat-icon>delete</mat-icon></button>
              &nbsp;

              <button mat-raised-button color="basic"
              (click)="onDetail(element)" 
              matTooltip="Detalle Deposito"
              matTooltipClass="tooltip-red">Detalle<mat-icon>subject</mat-icon></button>

              &nbsp;
              <button mat-flat-button color="accent"
              (click)="getActivateDeposito(element)"
              [disabled]="(element.stsdep == 'ACT' || element.stsdep == 'CON' || element.stsdep == 'ANU') ? true:false" 
                      matTooltip="Activar Deposito"
                      matTooltipClass="tooltip-red"
              >
                Activar
              </button>

              &nbsp;
              <button mat-flat-button color="accent" 
              (click)= "getNullDeposito(element)"
              [disabled]="(element.stsdep == 'VAL' || element.stsdep == 'CON' || element.stsdep == 'ANU') ? true:false"
                      matTooltip="Anular Deposito"
                      matTooltipClass="tooltip-red"
              >
                Anular
              </button>

              &nbsp;
              <button mat-flat-button color="accent" 
              (click) = "getEntidadFinanciera(element)"
              [disabled]="(element.stsdep == 'VAL' || element.stsdep == 'CON' || element.stsdep == 'ANU') ? true:false"
                      matTooltip="Confirmar Deposito"
                      matTooltipClass="tooltip-red"
              >
                Confirmar
              </button>


            </td>
          </ng-container>


          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        </table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>
</div>