<h1>Detalle de la distribucion de reaseguro</h1>
<div class="p-2" *ngIf="datasource; else loading">
  <icore-table
    [columns]="displayedColumns"
    [data]="datasource.data"
    [length]="datasource.data.length"
  >
    <h1>No hay operaciones</h1>
  </icore-table>
</div>
<button mat-raised-button color="primary" (click)="close()">
  <mat-icon> close </mat-icon>
  Cerrar
</button>

<ng-template #loading>
  <div class="mx-2">
    <ngx-skeleton-loader
      [count]="displayedColumns.length"
      appearance="line"
    ></ngx-skeleton-loader>
  </div>
</ng-template>
