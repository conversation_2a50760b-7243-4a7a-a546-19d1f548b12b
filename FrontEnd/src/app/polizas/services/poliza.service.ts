import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import {
  IPoliza,
  IPolizaGeneral,
  Pagination,
  filters,
} from "src/app/interface";
import { IOperpoliza } from "src/app/interface/operPoliza";
import { IResponsePoliza } from "src/app/interface/polizas.interface";
import { environment } from "../../../environments/environment";
import { IDatospoliza } from "./../../interface/polizas.interface";
import {
  IResponseData,
  IResponseData2,
} from "./../../interface/response.interface";

@Injectable({
  providedIn: "root",
})
export class PolizaService {
  private readonly urlBase: string = environment.apiUrl;
  private readonly urlBaseV1: string = environment.apiUrlv1;
  constructor(private readonly http: HttpClient) {}

  getAllPolizas(): Observable<IPoliza[]> {
    return this.http.get(`${this.urlBase}/polizas/activas`).pipe(
      map((res: Partial<IResponsePoliza>) => {
        const data = res.polizaProducto || [];
        return data;
      }),
    );
  }
  //servicio para mostrar los datos generales relacionados a la poliza
  getDatosGeneralesPolizas(numpol: string): Observable<IDatospoliza> {
    return this.http
      .get(`${this.urlBase}/polizadatos/generales/${numpol}`)
      .pipe(
        map((res: Partial<IResponseData2<IDatospoliza>>) => {
          const data = res.data;
          return data;
        }),
      );
  }

  getCambioDia(fecha: string) {
    return this.http.get(`${this.urlBase}/cambio-dia/${fecha}`).pipe(
      map((res: Partial<IResponseData2<any>>) => {
        return res.data;
      }),
    );
  }

  //servicio para mostrar las operaciones relacionadas a la poliza
  getOperacionPoliza(idpoliza: string): Observable<IOperpoliza[]> {
    return this.http.get(`${this.urlBase}/poliza/operaciones/${idpoliza}`).pipe(
      map((res: Partial<IResponseData2<IOperpoliza[]>>) => {
        const data = res.data;
        return data;
      }),
    );
  }

  getPolizasGeneral(
    pagination: Pagination,
    type?: filters,
    value?: string,
  ): Observable<{ data: IPolizaGeneral[]; total: number }> {
    let params = new HttpParams()
      .set("page", pagination.currentPage)
      .set("size", pagination.pageSize);

    if (type) {
      params = params.set(`filter[${type}]`, value);
    }

    return this.http.get(`${this.urlBase}/polizas`, { params }).pipe(
      map((res: Partial<IResponseData<IPolizaGeneral>>) => {
        return {
          data: res.data,
          total: res.total,
        };
      }),
    );
  }

  getPagadoresPoliza(idpoliza: string): Observable<any> {
    return this.http
      .get(`${this.urlBaseV1}/polizas/pagadores/${idpoliza}`)
      .pipe(
        map((res: Partial<IResponseData2<any[]>>) => {
          return res.data;
        }),
      );
  }

  filtersPolizas(
    type: filters,
    value: string,
    pagination: Pagination,
  ): Observable<{ data: IPolizaGeneral[]; total: number }> {
    let params = new HttpParams()
      .set(`filter[${type}]`, value)
      .set("page", pagination.currentPage)
      .set("size", pagination.pageSize);

    return this.http.get(`${this.urlBase}/polizas/filters`, { params }).pipe(
      map((res: Partial<IResponseData<IPolizaGeneral>>) => {
        return {
          data: res.data,
          total: res.total,
        };
      }),
    );
  }

  motivosAnulacion(): Observable<
    { label: string; value: string; catalogo: string }[]
  > {
    return this.http
      .get(`${this.urlBase}/poliza/motivos/anulacion`)
      .pipe(
        map(
          (
            res: Partial<
              IResponseData2<
                { label: string; value: string; catalogo: string }[]
              >
            >,
          ) => res.data,
        ),
      );
  }

  reporteriaEmision(idoperacion: number, reports: any[]) {
    return this.http.post(
      `${this.urlBase}/emision/reporteria/${idoperacion}`,
      reports,
    );
  }

  getReportesEmision() {
    return this.http.get(`${this.urlBase}/emision/reporteria`);
  }

  reporteMovFondo(data: any) {
    return this.http.post(`${this.urlBase}/emision/reporteria-vida`, data);
  }

  updateContratante(idpoliza: string, data: { contratante_id: number }) {
    return this.http.put(
      `${this.urlBaseV1}/polizas/contratante/${idpoliza}`,
      data,
    );
  }

  getDataVehiculo(idpoliza: number, certificado: number) {
    return this.http
      .get(`${this.urlBaseV1}/polizas/vehiculo/${idpoliza}/${certificado}`)
      .pipe(
        map((res: any) => {
          return res.data.data ?? res.data ?? res;
        }),
      );
  }

  updateVehiculo(idpoliza: number, certificado: number, data: any) {
    return this.http.put(
      `${this.urlBaseV1}/polizas/change/vehiculo/${idpoliza}/${certificado}`,
      data,
    );
  }

  deleteCobertura(
    idpoliza: string,
    certificado: string,
    ramo: string,
    cobertura: string,
  ) {
    return this.http.delete(
      `${this.urlBase}/delete-cobertura/${idpoliza}/${certificado}/${ramo}/${cobertura}`,
    );
  }

  excluirCobertura(data: {
    idpoliza: string;
    numcert: string;
    idcobertura: string;
  }) {
    return this.http.post(`${this.urlBaseV1}/polizas/excluir-cobertura`, data);
  }

  addCobertura(data: {
    idpoliza: string;
    numcert: string;
    idcobertura: string;
  }) {
    return this.http.post(`${this.urlBaseV1}/polizas/incluir-cobertura`, data);
  }

  addRecargoDescuento(data: {
    idpoliza: string;
    numcert: string;
    idrecadcto: string;
  }) {
    return this.http.post(`${this.urlBaseV1}/polizas/add-recadcto`, data);
  }

  getCoberturasForPoliza(idpoliza: string) {
    return this.http.get(
      `${this.urlBaseV1}/polizas/poliza-coberturas/${idpoliza}`,
    );
  }

  getRecargosDescuentosForPoliza(idpoliza: string, numcert: string) {
    return this.http.get(
      `${this.urlBaseV1}/polizas/recadctos/${idpoliza}/${numcert}`,
    );
  }

  editarCobertura(
    idCobertura: string,
    data: {
      idpoliza: string;
      numcert: string;
      prima?: number;
      suma?: number;
    },
  ) {
    return this.http.put(
      `${this.urlBaseV1}/polizas/update/cobertura/${idCobertura}`,
      data,
    );
  }
  getEndososSinValor(idpoliza: string, numcert: string) {
    return this.http.get(
      `${this.urlBase}/polizas/endoso-sin-valor/consulta/${idpoliza}/${numcert}`,
    );
  }

  saveEndosoSinValor(poliza: string, certificado: string, text: string) {
    return this.http.post(
      `${this.urlBase}/polizas/endoso-sin-valor/agregar/${poliza}/${certificado}`,
      { data: text },
    );
  }
  getEndosoReport(endoso: string, poliza) {
    return this.http.get(
      `${this.urlBase}/polizas/endoso-sin-valor/reporte/${endoso}/${poliza}`,
    );
  }

  asignarCodigoPlanilla(id: string, data: any) {
    return this.http.put(
      `${this.urlBase}/polizas/asignar-codigo-planilla/${id}`,
      data,
    );
  }

  changeCobrador(polizaid: string, id: number) {
    return this.http.patch(
      `${this.urlBaseV1}/polizas/change/cobrador/${polizaid}`,
      { cobrador: id },
    );
  }
}
