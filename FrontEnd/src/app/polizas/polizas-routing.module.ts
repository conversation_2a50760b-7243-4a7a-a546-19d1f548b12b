import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AuthGuard } from "../_guards/auth.guard";
import { CertificadosComponent } from './page/certificados/certificados.component';
import { DetallePolizaComponent } from "./page/detalle-poliza/detalle-poliza.component";
import { OperacionesComponent } from './page/operaciones/operaciones.component';
import { TalonarioPagoComponent } from "./page/talonario-pago/talonario-pago.component";
import { PolizasComponent } from "./polizas.component";

const routes: Routes = [
  { path: "", component: PolizasComponent, canActivate: [AuthGuard] },
  {
    path: "detalle/:id",
    component: DetallePolizaComponent,
    canActivate: [AuthGuard],
  },
  {                               
    path: "operaciones/:id",
    component: OperacionesComponent,
    canActivate: [AuthGuard],
  },
  {                               
    path: "certificados/:id",
    component: CertificadosComponent,
    canActivate: [AuthGuard],
  },
  {                               
    path: "operacion/talonario/:idpoliza/:numcert/:operacion",
    component: TalonarioPagoComponent,
    canActivate: [AuthGuard],
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class PolizasRoutingModule {}
