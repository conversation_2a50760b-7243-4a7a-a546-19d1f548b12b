<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg xmlns="http://www.w3.org/2000/svg" height="512" width="512">
  <g fill-rule="evenodd" stroke-width="1pt">
    <path fill="#00c4ff" d="M0-.01h512.003v171.125H0z"/>
    <path fill="#fff" d="M0 170.413h512.003v171.124H0z"/>
    <path fill="#00c4ff" d="M0 340.846h512.003V511.97H0z"/>
  </g>
  <path d="M382.49 221.33c0 14.564-11.864 26.37-26.5 26.37s-26.498-11.806-26.498-26.37 11.864-26.37 26.5-26.37 26.498 11.806 26.498 26.37z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(1.2625 0 0 1.2433 -190.84 -19.626)" stroke="#000" stroke-width=".625" fill="#ffd600"/>
  <path d="M364.43 195.28c-4.34-1.05-8.785.422-10.185.318-1.925 0-6.79-1.68-10.185 0M338.71 200.49c4.305-3.01 9.115 1.086 10.394.315 3.492-2.294 6.736-1.868 10.08.21 2.155 1.272 5.914-3.71 10.29.315" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".5" fill="none"/>
  <path d="M333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-.28 1.89-1.084.945-2.204-5.74-1.995-12.425-4.515-18.585-2.625-1.68 1.19-1.26 1.96.315 2.415z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path d="M333.88 205.63c2.275-1.855 9.694-1.925 17.324 2.414 1.155-.28 1.89-1.084.945-2.204-5.74-1.995-12.425-4.515-18.585-2.625-1.68 1.19-1.26 1.96.315 2.415z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.9748 0 0 .9447 603.337 47.64)" stroke="#000" stroke-width=".5" fill="#efc000"/>
  <path stroke-linejoin="round" d="M330.84 211.83c7.525-4.83 17.464-2.31 21.63.315-6.09-1.155-6.196-1.68-10.606-1.785-3.115.106-7.7-.21-11.024 1.47z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path d="M348.06 211.3c-3.675 7.665-10.08 7.77-14.594-.42" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M250.172 249.47c-1.062 1.163-3.66 1.853-5.805 1.543s-3.02-1.503-1.96-2.665c1.063-1.162 3.662-1.852 5.806-1.542s3.022 1.503 1.96 2.665z" fill-rule="evenodd" fill-opacity=".368"/>
  <path d="M244.89 247.98c.11.27-.228.63-.755.802s-1.042.093-1.152-.177c-.11-.27.23-.63.756-.802s1.04-.093 1.15.177z" fill-rule="evenodd" fill="gold"/>
  <path d="M349.18 224.5c-4.24 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.3 1.462 4.276 0 .977-1.65 7.128 3.113 2.927-3.938" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M341.64 236.31c3.638-.413 9.753-3.188 11.93-.9 1.874-2.063 8.476.6 12.714.9-3.076 1.875-9.302.6-12.265 2.588-2.89-1.763-9.267-.15-12.38-2.588z" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M347.5 239.58c5.514 2.25 6.752 1.913 12.716.225-1.238 3.264-4.398 3.95-6.19 3.826-1.857-.12-4.388.114-6.526-4.05z" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -78.82 48.29)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(-.6968 -.6607 -.6818 .6752 655.544 340.862)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(.682 .675 -.6964 .661 170.235 -128.707)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" transform="matrix(-.0056 -.9447 -.9748 .0054 474.993 589.288)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-.8943 -.376 -.388 .8666 654.602 195.86)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-.6944 -.663 -.6842 .673 648.558 337.493)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" transform="matrix(-.381 -.8696 -.8973 .369 587.322 475.4)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M349.18 224.5c-4.24 7.127 1.537 2.1 2.475 4.164 1.65 1.913 3.3 1.462 4.276 0 .977-1.65 7.128 3.113 2.927-3.938" stroke-opacity=".082" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="none"/>
  <path stroke-linejoin="round" d="M341.64 236.31c3.638-.413 9.753-3.188 11.93-.9 1.874-2.063 8.476.6 12.714.9-3.076 1.875-9.302.6-12.265 2.588-2.89-1.763-9.267-.15-12.38-2.588z" stroke-opacity=".082" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-linecap="round" stroke-width=".625" fill="#f0bf00"/>
  <path stroke-linejoin="round" d="M347.5 239.58c5.514 2.25 6.752 1.913 12.716.225-1.238 3.264-4.398 3.95-6.19 3.826-1.857-.12-4.388.114-6.526-4.05z" stroke-opacity=".082" fill-rule="evenodd" transform="matrix(.9748 0 0 .9447 -87.223 47.64)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6968 -.6607 -.6818 .6752 655.544 340.862)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.682 .675 -.6964 .661 170.235 -128.707)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.0056 -.9447 -.9748 .0054 474.993 589.288)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.8943 -.376 -.388 .8666 654.602 195.86)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6944 -.663 -.6842 .673 648.558 337.493)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.381 -.8696 -.8973 .369 587.322 475.4)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 -.9447 .9748 0 44.815 582.748)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.9748 0 0 .9447 604.637 48.13)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.6968 -.6607 .6818 .6752 -137.85 341.345)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.682 .675 .6964 .661 347.467 -128.463)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.0056 -.9447 .9748 .0054 42.424 589.288)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.8943 -.376 .388 .8666 -136.957 196.355)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.6944 -.663 .6842 .673 -131.525 337.493)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.381 -.8696 .8973 .369 -69.485 475.4)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.6968 -.6607 .6818 .6752 -137.85 341.345)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.682 .675 .6964 .661 347.467 -128.463)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.0056 -.9447 .9748 .0054 42.424 589.288)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.8943 -.376 .388 .8666 -136.957 196.355)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.6944 -.663 .6842 .673 -131.525 337.493)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.381 -.8696 .8973 .369 -69.485 475.4)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 .9447 -.9748 0 472.49 -71.697)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.9748 0 0 -.9447 -87.32 462.914)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.6968 .6607 -.6818 -.6752 655.16 169.7)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.682 -.675 -.6964 -.661 169.85 639.513)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.0056 .9447 -.9748 -.0054 474.894 -78.248)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.8943 .376 -.388 -.8666 654.268 314.703)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.6944 .663 -.6842 -.673 648.843 173.552)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.381 .8696 -.8973 -.369 586.8 35.64)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6968 .6607 -.6818 -.6752 655.16 169.7)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.682 -.675 -.6964 -.661 169.85 639.513)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.0056 .9447 -.9748 -.0054 474.894 -78.248)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.8943 .376 -.388 -.8666 654.268 314.703)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6944 .663 -.6842 -.673 648.843 173.552)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.381 .8696 -.8973 -.369 586.8 35.64)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.9748 0 0 -.9447 596.078 462.74)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(0 .9447 .9748 0 44.394 -79.747)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.6818 -.6752 .6968 -.6607 346.97 639.76)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(.6964 .661 .682 -.675 -137.843 169.465)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.9748 -.0054 .0056 -.9447 602.83 465.07)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.388 -.8666 .8943 -.376 197.336 638.894)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.6842 -.673 .6944 -.663 342.983 633.63)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.8973 -.369 .381 -.8696 485.31 573.508)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6818 -.6752 .6968 -.6607 346.97 639.76)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(.6964 .661 .682 -.675 -137.843 169.465)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M343.17 258.06c-3.977 10.41.38 15.358-1.567 20.964-1.874 5.398-10.025 18.776-3.82 24.59 4.93.227-.853-7.377 9.01-21.456 4.576-6.595-1.195-10.77 3.626-23.707.448-1.106-6.623-2.03-7.25-.39z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.9748 -.0054 .0056 -.9447 602.83 465.07)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.388 -.8666 .8943 -.376 197.336 638.894)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.6842 -.673 .6944 -.663 342.983 633.63)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path stroke-linejoin="round" d="M342.97 258.16c-1.528 11.683-.62 47.577 3.037 46.14 4.05 1.306 4.583-35.162 4.212-45.945-.053-1.194-7.037-1.938-7.25-.195z" stroke-opacity=".149" fill-rule="evenodd" transform="matrix(-.8973 -.369 .381 -.8696 485.31 573.508)" stroke="#000" stroke-width=".625" fill="gold"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(.9748 0 0 .9447 -86.718 48.374)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(.9748 0 0 .9447 -87.486 50.286)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.0793 0 0 .9447 -122.09 52.094)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.0793 0 0 .9447 -123.073 53.795)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.288 0 0 .9447 -191.837 55.603)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.4274 0 0 .9447 -237.42 57.41)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.3578 0 0 .9447 -215.23 59.43)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.323 0 0 .9447 -203.585 61.45)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.3578 0 0 .9447 -215.01 63.362)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.706 0 0 .9447 -328.153 65.594)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".171" transform="matrix(1.4622 0 0 .9447 -248.62 68.146)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -248.51 69.953)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -247.743 72.398)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -247.634 74.525)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -246.317 76.863)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -245 78.777)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -243.794 81.01)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -242.367 83.134)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -241.38 85.153)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -239.297 87.605)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(.94 0 0 .9447 -67.058 89.835)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -248.51 69.953)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -247.743 72.398)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -247.634 74.525)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -246.317 76.863)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -245 78.777)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -243.794 81.01)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -242.367 83.134)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -241.38 85.153)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(1.4622 0 0 .9447 -239.297 87.605)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".078" transform="matrix(.94 0 0 .9447 -66.58 89.65)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-.9748 0 0 .9447 603.894 47.885)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-.9748 0 0 .9447 604.662 49.798)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.0793 0 0 .9447 639.256 51.606)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.0793 0 0 .9447 640.247 53.306)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.288 0 0 .9447 709.014 55.113)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4274 0 0 .9447 754.594 56.92)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.3578 0 0 .9447 732.4 58.94)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.323 0 0 .9447 720.756 60.96)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.3578 0 0 .9447 732.188 62.874)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.706 0 0 .9447 845.322 65.106)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".134" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 765.79 67.658)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 765.68 69.465)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 764.912 71.91)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 764.8 74.035)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 763.488 76.375)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 762.175 78.29)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 760.96 80.52)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 759.536 82.65)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 758.558 84.67)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-1.4622 0 0 .9447 756.465 87.11)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" fill-rule="evenodd" fill-opacity=".867" transform="matrix(-.94 0 0 .9447 584.225 89.34)" stroke="#000" stroke-width="1pt" fill="#00699d"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 765.68 69.465)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 764.912 71.91)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 764.8 74.035)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 763.488 76.375)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 762.175 78.29)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 760.96 80.52)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 759.536 82.65)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 758.558 84.67)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-1.4622 0 0 .9447 756.465 87.11)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path d="M328.14 202.55h-3.15" stroke-opacity=".063" transform="matrix(-.94 0 0 .9447 584.225 89.34)" stroke="#000" stroke-width="1pt" fill="none"/>
  <path stroke-linejoin="round" d="M330.84 211.83c7.525-4.83 17.464-2.31 21.63.315-6.09-1.155-6.196-1.68-10.606-1.785-3.115.106-7.7-.21-11.024 1.47z" stroke-opacity=".387" fill-rule="evenodd" transform="matrix(-.9748 0 0 .9447 604.34 47.397)" stroke="#000" stroke-width=".625" fill="#f0bf00"/>
  <path d="M348.06 211.3c-3.675 7.665-10.08 7.77-14.594-.42" stroke-opacity=".387" transform="matrix(.9748 0 0 .9447 -61.768 47.153)" stroke="#000" stroke-width=".625" fill="none"/>
  <path d="M275.607 249.047c-1.097 1.113-3.782 1.774-5.997 1.477s-3.12-1.44-2.024-2.552c1.097-1.113 3.782-1.774 5.997-1.477s3.12 1.44 2.024 2.552z" fill-rule="evenodd" fill-opacity=".368"/>
  <path d="M270.35 247.497c.108.27-.23.63-.757.802s-1.042.092-1.152-.178c-.108-.27.23-.63.757-.802s1.042-.093 1.152.177z" fill-rule="evenodd" fill="gold"/>
</svg>
