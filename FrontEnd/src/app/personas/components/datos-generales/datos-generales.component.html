<form [formGroup]="personaForm">
  <div class="container">
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>Tipo</mat-label>

        <mat-select formControlName="idtipospersona" name="idtipospersona">
          <mat-option
            *ngFor="let tr of tiposRegistros$ | async"
            [value]="tr.id"
            >{{ tr.tipopersona }}</mat-option
          >
        </mat-select>
      </mat-form-field>
    </div>

    <div
      class="row animate__animated animate__fadeInDown"
      [hidden]="hiddenFormJuridico"
    >
      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="nombreempresa"
          placeholder="Nombre empresa"
          formControlName="nombreempresa"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="actividadprincipal"
          placeholder="Actividad Principal"
          formControlName="actividadprincipal"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <mat-label>Fecha de Constitucion Entidad </mat-label>
        <input
          matInput
          [matDatepicker]="picker2"
          formControlName="fechaconstitucionentidad"
        />
        <mat-datepicker-toggle
          matSuffix
          [for]="picker2"
        ></mat-datepicker-toggle>
        <mat-datepicker #picker2></mat-datepicker>
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <mat-label>Vigencia de la Representación Legal</mat-label>
        <input
          matInput
          [matDatepicker]="picker3"
          formControlName="vigencialegal"
        />
        <mat-datepicker-toggle
          matSuffix
          [for]="picker3"
        ></mat-datepicker-toggle>
        <mat-datepicker #picker3></mat-datepicker>
      </mat-form-field>
    </div>

    <div class="row animate__animated animate__fadeInDown">
      <div class="col">
        <h2 [hidden]="hiddenFormJuridico">Representate legal</h2>
      </div>
    </div>

    <div
      class="row animate__animated animate__fadeInDown"
      [hidden]="hiddenForm"
    >
      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="nombre1"
          placeholder="Primer nombre"
          formControlName="nombre1"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="nombre2"
          placeholder="Segundo nombre"
          formControlName="nombre2"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="nombre3"
          placeholder="Tercer nombre"
          formControlName="nombre3"
          sanitizeUppercase
        />
      </mat-form-field>
    </div>

    <div
      class="row animate__animated animate__fadeInDown"
      [hidden]="hiddenForm"
    >
      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="apellido1"
          placeholder="Primer apellido"
          formControlName="apellido1"
          (keydown)="onDisabledValidar()"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="apellido2"
          placeholder="Segundo apellido"
          formControlName="apellido2"
          sanitizeUppercase
        />
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <input
          matInput
          name="apellido3"
          placeholder="Tercer apellido"
          formControlName="apellido3"
          sanitizeUppercase
        />
      </mat-form-field>
    </div>

    <div
      class="row animate__animated animate__fadeInDown"
      [hidden]="hiddenForm"
    >
      <mat-form-field class="col" appearance="outline">
        <mat-label>Fecha nacimiento</mat-label>
        <input
          matInput
          [matDatepicker]="picker"
          formControlName="fecha_nacimiento"
        />
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
        <mat-error *ngIf="personaForm.controls.fecha_nacimiento.errors">
          La fecha de nacimiento debe ser anterior a la fecha actual.
        </mat-error>
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <mat-label>Género</mat-label>
        <mat-select formControlName="genero" name="genero">
          <mat-option *ngFor="let g of generos" [value]="g">{{
            g === "M" ? "Masculino" : "Femenino"
          }}</mat-option>
        </mat-select>
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <mat-label>Estado civil</mat-label>
        <mat-select formControlName="idestadocivil" name="idestadocivil">
          <mat-option
            *ngFor="let ec of estadosCiviles$ | async"
            [value]="ec.id"
            >{{ ec.estadocivil }}</mat-option
          >
        </mat-select>
      </mat-form-field>

      <mat-form-field class="col" appearance="outline">
        <mat-label>NIT</mat-label>
        <input matInput name="nit" placeholder="NIT" formControlName="nit" />
        <mat-error
          *ngIf="
            personaForm.get('nit').touched && personaForm.get('nit').errors
          "
        >
          {{ personaForm.get("nit").errors.invalidNit }}
        </mat-error>
      </mat-form-field>
    </div>
  </div>
</form>
