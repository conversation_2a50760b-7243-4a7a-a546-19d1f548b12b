import { ICorreo } from "./correo.inteface";
import { IDireccion } from "./direccion.interface";
import { IDocumentos } from "./documento.interface";
import { ITelefono } from "./telefono.interface";

export interface ITipoPersona {
  id: number;
  tipopersona: string;
}

interface IEstadoCivil {
  id?: string;
  estadocivil: string;
}

export interface PayloadAsignarPagador {
  idpoliza: number;
  pagadores: PayloadPagadores[];
}

export interface PayloadPagadores {
  pagador: number;
  porcentaje: number;
  nombre?: string;
}

export interface PayloadContratante {
  id: number;
  nombre?: string;
}

export interface IPersona {
  idtipospersona: number;
  estado_civil: IEstadoCivil;
  nombre1: string;
  nombre2: string;
  nombre3: string;
  apellido1: string;
  apellido2: string;
  apellido3: string;
  fecha_nacimiento: string;
  fechaIngreso: string;
  genero: string;
  nombreempresa: string;
  actividadprincipal: string;
  fechaconstitucionentidad: string;
  vigencialegal: string;
  created_at: string;
  nit: string;
  tipopersona?: ITipoPersona;
  direcciones?: IDireccion[];
  correos?: ICorreo[];
  telefonos?: ITelefono[];
  documentos?: IDocumentos[];
  tipoCliente?: string;
  nombre?: string;
  numcert?: number;
  id?: number;
  idcotizacion?: string;
  planfracc?: number;
}
