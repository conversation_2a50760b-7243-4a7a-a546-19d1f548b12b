import { Component, Inject, OnInit, ViewChild } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { CobradosTipoChequeService } from "./../../services/cobrados-tipo-cheque.service";
import { VwPolizasReqCobradosTipoCheque } from "./../../models/vw-polizas-req-cobrados-tipo-cheque";

@Component({
  selector: "app-list-cobrados-tipo-cheque",
  templateUrl: "./list-cobrados-tipo-cheque.component.html",
  styleUrls: ["./list-cobrados-tipo-cheque.component.css"],
})
export class ListCobradosTipoChequeComponent implements OnInit {
  public page_title: string;
  public status: string;
  public url;
  //public acreencia: EntidadAcreedora;
  public dataSource = null;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private cobradosTipoChequeService: CobradosTipoChequeService,
    //
    private _dialogRef: MatDialogRef<ListCobradosTipoChequeComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.page_title = "Listado de Cobrados Tipo Cheque";
  }

  displayedColumns: string[] = [
    "numpago",
    "poliza",
    "nocheque",
    "nombrE_contratante",
    "nombrE_pagador",
    "codigo",
    "idreling",
    "SerieFel",
    "fecha_ingreso",
    "NumeroDocumentoFel",
    "mtodocinglocal",
    "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getVwChequesReqCobradosTipoCheque();
  }

  getVwChequesReqCobradosTipoCheque() {
    this.cobradosTipoChequeService
      .getVwChequesReqCobradosTipoCheque()
      .subscribe(
        (response) => {
          if (response.status === "success") {
            this.dataSource =
              new MatTableDataSource<VwPolizasReqCobradosTipoCheque>(
                response.viewReqCobradosTipoCheque,
              );

            this.dataSource.filterPredicate = (
              data: VwPolizasReqCobradosTipoCheque,
              filter: string,
            ): boolean => {
              if (!filter) return true;

              const lowerCaseFilter = filter.toLowerCase();

              // Buscar primero en codpol_numpol
              if (data.codpol_numpol?.toLowerCase().includes(lowerCaseFilter)) {
                return true; 
              }

              const dataStr = Object.values(data).reduce((current, value) => {
                return (
                  current + " " + (value ? value.toString().toLowerCase() : "")
                );
              }, "");

              return dataStr.includes(lowerCaseFilter);
            };

            this.dataSource.paginator = this.paginator;
            this.dataSource.sort = this.sort;
          } else {
            // this.status = 'error';
          }
        },
        (error) => {
          console.log(<any>error);
          // this.status = 'error';
        },
      );
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onSelected(element) {
    this._dialogRef.close(<VwPolizasReqCobradosTipoCheque>element);
  }
}
