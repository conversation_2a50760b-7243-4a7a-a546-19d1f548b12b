import {
  AfterViewInit,
  Component,
  ElementRef,
  Input,
  OnDestroy,
  OnInit,
  ViewChild,
} from "@angular/core";
import { FormArray, FormBuilder, FormGroup } from "@angular/forms";
import { fromEvent, Observable } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  map,
  switchMap,
  takeUntil,
  tap,
} from "rxjs/operators";
import { DestroyState } from "src/app/@core";
import { ITipo } from "src/app/interface";
import { CorreosService, TiposService } from "src/app/personas/services";
import { createFormCorreos } from "src/app/_helpers";

@Component({
  selector: "app-correos",
  templateUrl: "./correos.component.html",
  styles: [
    `
      .remove-form {
        margin-left: -59px;
      }
      .remove-form > button {
        width: 50px;
        border-radius: 5px;
      }
    `,
  ],
})
export class CorreosComponent
  extends DestroyState
  implements OnInit, AfterViewInit, OnD<PERSON>roy
{
  @Input() personaForm!: FormGroup;
  @ViewChild("correo") correo: ElementRef;

  tiposCorreos$!: Observable<ITipo[]>;
  message = "";

  constructor(
    private fb: FormBuilder,
    private correosService: CorreosService,
    private tiposService: TiposService
  ) {
    super();
  }

  ngOnInit(): void {
    this.tiposCorreos$ = this.tiposService.getTipoCorreo();
  }

  ngAfterViewInit(): void {
    this.existsCorreo();
  }

  ngOnDestroy(): void {
    this.onDestroy();
  }

  existsCorreo() {
    fromEvent(this.correo.nativeElement, "keyup")
      .pipe(
        debounceTime(500),
        takeUntil(this.destroy$),
        map((e: any) => e.target.value),
        distinctUntilChanged(),
        switchMap((v) => this.correosService.validMail(v)),
        tap((data: any) => (this.message = data.message))
      )
      .subscribe();
  }

  removeDocumentos(i: number) {
    this.correos.removeAt(i);
  }

  get correos(): FormArray {
    return this.personaForm.get("correos") as FormArray;
  }

  moreCorreos() {
    this.correos.push(createFormCorreos(this.fb));
  }
}
