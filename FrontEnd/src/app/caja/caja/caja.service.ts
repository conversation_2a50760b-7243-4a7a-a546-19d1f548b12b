import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { global } from "../../_services/global";

@Injectable()
export class CajaService {
    
    public url: string;
    public selected = {
        id: null, 
        codcaja: '', 
        desccaja: '', 
        fecing: '',
        stscaja: '', 
        fecanu: ''        
    };

    constructor(
        private _http: HttpClient
    ) {
        this.url = global.url;
    }

    test(){
        return "Hola mundo desde el servicio caja!!!";
    }
   
    getCajaUsuario(user): Observable<any>{        
        console.log('getCajaUsuario');
        let json = JSON.stringify(user);
        let params = 'user='+user;
        let headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded');
        return this._http.post(this.url+'caja/getCajaUsuario', params, {headers: headers});
    }

    getCajas(): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.get(this.url+'caja', {headers: headers});
    }

    getCaja(id): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.get(this.url+'caja/' + id, {headers: headers});
    }

    create(/*token,*/ caja): Observable<any>{        
        let params = caja;
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'caja', params, {headers: headers});
    }
  
    update(selected): Observable<any>{
        //console.log('update');
        let params = selected;        
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.put(this.url+'caja/'+params.id, params, {headers: headers});
    }

    delete(/*token, */id): Observable<any>{        
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.delete(this.url+'caja/'+id, {headers: headers});
    }

}