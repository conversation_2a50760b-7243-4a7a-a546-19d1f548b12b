import { Component, OnInit } from "@angular/core";
import { VerificacionContratoComponent } from "src/app/polizas/components/verificacion-contrato/verificacion-contrato.component";
import { IActions } from "src/app/shared/interfaces/actions.interface";
import { ModalService } from "../../../_services/modal.service";
import { DistribucionManualPolizaService } from "../../services/distribucion-manual-poliza.service";

@Component({
  selector: "app-distribucion-manual",
  templateUrl: "./distribucion-manual.component.html",
  styleUrls: ["./distribucion-manual.component.css"],
})
export class DistribucionManualComponent implements OnInit {
  rechargeFunction: Function;

  actions: IActions[] = [
    {
      color: "accent",
      icon: "edit",
      tooltipText: "Distribuir",
      onClick: ({ row }) => {
        this.edit(row);
      },
    } /*
    {
      tooltipText: "Ver",
      icon: "info",
      onClick: ({ row }) => {
        this.router.navigate(["/reaseguros/ficha-contacto", row.id]);
      },
      color: "accent",
    },
    {
      tooltipText: "Activar",
      icon: "check",
      onClick: ({ row }) => {
        this.activate(row);
      },
      color: "primary",
      condition: (row) => row.estado !== "INA",
    },
    {
      tooltipText: "Desactivar",
      icon: "block",
      onClick: ({ row }) => {
        this.deactivate(row);
      },
      color: "warn",
      condition: (row) => row.estado !== "ACT",
    },*/,
  ];

  constructor(
    public distribucionManualPolizaService: DistribucionManualPolizaService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {}

  edit(row: any) {
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<VerificacionContratoComponent>({
      component: VerificacionContratoComponent,
      title: "Distribución Manual",
      element: { idpoliza: row.poliza_id, numcert: row.certificado },
      actionClose: onClose,
    });
  }
}
