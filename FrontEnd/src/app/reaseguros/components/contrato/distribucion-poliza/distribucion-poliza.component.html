<div>
  <div class="d-flex justify-content-between align-items-center">
    <div class="text-center flex-grow-1">
      <h3>
        Distribucion de Poliza {{ data.element.idpoliza }} - Certificado
        {{ data.element.certificado }}
      </h3>
      <h5>La distribucion del contrato se realizo de la siguiente manera:</h5>
    </div>
    <div>
      <mat-form-field appearance="outline" class="compact-form-field">
        <mat-label> Secciones </mat-label>
        <mat-select
          [(ngModel)]="selectedOption"
          (selectionChange)="filterData()"
        >
          <mat-option value="*">Todas</mat-option>
          <mat-option value="1">Sección I</mat-option>
          <mat-option value="2">Sección II</mat-option>
          <mat-option value="3">Sección III</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>

  <div *ngFor="let poliza of filteredDataPoliza" class="p-1">
    <h4 class="text-center">
      <strong>{{ poliza.nombre_tipo_contrato }}</strong> -
      {{ poliza.tipo_contrato_desc }} -
      {{ this.sectionDescription(poliza.seccion) }}
    </h4>
    <h4 class="text-center">
      Utilizado: {{ poliza.moneda }}{{ poliza.cantidad | number }}/{{
        poliza.moneda
      }}{{ poliza.capacidad_tipo_contrato_detalle | number }}
    </h4>
    <div class="d-flex justify-content-around">
      <h4 class="text-center">
        Prima retenida:
        {{ poliza.moneda }}{{ poliza.prima | number }}
      </h4>
      <h4>
        Comision:
        {{ poliza.moneda }}{{ poliza.comision_prima | number }}
      </h4>
      <h4>
        ISR:
        {{ poliza.moneda }}{{ poliza.isr_prima | number }}
      </h4>
      <h4>
        Prima Neta:
        {{ poliza.moneda }}{{ poliza.primatotal | number }}
      </h4>
    </div>
  </div>
</div>
