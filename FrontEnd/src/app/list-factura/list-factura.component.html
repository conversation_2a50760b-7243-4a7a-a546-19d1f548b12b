<div class="mat-elevation-z8">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr />

  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

    <!-- Factura Column -->
    <ng-container matColumnDef="factura">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Factura </th>
      <td mat-cell *matCellDef="let element"> {{element.factura}} </td>
    </ng-container>

    <!-- Producto Column -->
    <ng-container matColumnDef="producto">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Producto </th>
      <td mat-cell *matCellDef="let element"> {{element.producto}} </td>
    </ng-container>

    <!-- Póliza Column -->
    <ng-container matColumnDef="poliza_aseguradora">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Póliza </th>
      <td mat-cell *matCellDef="let element"> {{element.poliza_aseguradora}} </td>
    </ng-container>

    <!-- Pagador Column -->
    <ng-container matColumnDef="pagador">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Pagador </th>
      <td mat-cell *matCellDef="let element"> {{element.pagador}} </td>
    </ng-container>

    <!-- Fecha Emisión Column 
    <ng-container matColumnDef="fecoperacion">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> F.Emisión </th>
      <td mat-cell *matCellDef="let element"> {{element.fecoperacion}} </td>
    </ng-container>
    -->

    <!-- FEL Column -->
    <ng-container matColumnDef="fel">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> FEL </th>
      <td mat-cell *matCellDef="let element"> {{element.fel}} </td>
    </ng-container>

    <!-- FEL Column -->
    <ng-container matColumnDef="ruta_fel">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Factura </th>
      <td mat-cell *matCellDef="let element"> 
        <button mat-raised-button 
                [disabled]="onDisablePreviewFactura(element)" 
                color="accent"
                (click)="factura(element.ruta_fel)"
          > Ver Factura <mat-icon>preview</mat-icon> 
        </button> 
      </td>
    </ng-container>

    <!-- Fecha Vencimiento Column -->
    <ng-container matColumnDef="fecha_vencimiento">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> F. Vencimiento </th>
      <td mat-cell *matCellDef="let element"> {{element.fecha_vencimiento | date:'dd/MM/yyyy' }} </td>
    </ng-container>

    <!-- Monto Column -->
    <ng-container matColumnDef="totalfactura">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto $</th>
      <td mat-cell *matCellDef="let element"> {{element.totalfactura | currency :' ':'code'}} </td>
    </ng-container>

    <!-- Monto Column -->
    <ng-container matColumnDef="totalfactura_local">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto Q</th>
      <td mat-cell *matCellDef="let element"> {{element.totalfactura_local | currency :' ':'code'}} </td>
    </ng-container>

    <!-- Monto Column -->
    <ng-container matColumnDef="moneda">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Moneda</th>
      <td mat-cell *matCellDef="let element"> {{element.moneda}} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>

<factura-icore></factura-icore>
