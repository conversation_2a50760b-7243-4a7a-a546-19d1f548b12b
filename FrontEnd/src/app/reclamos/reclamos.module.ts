import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReclamosComponent } from "./reclamos.component";
import { ReclamosRoutingModule } from "./reclamos-routing.module";
import { MaterialModule } from "../material.module";
import { FormularioComponent } from "./pages/formulario/formulario.component";
import { PolizasComponent } from "./components/polizas/polizas.component";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { DireccionComponent } from "./components/direccion/direccion.component";
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { DetalleComponent } from "./pages/detalle/detalle.component";
import { EstadisticasComponent } from "./pages/estadisticas/estadisticas.component";
import { ComponentsModule } from "../components/components.module";
import { SharedModule } from "../shared/shared.module";
import { RequisitosComponent } from "./pages/requisitos/requisitos.component";
import { AfectadosTercerosComponent } from "./pages/afectados-terceros/afectados-terceros.component";
import { ReservasComponent } from "./pages/reservas/reservas.component";
import { CoberturasPolizaComponent } from "./components/coberturas-poliza/coberturas-poliza.component";
import { RecuperacionesComponent } from "./pages/recuperaciones/recuperaciones.component";
import { SalvamentosComponent } from "./pages/salvamentos/salvamentos.component";
import { RecuperacionNuevoComponent } from "./pages/recuperaciones/recuperacion-nuevo/recuperacion-nuevo.component";
import { RecuperacionDetalleComponent } from "./pages/recuperaciones/recuperacion-detalle/recuperacion-detalle.component";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";
import { MTX_DATETIME_FORMATS } from "@ng-matero/extensions/core";
import { MtxDatetimepickerModule } from "@ng-matero/extensions/datetimepicker";
import { MtxMomentDatetimeModule } from "@ng-matero/extensions-moment-adapter";
import { SalvamentoNuevoComponent } from "./pages/salvamentos/salvamento-nuevo/salvamento-nuevo.component";
import { PresupuestosComponent } from "./pages/presupuestos/presupuestos.component";
import { PresupuestoNuevoComponent } from "./pages/presupuestos/presupuesto-nuevo/presupuesto-nuevo.component";
import { ButtonBackComponent } from "./components/button-back/button-back.component";
import { SalvamentoDetalleComponent } from "./pages/salvamentos/salvamento-detalle/salvamento-detalle.component";
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS } from "@angular/material-moment-adapter";
import { HistoryReservasComponent } from "./components/history-reservas/history-reservas.component";
import { SmsComponent } from "../gestion-cartera/sms/sms.component";
import { EmailComponent } from "../components/email/email.component";
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from "@angular/material/core";
import {
  DetallePresupuestoComponent,
  DialogAutorizarMonto,
  ImagenesDetalle,
} from "./pages/presupuestos/detalle-presupuesto/detalle-presupuesto.component";
import { ProveedoresActivosComponent } from "./components/proveedores-activos/proveedores-activos.component";
import { RepuestosComponent } from "./components/repuestos/repuestos.component";
import { CabinaComponent } from "./pages/cabina/cabina.component";
import { FormularioComponent as FormularioCabinaComponent } from "./pages/cabina/formulario/formulario.component";
import { GalleryComponent } from "./components/gallery/gallery.component";
import { SteponeComponent } from "./pages/cabina/formulario/steps/stepone/stepone.component";
import { SteptwoComponent } from "./pages/cabina/formulario/steps/steptwo/steptwo.component";
import { StepthreeComponent } from "./pages/cabina/formulario/steps/stepthree/stepthree.component";
import { StepfourComponent } from "./pages/cabina/formulario/steps/stepfour/stepfour.component";
import { StepfiveComponent } from "./pages/cabina/formulario/steps/stepfive/stepfive.component";
import { StepsixComponent } from "./pages/cabina/formulario/steps/stepsix/stepsix.component";
import { PagosComponent } from "./pages/pagos/pagos.component";
import { FormularioComponent as FormularioPagosComponent } from "./pages/pagos/formulario/formulario.component";
import { FormularioDetalleReclamoComponent } from "./pages/pagos/formulario-detalle-reclamo/formulario-detalle-reclamo.component";
import { ListReservasComponent } from "./components/list-reservas/list-reservas.component";
import { ListadoCotizacionesComponent } from "./pages/subastas/pages/listado-cotizaciones/listado-cotizaciones.component";
import { DetalleCotizacionComponent } from "./pages/subastas/pages/listado-cotizaciones/detalle-cotizacion/detalle-cotizacion.component";
import { ListClientePolizaComponent } from "./pages/subastas/components/list-cliente-poliza/list-cliente-poliza.component";
import { ListRepuestoComponent } from "./pages/subastas/components/list-repuesto/list-repuesto.component";
import { FormularioCotizacionComponent } from "./pages/subastas/pages/listado-cotizaciones/formulario-cotizacion/formulario-cotizacion.component";
import { FormularioCotizacionDetalleComponent } from "./pages/subastas/pages/listado-cotizaciones/detalle-cotizacion/formulario-cotizacion-detalle/formulario-cotizacion-detalle.component";
import { FlexLayoutModule } from "@angular/flex-layout";
import { ListadoRepuestosComponent } from "./pages/subastas/pages/listado-repuestos/listado-repuestos.component";
import { FormularioRepuestoComponent } from "./pages/subastas/pages/listado-repuestos/formulario-repuesto/formulario-repuesto.component";
import { ListPolizaEstadoComponent } from "../list-poliza-estado/list-poliza-estado.component";
import { ListadoProveedoresComponent } from "../components/proveedores/listado-proveedores/listado-proveedores.component";
import { FormularioProveedorComponent } from "../components/proveedores/listado-proveedores/formulario-proveedor/formulario-proveedor.component";
import { ListadoCotizacionesProveedoresComponent } from "../components/proveedores/listado-proveedores/listado-cotizaciones-proveedores/listado-cotizaciones-proveedores.component";
import { OfertasComponent } from "./pages/subastas/pages/ofertas/ofertas.component";
import { FormularioOfertaComponent } from "./pages/subastas/pages/ofertas/formulario-oferta/formulario-oferta.component";
import { DetalleComponent as DetalleOfertaComponent } from "./pages/subastas/pages/ofertas/detalle/detalle.component";
import { FormularioOfertarComponent } from "./pages/subastas/pages/ofertas/formulario-ofertar/formulario-ofertar.component";
import { AngularImageViewerModule } from "angular-x-image-viewer";
import { OfertantesComponent as OfertasCotizacionesComponent } from "./pages/subastas/pages/listado-cotizaciones/ofertantes/ofertantes.component";
import { PagosFactExternasComponent } from './pages/pagos/pagos-fact-externas/pagos-fact-externas.component';

export const MY_DATE_FORMATS = {
  parse: {
    dateInput: "DD/MM/YYYY",
  },
  display: {
    dateInput: "DD/MM/YYYY",
    monthYearLabel: "MMMM YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "MMMM YYYY",
  },
};

@NgModule({
  declarations: [
    ReclamosComponent,
    FormularioComponent,
    PolizasComponent,
    DireccionComponent,
    DetalleComponent,
    EstadisticasComponent,
    RequisitosComponent,
    AfectadosTercerosComponent,
    ReservasComponent,
    CoberturasPolizaComponent,
    RecuperacionesComponent,
    SalvamentosComponent,
    RecuperacionNuevoComponent,
    RecuperacionDetalleComponent,
    SalvamentoNuevoComponent,
    PresupuestosComponent,
    SmsComponent,
    PresupuestoNuevoComponent,
    ButtonBackComponent,
    SalvamentoDetalleComponent,
    HistoryReservasComponent,
    DetallePresupuestoComponent,
    ProveedoresActivosComponent,
    RepuestosComponent,
    DialogAutorizarMonto,
    CabinaComponent,
    FormularioCabinaComponent,
    FormularioPagosComponent,
    GalleryComponent,
    SteponeComponent,
    SteptwoComponent,
    StepthreeComponent,
    StepfourComponent,
    StepfiveComponent,
    StepsixComponent,
    PagosComponent,
    FormularioDetalleReclamoComponent,
    FormularioOfertarComponent,
    ListReservasComponent,
    ListadoCotizacionesComponent,
    FormularioProveedorComponent,
    ListadoCotizacionesProveedoresComponent,
    ListadoProveedoresComponent,
    DetalleOfertaComponent,
    DetalleCotizacionComponent,
    ListPolizaEstadoComponent,
    ListClientePolizaComponent,
    ListadoRepuestosComponent,
    FormularioRepuestoComponent,
    OfertasComponent,
    FormularioOfertaComponent,
    ListRepuestoComponent,
    FormularioCotizacionComponent,
    FormularioCotizacionDetalleComponent,
    OfertasCotizacionesComponent,
    ImagenesDetalle,
    PagosFactExternasComponent
  ],
  imports: [
    CommonModule,
    ComponentsModule,
    MaterialModule,
    ReclamosRoutingModule,
    ReactiveFormsModule,
    FormsModule,
    NgxSkeletonLoaderModule,
    MtxDatetimepickerModule,
    MtxMomentDatetimeModule,
    PerfectScrollbarModule,
    SharedModule,
    AngularImageViewerModule,
    ComponentsModule,
    FlexLayoutModule.withConfig({
      useColumnBasisZero: false,
      printWithBreakpoints: ["md", "lt-lg", "lt-xl", "gt-sm", "gt-xs"],
    }),
  ],
  providers: [
    {
      provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS,
      useValue: { useUtc: true },
    },
    {
      provide: MTX_DATETIME_FORMATS,
      useValue: {
        parse: {
          dateInput: "YYYY-MM-DD",
          monthInput: "MMMM",
          yearInput: "YYYY",
          timeInput: "HH:mm",
          datetimeInput: "YYYY-MM-DD HH:mm",
        },
        display: {
          dateInput: "YYYY-MM-DD",
          monthInput: "MMMM",
          yearInput: "YYYY",
          timeInput: "HH:mm",
          datetimeInput: "YYYY-MM-DD HH:mm",
          monthYearLabel: "YYYY MMMM",
          dateA11yLabel: "LL",
          monthYearA11yLabel: "MMMM YYYY",
          popupHeaderDateLabel: "MMM DD, ddd",
        },
      },
    },
    {
      provide: MAT_DATE_LOCALE,
      useValue: "es-GT",
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: MY_DATE_FORMATS,
    },
  ],
})
export class ReclamosModule {}
