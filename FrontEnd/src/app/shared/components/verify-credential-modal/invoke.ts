import { PartialObserver } from "rxjs";
import { ModalService } from "../../../_services/modal.service";
import { VerifyCredentialModalComponent } from "./verify-credential-modal.component";

export function VerifyCredentials(
  modalService: ModalService,
  onClose?: PartialObserver<any>,
  roles: string[] = ["admin"],
) {
  modalService.openDialog<VerifyCredentialModalComponent>({
    component: VerifyCredentialModalComponent,
    title: "Verificar credenciales",
    element: { roles },
    actionClose: onClose,
  });
}
