<div class="col-md-12">
  <h1>{{ pageTitle }}</h1>
  <form
    class="col-md-12 ml-0 pl-0"
    [formGroup]="comisionesForm"
  >
    <div class="row">
      <mat-form-field class="col" appearance="outline">
          <mat-label>% Comision</mat-label>
          <input
            matInput
            type="number"
            step="0.00"
            min="0.00"
            max="100000.00"
            name="comision"
            formControlName="comision"
            placeholder="Comision"
            (blur)="intForDecimal($event)"
          />
      </mat-form-field>

      <mat-form-field class="col"appearance="outline">
          <mat-label for="idramo" class="control-label">Ramo</mat-label>
          <mat-select
            formControlName="idramo"
            name="idramo"
          >
            <mat-option *ngFor="let ramo of ramos" [value]="ramo.idramo">{{
              ramo.descripcion
            }}</mat-option>
          </mat-select>
      </mat-form-field>
    </div>

    <div class="row">
      <mat-form-field class="col" appearance="outline">
          <mat-label >Fecha</mat-label>
          <input
            type="date"
            formControlName="fechainivalid"
            matInput
            name="fecha"
            required
          />
      </mat-form-field>
      </div>
  </form>
  <button mat-flat-button color="primary" (click)="saveForm()" matTooltip="Guardar">
    <mat-icon>save</mat-icon>
  </button>
  <button mat-flat-button (click)="close()" class="ml-2 bg-danger" matTooltip="Cerrar">
    <mat-icon>cancel</mat-icon>
  </button>
</div>
