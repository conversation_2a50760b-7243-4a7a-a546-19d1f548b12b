
<div class="mat-elevation-z8">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr />
  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

    <!-- IdCliente Column -->
    <ng-container matColumnDef="idcliente">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Id </th>
      <td mat-cell *matCellDef="let element"> {{element.idcliente}} </td>
    </ng-container>

    <!-- Nombre Column -->
    <ng-container matColumnDef="nombre">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Nombre </th>
      <td mat-cell *matCellDef="let element"> {{element.nombre}} </td>
    </ng-container>

    <!-- Nit Column -->
    <ng-container matColumnDef="nit">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> NIT </th>
      <td mat-cell *matCellDef="let element"> {{element.nit}} </td>
    </ng-container>

    <!-- Producto Column -->
    <ng-container matColumnDef="codigo_producto">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Producto </th>
      <td mat-cell *matCellDef="let element"> {{element.codigo_producto}} </td>
    </ng-container>

    <!-- Póliza Column -->
    <ng-container matColumnDef="poliza_aseguradora">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Póliza </th>
      <td mat-cell *matCellDef="let element"> {{element.poliza_aseguradora}} </td>
    </ng-container>

    <!-- Estado Column -->
    <ng-container matColumnDef="estado_poliza">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
      <td mat-cell *matCellDef="let element"> {{element.estado_poliza}} </td>
    </ng-container>

    <!-- Vigencia Inicial Column -->
    <ng-container matColumnDef="vigencia_inicio">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Vig.Inicial </th>
      <td mat-cell *matCellDef="let element"> {{element.vigencia_inicio}} </td>
    </ng-container>    

    <!-- Vigencia Final Column -->
    <ng-container matColumnDef="vigencia_fin">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Vig.Final </th>
      <td mat-cell *matCellDef="let element"> {{element.vigencia_fin}} </td>
    </ng-container> 

    <!-- Direccion Column -->
    <ng-container matColumnDef="nombre_intermediario">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Intermediario </th>
      <td mat-cell *matCellDef="let element"> {{element.nombre_intermediario}} </td>
    </ng-container>

    <!-- Action Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
      <td mat-cell *matCellDef="let element">
        <!-- <button mat-flat-button color="primary" routerLink="/editar-caja/, caja.id"> -->
        <button mat-flat-button color="primary" (click)="onSelected(element)" matTooltip="Seleccionar"
          matTooltipClass="tooltip-red">
          Seleccionar<mat-icon>touch_app</mat-icon>
        </button>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>