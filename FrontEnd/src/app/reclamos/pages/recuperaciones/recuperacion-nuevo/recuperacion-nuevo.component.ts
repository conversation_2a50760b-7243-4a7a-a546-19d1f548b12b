import { Component, OnInit } from "@angular/core";
import { FormBuilder, Validators } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";

import { RecuperacionesService } from "src/app/reclamos/services/recuperaciones.service";

import { NgxSpinnerService } from "ngx-spinner";
import { ListReclamosComponent } from "src/app/shared/components/list-reclamos/list-reclamos.component";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";

import { VwReclamos } from "src/app/shared/models/vw-reclamos";
import { ListClienteComponent } from "src/app/components/list-cliente/list-cliente.component";
import { Cliente } from "src/app/components/list-cliente/cliente";

import Swal from "sweetalert2";
import { tap } from "rxjs/operators";

@Component({
  selector: "app-recuperacion-nuevo",
  templateUrl: "./recuperacion-nuevo.component.html",
  styleUrls: ["./recuperacion-nuevo.component.css"],
})
export class RecuperacionNuevoComponent implements OnInit {
  page_title: string = "Recuperacion - Nuevo";
  status: string;

  usuarioPerfil: any;
  identity: any;

  /* Listas de Valores */
  claseBienes: string[] = [];
  tipos: string[] = [];
  ubicaciones: string[] = [];
  finalidades: string[] = [];
  viasProcesos: string[] = [];
  procesos: string[] = [];
  propiedadBienes: string[] = [];
  estadoBienes: string[] = [];
  textButton: string = "Crear";
  private isUpdate: boolean = false;
  private recuperacionId: string | number = "";

  constructor(
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private recuperacionesService: RecuperacionesService,
    private formBuilder: FormBuilder,
    private dialog: MatDialog,
    private route: ActivatedRoute
  ) {}

  form = this.formBuilder.group(
    {
      IdRecuperacion: [null],
      tipo: ["", [Validators.required]],
      estado: ["VAL", [Validators.required]],
      caso: ["", Validators.required],
      fecha_ingreso: [
        { value: new Date(), disabled: true },
        [Validators.required],
      ],
      fecha_cierre: [""],
      codmoneda: [""],
      idreclamo: ["", [Validators.required]],
      reclamo: [""],
      idpoliza: ["", [Validators.required]],
      numcert: ["", [Validators.required]],
      idcliente: ["", [Validators.required]],
      IdRecuperador: ["", [Validators.required]],
      recuperador: [""],
      idcomprador: ["", [Validators.required]],
      comprador: [""],
      indDescuento: [""],
      codigo_marca: [""],
      codigo_modelo: [""],
      cc: [""],
      anio: [""],
      placas: [""],
      color: [""],
      chasis: [""],
      clase_bien: [""],
      ViaProceso: [""],
      Proceso: ["", [Validators.required]],
      Propiedad: [""],
      Traspaso: [""],
      Ubicacion: [""],
      finalidad: [""],
      escritura: [""],
      fechaescritura: [""],
      convenio: [""],
      fechaconvenio: ["", [Validators.required]],
      montoestimado: ["", [Validators.required]],
      montototal: ["", [Validators.required]],
      cuotas: ["", [Validators.required]],
      observaciones: [""],
      idreclamotercero: [""],
      fechainicobro: [""],
      idclientefacturar: [""],
      facturar: [""],
    },
    { updateOn: "change" }
  );

  ngOnInit(): void {
    this.recuperacionesService
      .selectIDRecuperaciones()
      .pipe(
        tap((IdRecuperacion) => {
          this.form.patchValue({
            caso: IdRecuperacion + 1,
          });
        })
      )
      .subscribe();

    this.route.params.subscribe((param) => (this.recuperacionId = param.id));
    if (this.recuperacionId && this.recuperacionId !== "new") {
      this.ngxSpinnerService.show();
      this.recuperacionesService
        .getRecuperacionById(Number(this.recuperacionId))
        .pipe(
          tap({
            next: (recuperacion) => {
              this.cargarFormulario(recuperacion.data);
              this.ngxSpinnerService.hide();
              this.textButton = "Editar";
              this.isUpdate = true;
            },
          })
        )
        .subscribe();
    }

    this.getClaseBien();
    this.getTipos();
    this.getUbicaciones();
    this.getFinalidades();
    this.getViasProcesos();
    this.getProcesos();
    this.getPropiedadBien();
    this.getEstadoBienes();
  }

  openDialogoReclamos(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListReclamosComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: VwReclamos) => {
      if (result) {
        console.log(result);
        this.form.patchValue({
          idreclamo: result.idreclamo,
          reclamo: result.idreclamo,
          idpoliza: result.idpoliza,
          idcliente: result.idcliente,
          numcert: result.certificado,
          idreclamotercero: result.idreclamotercero,
          codigo_marca: result.MARCA,
          codigo_modelo: result.MODELO,
          placas: result.PLACA,
          chasis: result.CHASIS,
          color: result.COLOR,
          montototal: result.totalreservas,
        });
      }
    });
  }

  openDialogoRecuperador(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListClienteComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: Cliente) => {
      if (result) {
        console.log(result);
        this.form.patchValue({
          IdRecuperador: result.idcliente,
          recuperador: result.nombre,
        });
      }
    });
  }

  openDialogoComprador(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListClienteComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: Cliente) => {
      if (result) {
        console.log(result);
        this.form.patchValue({
          idcomprador: result.idcliente,
          comprador: result.nombre,
        });
      }
    });
  }

  openDialogoFacturar(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListClienteComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: Cliente) => {
      if (result) {
        console.log(result);
        this.form.patchValue({
          idclientefacturar: result.idcliente,
          facturar: result.nombre,
        });
      }
    });
  }

  getClaseBien() {
    this.recuperacionesService.getParametro("CLASEBIE").subscribe(
      (response) => {
        if (response.status == "success") {
          this.claseBienes = response.lval;
        } else {
          this.claseBienes = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getTipos() {
    this.recuperacionesService.getParametro("TIPOSALREC").subscribe(
      (response) => {
        if (response.status == "success") {
          this.tipos = response.lval;
        } else {
          this.tipos = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getUbicaciones() {
    this.recuperacionesService.getParametro("SLRUBICA").subscribe(
      (response) => {
        if (response.status == "success") {
          this.ubicaciones = response.lval;
        } else {
          this.ubicaciones = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getFinalidades() {
    this.recuperacionesService.getParametro("SLRFINAL").subscribe(
      (response) => {
        if (response.status == "success") {
          this.finalidades = response.lval;
        } else {
          this.finalidades = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getViasProcesos() {
    this.recuperacionesService.getParametro("VIAPROC").subscribe(
      (response) => {
        if (response.status == "success") {
          this.viasProcesos = response.lval;
        } else {
          this.viasProcesos = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getProcesos() {
    this.recuperacionesService.getParametro("SLRPROCE").subscribe(
      (response) => {
        if (response.status == "success") {
          this.procesos = response.lval;
        } else {
          this.procesos = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getPropiedadBien() {
    this.recuperacionesService.getParametro("PROPIEDA").subscribe(
      (response) => {
        if (response.status == "success") {
          this.propiedadBienes = response.lval;
        } else {
          this.propiedadBienes = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  getEstadoBienes() {
    this.recuperacionesService.getParametro("SLRESTAD").subscribe(
      (response) => {
        if (response.status == "success") {
          this.estadoBienes = response.lval;
        } else {
          this.estadoBienes = [];
        }
      },
      (error) => {
        console.log(<any>error);
      }
    );
  }

  onSaveForm(data: any) {
    if (this.isUpdate) {
      this.update(data);
    } else {
      this.save(data);
    }
  }

  private save(payload: any) {
    this.ngxSpinnerService.show();
    this.recuperacionesService.postRecuperacion(payload).subscribe(
      (response) => {
        Swal.fire(
          "Datos procesados",
          "La Recuperación se ha procesado correctamente",
          "success"
        );
        this.ngxSpinnerService.hide();
        this.close();
      },
      (error) => {
        console.log("error", error);
        this.ngxSpinnerService.hide();
        Swal.fire(
          "Error en la Recuperación",
          "No se ha procesado la información",
          "error"
        );
      }
    );
  }

  private update(payload: any) {
    this.ngxSpinnerService.show();
    this.recuperacionesService
      .updateRecuperacion(payload, this.recuperacionId.toString())
      .subscribe(
        (response) => {
          Swal.fire(
            "Datos procesados",
            "La Recuperación se ha editado correctamente",
            "success"
          );
          this.ngxSpinnerService.hide();
          this.close();
        },
        (error) => {
          console.log("error", error);
          this.ngxSpinnerService.hide();
          Swal.fire(
            "Error en la Recuperación",
            "No se ha procesado la información",
            "error"
          );
        }
      );
  }

  private cargarFormulario(recuperacion: any) {
    this.form.patchValue({
      IdRecuperacion: recuperacion.IdRecuperacion,
      tipo: recuperacion.tipo,
      estado: recuperacion.estado,
      caso: recuperacion.caso,
      fecha_ingreso: recuperacion.fecha_ingreso,
      fecha_cierre: recuperacion.fecha_cierre,
      codmoneda: recuperacion.codmoneda,
      idreclamo: recuperacion.idreclamo,
      reclamo: recuperacion.idreclamo,
      idpoliza: recuperacion.idpoliza,
      numcert: recuperacion.numcert,
      idcliente: recuperacion.idcliente,
      IdRecuperador: recuperacion.IdRecuperacion,
      recuperador: recuperacion.nombre_recuperador,
      idcomprador: recuperacion.idcomprador,
      comprador: recuperacion.nombre_comprador,
      indDescuento: recuperacion.indDescuento,
      codigo_marca: recuperacion.codigo_marca,
      codigo_modelo: recuperacion.codigo_modelo,
      cc: recuperacion.cc,
      anio: recuperacion.anio,
      placas: recuperacion.placas,
      color: recuperacion.color,
      chasis: recuperacion.chasis,
      clase_bien: recuperacion.clase_bien,
      ViaProceso: recuperacion.ViaProceso,
      Proceso: recuperacion.Proceso,
      Propiedad: recuperacion.Propiedad,
      Traspaso: recuperacion.Traspaso,
      Ubicacion: recuperacion.Ubicacion,
      finalidad: recuperacion.finalidad,
      escritura: recuperacion.escritura,
      fechaescritura: recuperacion.fechaescritura,
      convenio: recuperacion.convenio,
      fechaconvenio: recuperacion.fechaconvenio,
      montoestimado: recuperacion.montoestimado,
      montototal: recuperacion.montototal,
      cuotas: recuperacion.cuotas,
      observaciones: recuperacion.observaciones,
      idreclamotercero: recuperacion.idreclamotercero,
      fechainicobro: recuperacion.fechainicobro,
      idclientefacturar: recuperacion.idcliente,
      facturar: recuperacion.nombre_cliente,
      dRecuperacion: recuperacion.dRecuperacion,
    });
  }

  close() {
    this.router.navigate(["reclamos/recuperaciones"]);
  }
}
