export interface FacturaExterna {
  idfacturaexterna: number;
  fecha: string;
  persona: string;
  nit: string;
  facturano: string;
  tipo: string;
  cantidad: number;
  totalfactura: number;
  iva: number;
  neto: number;
  estado: string;
  idcliente?: number;
}

export interface FacturaExternaPayload {
  fecha: string;
  idcliente: number;
  facturano: string;
  tipo: string;
  cantidad: number;
  totalfactura: number;
}

export interface TipoFactura {
  codigo: string;
  descripcion: string;
}
