// Core variables and mixins
@import "../../bootstrap/functions";
@import "../../bootstrap/mixins";

// Core variables and mixins overrides
@import "../../core/variables/variables";
@import "../../bootstrap/variables";

@import "../../core/variables/components-variables";


#toast-container > div {
    opacity: 0.9;
    -ms-filter: progid:DXImageTransform.Microsoft.Alpha(Opacity=90);
    filter: alpha(opacity=90);
}

#toast-container > div:hover {
    -webkit-box-shadow: 2px 2px 10px 0px rgba(0,0,0,0.75);
    -moz-box-shadow: 2px 2px 10px 0px rgba(0,0,0,0.75);
    box-shadow: 2px 2px 10px 0px rgba(0,0,0,0.75);
}
.toast {
    background-color: $primary;
}
.toast-success {
    background-color: $success;
}
.toast-error {
    background-color: $danger;
}
.toast-info {
    background-color: $info;
}
.toast-warning {
    background-color: $warning;
}