import { Component, Inject } from "@angular/core";
import { TuiDialog } from "@taiga-ui/cdk";
import { TuiPdfViewerOptions } from "@taiga-ui/kit";
import { POLYMORPHEUS_CONTEXT } from "@tinkoff/ng-polymorpheus";

export type Buttons = ReadonlyArray<
  Readonly<{
    text: string;
    color: string;
    onClick(context: TuiDialog<TuiPdfViewerOptions<Buttons>, string>): void;
  }>
>;

@Component({
  template: `
    <button
      *ngFor="let button of context.data"
      tuiButton
      mat-flat-button
      [color]="button.color"
      (click)="button.onClick(context)"
      class="ml-2"
    >
      {{ button.text }}
    </button>
  `,
})
export class ActionsButtons {
  constructor(
    @Inject(POLYMORPHEUS_CONTEXT)
    readonly context: TuiDialog<TuiPdfViewerOptions<Buttons>, string>
  ) {}
}
