import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { ILval, IResponseData2 } from "../../../interface";
import { EntidadFinanciera } from "../interfaces/entidad-financiera";

@Injectable({
  providedIn: "root",
})
export class EntidadFinancieraService {
  private url = environment.apiUrl;
  constructor(private http: HttpClient) {}

  getEntidadFinanciera(): Observable<EntidadFinanciera> {
    return this.http
      .get(`${this.url}/entidad-financiera`)
      .pipe(map((res: Partial<IResponseData2<EntidadFinanciera>>) => res.data));
  }

  getEntidadFinancieraLval(): Observable<ILval> {
    return this.http
      .post(`${this.url}/lval/catalogo/TENTFINA`, {})
      .pipe(map((res: any) => res.lval));
  }

  createEntidad(body: EntidadFinanciera) {
    return this.http.post(`${this.url}/entidad-financiera`, body);
  }

  updateEntidad(body: EntidadFinanciera, id: string) {
    return this.http.put(`${this.url}/entidad-financiera/${id}`, body);
  }
}
