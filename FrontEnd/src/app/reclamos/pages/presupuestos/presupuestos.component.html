<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>
        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="mat-elevation-z8"
            perfectScrollbar
          >
            <!-- idpresupuesto Column -->
            <ng-container matColumnDef="idpresupuesto">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                ID Presupuesto
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.Idpresupuesto }}
              </td>
            </ng-container>

            <!-- estado Column -->
            <ng-container matColumnDef="estado">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
              <td mat-cell *matCellDef="let element">{{ element.Estado }}</td>
            </ng-container>

            <!-- fecha Column -->
            <ng-container matColumnDef="fecha">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Fecha</th>
              <td mat-cell *matCellDef="let element">
                {{ element.fecha }}
              </td>
            </ng-container>

            <!-- tipo Column -->
            <ng-container matColumnDef="tipo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo</th>
              <td mat-cell *matCellDef="let element">{{ element.TIpo }}</td>
            </ng-container>

            <!-- poliza Column -->
            <ng-container matColumnDef="poliza">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Poliza</th>
              <td mat-cell *matCellDef="let element">
                {{ element.poliza_aseguradora }}
              </td>
            </ng-container>

            <!-- numcert Column -->
            <ng-container matColumnDef="numcert">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Certificado
              </th>
              <td mat-cell *matCellDef="let element">{{ element.numcert }}</td>
            </ng-container>

            <ng-container matColumnDef="idcotizacion">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                ID cotizacion
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.idcotizacion || "Sin cotizacion" }}
              </td>
            </ng-container>

            idcotizacion

            <!-- monto_deducible Column -->
            <ng-container matColumnDef="monto_deducible">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Monto Deducible
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.monto_deducible }}
              </td>
            </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
              <td mat-cell *matCellDef="let element">
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onActive(element)"
                  [disabled]="
                    element.Estado === 'ACT' || element.Estado === 'CER'
                  "
                  matTooltip="Activar"
                  class="mr-1"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>check</mat-icon>
                </button>

                <button
                  mat-flat-button
                  color="accent"
                  (click)="onEdit(element)"
                  class="mr-1"
                  matTooltip="Editar"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  mat-flat-button
                  color="accent"
                  class="mr-1"
                  (click)="onDetail(element)"
                  matTooltip="Detalle"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>subject</mat-icon>
                </button>

                <button
                  mat-icon-button
                  [matMenuTriggerFor]="moreOptions"
                  aria-label="Example icon-button with a menu"
                >
                  <mat-icon>more_vert</mat-icon>
                </button>

                <mat-menu #moreOptions="matMenu">
                  <button
                    mat-menu-item
                    color="accent"
                    (click)="onActive(element)"
                    matTooltip="Autorizar"
                    matTooltipClass="tooltip-red"
                  >
                    <mat-icon>lock_open</mat-icon>
                    <span>Autorizar</span>
                  </button>
                  <button
                    mat-menu-item
                    color="primary"
                    (click)="onNotificacion(element.Idpresupuesto)"
                    matTooltip="Notificaciones"
                    matTooltipClass="tooltip-red"
                  >
                    <mat-icon>description</mat-icon>
                    <span>Notificar</span>
                  </button>
                  <button
                    mat-menu-item
                    color="primary"
                    matTooltip="Crear"
                    (click)="
                      cotizacionPresupuesto(
                        element.Idpresupuesto,
                        element.idcotizacion
                      )
                    "
                  >
                    <mat-icon>article</mat-icon>
                    <span>Crear/Actulizar Cotizacion</span>
                  </button>
                  <button
                    mat-menu-item
                    color="primary"
                    matTooltip="Cotizacion"
                    [disabled]="!element.idcotizacion"
                    (click)="viewCotizacion(element.idcotizacion)"
                  >
                    <mat-icon>visibility</mat-icon>
                    <span>Ver Cotizacion</span>
                  </button>
                </mat-menu>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
