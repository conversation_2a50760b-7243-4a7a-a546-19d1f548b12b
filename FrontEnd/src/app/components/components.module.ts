import { NgModule } from "@angular/core";
import { CommonModule, DatePipe } from "@angular/common";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { FilterPersonaComponent } from "./filters/persona/filter-persona.component";
import { MaterialModule } from "../material.module";
import { OutsideClickDirective } from "../_directives/outside-click.directive";
import { FilterCobradorComponent } from "./filters/cobrador/filter-cobrador.component";
import { FilterEjecutivoComponent } from "./filters/ejecutivo/filter-ejecutivo.component";
import { IcoreTableComponent } from "./icore-table/icore-table.component";
import { DynamicPipe } from "./icore-table/pipes/dynamic.pipe";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";
import { AsignacionesComponent } from "./asignaciones/asignaciones.component";
import { IntermediariosComponent } from "./filters/intermediarios/intermediarios.component";
import { TalonarioRequerimientoComponent } from "./talonario-requerimiento/talonario-requerimiento.component";
import { RequerimientoPagoComponent } from "./requerimiento-pago/requerimiento-pago.component";
import { RubroRequerimientoComponent } from "./rubro-requerimiento/rubro-requerimiento.component";
import { AlertSelectComponent } from "./alert-select/alert-select.component";
import { ErrorSnackbarComponent } from "./error-snackbar/error-snackbar.component";
import { ErrorLogsComponent } from "./error-logs/error-logs.component";
import { BulkLoadComponent } from "./bulk-load/bulk-load.component";
import { ProgressComponent } from "./progress/progress.component";
import { ButtonBackComponent } from "./buttons/button-back.component";
import { FacturaComponent } from "./facturas/factura.component";
import { MaintenanceComponent } from "./maintenance/maintenance.component";
import { ListClienteComponent } from "./list-cliente/list-cliente.component";
import { EndososComponent } from "./endosos/endosos.component";
import { ListClientePolizaComponent } from "../list-cliente-poliza/list-cliente-poliza.component";
import { SelectsComponent } from "./selects/selects.component";
import { OperacionAccesoComponent } from "./operacion-acceso/operacion-acceso.component";
import { EmailComponent } from "./email/email.component";
import { ListUsuarioComponent } from "./list-usuario/list-usuario.component";

@NgModule({
  declarations: [
    FilterPersonaComponent,
    OutsideClickDirective,
    FilterCobradorComponent,
    FacturaComponent,
    FilterEjecutivoComponent,
    IcoreTableComponent,
    DynamicPipe,
    AsignacionesComponent,
    IntermediariosComponent,
    TalonarioRequerimientoComponent,
    RequerimientoPagoComponent,
    RubroRequerimientoComponent,
    AlertSelectComponent,
    ErrorSnackbarComponent,
    ErrorLogsComponent,
    BulkLoadComponent,
    ButtonBackComponent,
    ProgressComponent,
    MaintenanceComponent,
    ListClienteComponent,
    ListClientePolizaComponent,
    EndososComponent,
    SelectsComponent,
    OperacionAccesoComponent,
    EmailComponent,
    ListUsuarioComponent,

  ],
  exports: [
    FilterPersonaComponent,
    FilterCobradorComponent,
    FilterEjecutivoComponent,
    FacturaComponent,
    EndososComponent,
    IcoreTableComponent,
    TalonarioRequerimientoComponent,
    MaintenanceComponent,
    ButtonBackComponent,
    AlertSelectComponent,
    ListClienteComponent,
    ListClientePolizaComponent,
    SelectsComponent,
    OperacionAccesoComponent,
    EmailComponent,
    ListUsuarioComponent
  ],
  imports: [
    CommonModule,
    MaterialModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    PerfectScrollbarModule,
  ],
  providers: [DatePipe],
})
export class ComponentsModule {}
