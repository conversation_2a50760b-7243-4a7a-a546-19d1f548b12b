<div class="page_title">
  <h1>{{ page_title }}</h1>
</div>
<hr />
<div class="mat-elevation-z3">
  <table mat-table [dataSource]="dataSource">
    <!-- Position Column -->
    <ng-container matColumnDef="descripcion_repuesto">
      <th mat-header-cell *matHeaderCellDef>REPUESTO</th>
      <td mat-cell *matCellDef="let element">
        {{ element.descripcion_repuesto }}
      </td>
    </ng-container>

    <!-- Name Column -->
    <ng-container matColumnDef="observaciones">
      <th mat-header-cell *matHeaderCellDef>OBSERVACIONES</th>
      <td mat-cell *matCellDef="let element">{{ element.observaciones }}</td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>

  <mat-paginator
    [pageSizeOptions]="[5, 10, 20]"
    showFirstLastButtons
  ></mat-paginator>
</div>

<mat-grid-list cols="2" rowHeight="80px" gutterSize="1px">
  <mat-grid-tile>
    <mat-dialog-actions>
      <button mat-flat-button color="primary" (click)="close()">
        <mat-icon>highlight_off</mat-icon>Cerrar
      </button>
    </mat-dialog-actions>
  </mat-grid-tile>
</mat-grid-list>
