<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="page_title text-center">
        <h1>{{ pageTitle }}</h1>
      </div>
      <hr />

      <div class="container-btn-new my-4">
        <div class="btn-new">
          <button
            mat-flat-button
            color="primary"
            matTooltip="Nueva Entidad"
            (click)="newEntidad()"
          >
            <mat-icon>add</mat-icon>
          </button>
        </div>
      </div>

      <icore-table
        *ngIf="data$ | async as entidades; else loading"
        [columns]="displayedColumns"
        [data]="entidades"
        [filter]="true"
      ></icore-table>
    </div>

    <ng-template #loading>
      <ngx-skeleton-loader [count]="4" appearance="line"></ngx-skeleton-loader>
    </ng-template>
  </div>
</div>
