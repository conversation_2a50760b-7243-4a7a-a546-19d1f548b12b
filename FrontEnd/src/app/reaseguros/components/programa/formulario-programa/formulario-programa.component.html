<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8 py-2">
        <button-back class="ml-2"></button-back>
        <div class="page_title text-center">
          <h1>{{ id === "new" ? "Nuevo" : "Editar" }} programa</h1>
        </div>
        <form [formGroup]="formularioPrograma" class="p-4">
          <div class="row">
            <mat-form-field class="col" appearance="outline">
              <mat-label>Nombre</mat-label>
              <input
                matInput
                formControlName="nombre"
                name="nombre"
                required
                sanitizeUppercase
              />
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Descripcion</mat-label>
              <input
                matInput
                formControlName="descripcion"
                name="descripcion"
                required
                sanitizeUppercase
              />
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Estado</mat-label>
              <mat-select formControlName="estado" name="estado">
                <mat-option
                  *ngFor="let estado of estados"
                  [value]="estado.value"
                  >{{ estado.label }}</mat-option
                >
              </mat-select>
            </mat-form-field>
          </div>
          <div class="row">
            <mat-form-field class="col" appearance="outline">
              <mat-label>Vigencia de inicio</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="vigencia_inicio"
                name="vigencia_inicio"
                required
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Vigencia de fin</mat-label>
              <input
                matInput
                [matDatepicker]="picker2"
                formControlName="vigencia_fin"
                name="vigencia_fin"
                required
              />
              <mat-datepicker-toggle
                matSuffix
                [for]="picker2"
              ></mat-datepicker-toggle>
              <mat-datepicker #picker2></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="d-flex justify-content-end m-2">
            <button mat-flat-button color="accent" (click)="saveForm()">
              <mat-icon>save</mat-icon>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
