<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ pageTitle }}</h1>
        </div>
        <mat-divider></mat-divider>
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button
              mat-flat-button
              color="primary"
              class="mr-1"
              matTooltip="Nuevo"
              [routerLink]="['/alertas-avisos', 'nuevo']"
            >
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>
        
      <div class="table-responsive" perfectScrollbar>
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          *ngIf="dataSource?.data.length > 0 && isLoadingResults; else Skeleton"
        >
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>#</th>
            <td mat-cell *matCellDef="let element">
              {{ element.id }}
            </td>
          </ng-container>

          <ng-container matColumnDef="tipo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo</th>
            <td mat-cell *matCellDef="let element">
              {{ element.tipo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="modulo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Modulo</th>
            <td mat-cell *matCellDef="let element">
              {{ element.modulo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="evento">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Evento</th>
            <td mat-cell *matCellDef="let element">
              {{ element.evento }}
            </td>
          </ng-container>

          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
            <td mat-cell *matCellDef="let element">
              <mat-chip-list>
                <mat-chip
                  selected
                  [color]="colorState[element.estado]"
                >
                  {{ element.estado }}
                </mat-chip>
              </mat-chip-list>
            </td>
          </ng-container>

          <ng-container matColumnDef="envia_correo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Envia Correo</th>
            <td mat-cell *matCellDef="let element">
              {{ element.envia_correo ? 'S': 'N' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="correos">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Correos
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.correos }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-flat-button
                color="accent"
                matTooltip="Editar"
                class="mr-1"
                [routerLink]="['/alertas-avisos', element.id]"
              >
                <mat-icon>edit</mat-icon>
              </button>
              <button
                mat-flat-button
                [color]="element.estado !== 'ACT' ? 'accent' : 'none'"
                matTooltip="Activar"
                [class]="element.estado === 'ACT' ? 'bg-danger' : ''"
                class="mr-1"
                [disabled]="element.estado === 'INA'"
                (click)="changeStatus(element.id, element.estado)"
              >
              <mat-icon>{{ element.estado === 'ACT' ? 'close' : 'done' }}</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr
            mat-row
            *matRowDef="let row; columns: displayedColumns"
          ></tr>
        </table>

        <ng-template #Skeleton>
          <div class="mx-2">
            <ngx-skeleton-loader
              [count]="5"
              appearance="line"
            ></ngx-skeleton-loader>
          </div>
        </ng-template>
      </div>
      <mat-paginator
        [pageSizeOptions]="[5, 10, 20]"
        showFirstLastButtons
      ></mat-paginator>

      </div>
    </div>
  </div>
</div>
