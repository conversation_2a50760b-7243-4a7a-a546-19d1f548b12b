import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, map } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { environment } from '../../../environments/environment';
import { ITipoISR } from 'src/app/interface/tipoISR.interface';
import { IResponseData, IResponseData2, Pagination } from '../../interface';

@Injectable({
  providedIn: 'root',
})
export class TipoIsrService {
  private readonly urlBase = environment.apiUrlv1;

  constructor(private readonly http: HttpClient) {}

  /**
   * Consulta todos los tipos de ISR con paginación y búsqueda
   */
  getTipoISR(
    query?: {
      param: string[];
      value: string;
    },
    pagination?: Pagination,
    required = false
  ): Observable<{ data: ITipoISR[]; total: number }> {
    let params = new HttpParams();

    if (required) {
      params = params.append('required', 'true');
    }

    if (pagination) {
      params = params.append('page', pagination.currentPage);
      params = params.append('size', pagination.pageSize);
    }

    if (query) {
      params = params.append('filter', query.param.join(','));
      params = params.append('contains', query.value);
    }

    return this.http
      .get(`${this.urlBase}/tipo-isr`, { params })
      .pipe(
        map((res: Partial<IResponseData<ITipoISR>>) => {
          const data = res.data || [];
          return {
            data,
            total: res.meta?.total ?? data.length,
          };
        }),
        catchError((e) => {
          of([]);
          throw e.status;
        })
      );
  }

  /**
   * Obtener tipo de ISR por ID, opcional para futuro
   */
  getTipoISRById(id: number): Observable<ITipoISR> {
    return this.http
      .get(`${this.urlBase}/tipo-isr/${id}`)
      .pipe(map((res: Partial<IResponseData2<ITipoISR>>) => res.data));
  }

  /**
   * Crear un nuevo tipo de ISR
   */
  createTipoISR(data: Partial<ITipoISR>): Observable<{ success: boolean; message: string }> {
    return this.http.post<{ success: boolean; message: string }>(`${this.urlBase}/tipo-isr`, data);
  }

  /**
   * Actualizar tipo de ISR existente
   */
  updateTipoISR(id: number, data: Partial<ITipoISR>): Observable<{ success: boolean; message: string }> {
    return this.http.put<{ success: boolean; message: string }>(`${this.urlBase}/tipo-isr/${id}`, data);
  }

  /**
   * Activar tipo de ISR por ID
   */
  activarTipoISR(id: number): Observable<any> {
    return this.http.put(`${this.urlBase}/tipo-isr/${id}/activar`, {});
  }
  
    /**
   * Eliminar tipo de ISR por ID (inactivar)
   */
  inactivarTipoISR(id: number): Observable<any> {
    return this.http.delete(`${this.urlBase}/tipo-isr/${id}`);
  }
  
}
