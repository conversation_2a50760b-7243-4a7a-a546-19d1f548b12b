<!--<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ pageTitle }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div *ngIf="dataSource as data; else loading">
          <icore-table
            [columns]="displayedColumns"
            [data]="data.data"
            [length]="data.length"
            (onNotData)="onNew()"
            [valueToSearch]="valueToSearch"
          >
            <h1>No hay cotizaciones</h1>
          </icore-table>
        </div>

        <ng-template #loading>
          <div class="mx-2">
            <ngx-skeleton-loader
              [count]="displayedColumns.length"
              appearance="line"
            ></ngx-skeleton-loader>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>-->
<app-list-template
  [title]="'Clientes'"
  [displayedColumns]="displayedColumns"
  [service]="_clienteService"
  [onNew]="onNew"
  [hasPagination]="true"
  (reload)="rechargeFunction = $event"
>
</app-list-template>
