<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body" style="zoom: 80%">
      <!-- Basic form layout section start -->
      <section id="horizontal-form-layouts">
        <div class="row">
          <div class="col-md-0.7" style="width: 50px; padding-right: 0px">
            <!--<ng-container>
              <div
                class="form-actions"
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: flex-start;
                  width: 75px;
                "
              >
                <button
                  mat-flat-button
                  type="button"
                  class="btn mr-1"
                  color="primary"
                  style="margin-top: 15px"
                  matTooltip="Incluir"
                >
                  <i class="feather ft-check-circle"></i>
                </button>
                <button
                  mat-flat-button
                  type="submit"
                  class="bg-danger mr-1"
                  color="accent"
                  style="margin-top: 15px"
                  matTooltip="Excluir"
                  (click)="anularCertificado()"
                >
                  <i class="feather ft-x-circle"></i>
                </button>
                <button
                  mat-flat-button
                  type="submit"
                  class="btn mr-1"
                  color="accent"
                  style="margin-top: 15px"
                  matTooltip="Rehabilitar"
                >
                  <i class="feather ft-rotate-ccw"></i>
                </button>
                <button
                  mat-flat-button
                  type="submit"
                  class="btn btn-primary"
                  style="margin-top: 15px; display: none"
                  matTooltip="Renovar"
                >
                  <i class="feather ft-repeat"></i>
                </button>
              </div>
            </ng-container>-->
          </div>
          <div class="col-md-11 ml-2">
            <button
              mat-flat-button
              color="primary"
              type="button"
              class="mb-2"
              (click)="back()"
              style="margin-right: 10px"
            >
              <i class="feather ft-arrow-left" style="font-size: 24px"> </i>
            </button>

            <m-card [options]="options">
              <ng-container mCardBody>
                <div *ngIf="certificados$ | async as certificado; else loading">
                  <div class="form form-horizontal">
                    <div class="form-body">
                      <h4 class="form-section">
                        <i class="feather ft-clipboard"></i> Datos de la póliza
                        / Certificado
                      </h4>
                      <ng-container mCardHeaderTitle>
                        <div class="row">
                          <label
                            class="col-md-2 label-control"
                            style="border-top-width: 2px"
                            >POLIZA:
                          </label>

                          <label
                            class="col-md-2 label-control"
                            style="border-top-width: 2px"
                            >NUMERO:
                          </label>
                          <label
                            class="col-md-2 label-control"
                            style="border-top-width: 2px"
                            >ESTADO:
                          </label>
                        </div>
                      </ng-container>
                      <div class="row">
                        <mat-form-field class="col-md-2" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.producto"
                            placeholder="Poliza"
                          />
                        </mat-form-field>
                        <mat-form-field class="col-md-2" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.polizaAseguradora"
                            placeholder="Numero Poliza"
                          />
                        </mat-form-field>
                        <mat-form-field class="col-md-2" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.stsPol"
                            placeholder="Estado"
                          />
                        </mat-form-field>
                      </div>

                      <div class="row">
                        <label
                          class="col-md-4 label-control"
                          style="border-top-width: 2px"
                          >INTERMEDIARIO:
                        </label>
                        <label
                          class="col-md-4 label-control"
                          style="border-top-width: 2px"
                        >
                          CODIGO INTERMEDIARIO:
                        </label>
                        <label
                          class="col-md-4 label-control"
                          style="border-top-width: 2px"
                        >
                          CANAL INTERMEDIARIO:
                        </label>
                      </div>

                      <div class="row">
                        <mat-form-field class="col" appearance="outline">
                          <textarea
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.intermediario"
                            placeholder="Estado"
                          ></textarea>
                        </mat-form-field>
                        <mat-form-field class="col" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.codigoIntermediario"
                            placeholder="CODIGO INTER"
                          />
                        </mat-form-field>
                        <mat-form-field class="col" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.canalIntermediario"
                            placeholder="CODIGO INTER"
                          />
                        </mat-form-field>
                      </div>

                      <div class="row">
                        <label
                          class="col label-control"
                          style="border-top-width: 2px"
                          >CONTRATANTE:
                        </label>
                        <label
                          class="col label-control"
                          style="border-top-width: 2px"
                          >COBRADOR:
                        </label>
                      </div>
                      <div class="row">
                        <mat-form-field class="col" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="certificado.cliente"
                            placeholder="CLIENTE"
                          />
                        </mat-form-field>
                        <mat-form-field class="col" appearance="outline">
                          <input
                            readonly
                            type="text"
                            matInput
                            [value]="poliza.cobrador"
                            placeholder="COBRADOR"
                          />
                        </mat-form-field>
                      </div>
                    </div>
                  </div>

                  <div class="page_title my-2">
                    <h4>Listado de Certificados</h4>
                  </div>
                  <div class="d-flex justify-content-between">
                    <button
                      mat-flat-button
                      color="primary"
                      matTooltip="Recargar certificados"
                      (click)="reloadCertificados()"
                    >
                      <mat-icon>refresh</mat-icon>
                    </button>

                    <button
                      mat-flat-button
                      color="primary"
                      matTooltip="Incluir Certificado"
                      (click)="incluirCertificado()"
                    >
                      <mat-icon>add</mat-icon>
                    </button>
                  </div>
                  <icore-table
                    [columns]="displayedColumns"
                    [data]="certificado.certificados"
                    (onClick)="detalleCertificado($event)"
                    [filter]="true"
                    class="cursor-pointer table-hover"
                  ></icore-table>
                </div>
                <ng-template #loading>
                  <span>loading...</span>
                </ng-template>
              </ng-container>
            </m-card>
          </div>
        </div>

        <div *ngIf="certificadosDetalle$ | async as detalle; else noData">
          <app-datos-asegurado
            [asegurado]="detalle.datosAsegurado"
            *ngIf="particulares"
          ></app-datos-asegurado>
          <app-datos-particulares
            [particulares]="detalle.particulares"
            *ngIf="particulares"
          >
            <!--<mat-icon>add</mat-icon>-->

            <!--Coberturas button-->
            <ng-component *ngTemplateOutlet="buttons"></ng-component>
          </app-datos-particulares>

          <app-coberturas
            *ngIf="coberturas"
            [dataCoberturas]="detalle.coberturas"
            [moneda]="detalle.moneda"
          >
            <ng-component *ngTemplateOutlet="buttons"></ng-component>
          </app-coberturas>

          <app-recargos-descuentos-poliza
            [dataRecargos]="detalle.recargosDescuentos"
            *ngIf="recargos"
          >
            <ng-component *ngTemplateOutlet="buttons"></ng-component>
          </app-recargos-descuentos-poliza>
          <app-beneficiarios *ngIf="beneficiarios">
            <ng-component *ngTemplateOutlet="buttons"></ng-component>
          </app-beneficiarios>

          <app-asegurado-adicional *ngIf="asegAdicional">
            <ng-component *ngTemplateOutlet="buttons"></ng-component>
          </app-asegurado-adicional>
        </div>

        <ng-template #noData>
          <div class="content-no-data">
            <span class="no-data">Selecciona un certificado </span>
          </div>
        </ng-template>
      </section>
    </div>
  </div>
</div>

<ng-template #buttons>
  <button
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="coberturas"
    [disabled]="coberturas"
    (click)="changeCoberturas()"
  >
    Coberturas
    <mat-icon>article</mat-icon>
  </button>

  <!--Particulares button-->
  <button
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="particulares"
    [disabled]="particulares"
    (click)="changeParticulares()"
  >
    Particulares
    <mat-icon>people</mat-icon>
  </button>

  <!--Recargos button-->
  <button
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="recargos"
    [disabled]="recargos"
    (click)="changeRecargos()"
  >
    Recargos
    <mat-icon>percent</mat-icon>
  </button>

  <!--Boton Aseg Adicional-->
  <button
    *ngIf="isVida"
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Asegurado Adicional"
    [disabled]="asegAdicional"
    (click)="changeAseguradoAdicional()"
  >
    Aseg. Adicional
    <mat-icon>people</mat-icon>
  </button>

  <!--Boton Beneficiarios-->
  <button
    *ngIf="isVida"
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Beneficiarios"
    [disabled]="beneficiarios"
    (click)="changeBeneficiarios()"
  >
    Beneficiarios
    <mat-icon>people</mat-icon>
  </button>

  <!--Boton Vida-->
  <button
    *ngIf="showButton"
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="vida"
    (click)="movFondo()"
  >
    Mov. Fondo
  </button>

  <!--Boton de Anular Certificado-->
  <button
    *ngIf="detalleCert.stsCert === 'ACT' && noCert > 1"
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Anular Certificado"
    (click)="anularCertificado()"
  >
    Anular Certificado
    <mat-icon>delete</mat-icon>
  </button>

  <button
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Cambiar vehiculo"
    (click)="changeAuto()"
  >
    Cambiar Vehiculo
    <mat-icon>edit</mat-icon>
  </button>

  <!--Boton de anular poliza-->
  <button
    *ngIf="detalleCert.stsCert === 'ACT' && noCert === 1"
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Anular Poliza"
    (click)="anular()"
  >
    Anular Poliza
    <mat-icon>delete</mat-icon>
  </button>

  <!--Boton cambio de cobrador-->
  <button
    mat-flat-button
    color="accent"
    class="m-1"
    matTooltip="Cambiar Cobrador"
    (click)="cambiarCobrador()"
  >
    Cambiar cobrador
    <mat-icon>edit</mat-icon>
  </button>
</ng-template>
