import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { global } from "../_services/global";

@Injectable()
export class ClientePolizaService {
    
    public url: string;
    
    constructor(
        private _http: HttpClient
    ) {
        this.url = global.url;
    }
   
    getClientePoliza(): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.get(this.url+'clientePoliza', {headers: headers});
    }
  
    
    

}