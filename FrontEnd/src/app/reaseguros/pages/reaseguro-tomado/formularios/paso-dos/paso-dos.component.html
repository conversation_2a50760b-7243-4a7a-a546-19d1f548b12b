<div *ngIf="fields.length > 0; else Skeleton">
  <div class="form-container">
    <div class="section-buttons-column">
      <button
        mat-raised-button
        color="primary"
        (click)="selectSection('SI')"
        [class.active]="currentSection === 'SI'"
      >
        Sección 1
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="selectSection('SII')"
        [class.active]="currentSection === 'SII'"
      >
        Sección 2
      </button>
      <button
        mat-raised-button
        color="primary"
        (click)="selectSection('SIII')"
        [class.active]="currentSection === 'SIII'"
      >
        Sección 3
      </button>
    </div>
    <div class="form-content">
      <form [formGroup]="form">
        <div class="container">
          <formly-form
            [form]="form"
            [fields]="fields"
            [model]="model"
            [options]="options"
          ></formly-form>
        </div>
        <div class="form-group text-center space-20 mt-2">
          <button mat-button matStepperPrevious (click)="back()">
            <span style="margin-right: 10px">
              <i class="feather ft-chevron-left"></i>
            </span>
            Regresar
          </button>

          <button
            mat-button
            (click)="addValueInputs()"
            [disabled]="modeloAntiguo || !form.valid"
          >
            Siguiente
            <span style="margin-left: 10px">
              <i class="feather ft-chevron-right"></i>
            </span>
          </button>
        </div>
      </form>

      <app-recargos-descuentos></app-recargos-descuentos>
    </div>
  </div>
</div>

<ng-template #Skeleton>
  <ngx-skeleton-loader count="8" appearance="line"></ngx-skeleton-loader>
</ng-template>
