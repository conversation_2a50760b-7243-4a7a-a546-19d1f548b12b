<div class="mat-elevation-z8">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr />
  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">         

    <!-- Id Column -->
    <ng-container matColumnDef="idcotizacion">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Id </th>
      <td mat-cell *matCellDef="let element"> {{element.idcotizacion}} </td>
    </ng-container>

    <!-- codigo Column -->
    <ng-container matColumnDef="codigo">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Código </th>
      <td mat-cell *matCellDef="let element"> {{element.codigo}} </td>
    </ng-container>     

    <!-- fecha_cotizacion Column -->
    <ng-container matColumnDef="fecha_cotizacion">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha </th>
      <td mat-cell *matCellDef="let element"> {{element.fecha_cotizacion}} </td>
    </ng-container>             

    <!-- usuario Column -->
    <ng-container matColumnDef="usuario">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Usuario </th>
      <td mat-cell *matCellDef="let element"> {{element.usuario}} </td>
    </ng-container> 

    <!-- montototal Column -->
    <ng-container matColumnDef="montototal">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto Total </th>
      <td mat-cell *matCellDef="let element"> {{element.montototal}} </td>
    </ng-container> 

    <!-- cantidad_repuestos Column -->
    <ng-container matColumnDef="cantidad_repuestos">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Cant. Repuestos </th>
      <td mat-cell *matCellDef="let element"> {{element.cantidad_repuestos}} </td>
    </ng-container> 

    <!-- estado Column -->
    <ng-container matColumnDef="estado">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
      <td mat-cell *matCellDef="let element"> {{element.estado}} </td>
    </ng-container> 

    <!-- cantidad_ofertantes Column -->
    <ng-container matColumnDef="cantidad_ofertantes">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Cant. Ofertantes </th>
      <td mat-cell *matCellDef="let element"> {{element.cantidad_ofertantes}} </td>
    </ng-container> 

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>

