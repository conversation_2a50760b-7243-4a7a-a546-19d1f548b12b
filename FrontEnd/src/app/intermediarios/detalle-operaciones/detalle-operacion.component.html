<h2 mat-dialog-title class="text-center mb-2">
    {{ data.title }}
  </h2>
  
  <mat-dialog-content class="p-3">
  
    <!-- Info operación -->
    <div class="mb-4">
      <h5 class="mb-3 text-primary border-bottom pb-1">Información de la Operación</h5>
      <div class="row g-2">
        <div class="col-md-6">
          <strong>Tipo de Operación:</strong> {{ data.element.operacion.tipooperinter }}
        </div>
        <div class="col-md-6">
          <strong>Fecha:</strong> {{ data.element.operacion.foperinter | date: 'dd-MM-yyyy' }}
        </div>
        <div class="col-md-6">
          <strong>Monto:</strong> {{ data.element.operacion.montoopermoneda }}
        </div>
        <div class="col-md-6">
          <strong>Estado:</strong> 
          <span [ngClass]="{
            'text-success': data.element.operacion.stsoperinter === 'ACT',
            'text-danger': data.element.operacion.stsoperinter === 'ANU',
            'text-warning': data.element.operacion.stsoperinter === 'SUS'
          }">
            {{ data.element.operacion.stsoperinter }}
          </span>
        </div>
      </div>
    </div>
  
    <hr />
  
    <!-- Tabla de cuotas -->
    <div>
      <h5 class="mb-3 text-primary border-bottom pb-1">Cuotas Generadas</h5>
      <div class="table-responsive">
        <table class="table table-hover table-bordered table-sm text-center align-middle">
          <thead class="table-light">
            <tr>
              <th>#</th>
              <th>Fecha Pago</th>
              <th>Monto</th>
              <th>Requerimiento</th>
              <th>Estado</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let cuota of data.element.detalle">
              <td>{{ cuota.numerocuota }}</td>
              <td>{{ cuota.fechapago | date: 'dd-MM-yyyy' }}</td>
              <td>{{ cuota.montopago }}</td>
              <td>{{ cuota.requerimiento }}</td>
              <td>{{ cuota.estado }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  
  </mat-dialog-content>
  