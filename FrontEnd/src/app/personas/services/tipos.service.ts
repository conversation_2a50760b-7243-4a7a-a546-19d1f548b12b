import { Injectable } from "@angular/core";
import { HttpClient } from "@angular/common/http";

import { Observable, of } from "rxjs";
import { map, catchError } from "rxjs/operators";

import { environment } from "../../../environments/environment";
import { ITipo, IResponseData, IEstadocivil } from "../../interface";

@Injectable({
  providedIn: "root",
})
export class TiposService {
  private readonly urlBase: string = environment.apiUrl;

  constructor(private readonly http: HttpClient) {}

  getTipoDocumentos(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipodoc`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      }),
      catchError(() => of([]))
    );
  }

  getTipoRegistro(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipopersonas`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      }),
      catchError(() => of([]))
    );
  }

  getTipoTelefonos(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipotelefono`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      })
    );
  }

  getTipoDirecciones(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipodireccion`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      })
    );
  }

  getTipoCorreo(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipocorreo`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      })
    );
  }

  getEstadoCivil(): Observable<IEstadocivil[]> {
    return this.http.get(`${this.urlBase}/estadocivil`).pipe(
      map((res: Partial<IResponseData<IEstadocivil>>) => {
        const data = res.data || [];
        return data;
      })
    );
  }

  getTipoCobrador(): Observable<ITipo[]> {
    return this.http.get(`${this.urlBase}/tipocobrador`).pipe(
      map((res: Partial<IResponseData<ITipo>>) => {
        const data = res.data || [];
        return data;
      })
    );
  }
}
