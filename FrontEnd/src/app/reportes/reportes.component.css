table {
    width: 100%;
}

table td {
    cursor: pointer;
}

.row-is-clicked {
    background-color: rgba(0, 0, 0, 0.1);
}

.page_title {
    margin-top: 1rem !important;
    margin-left: 1rem;
}

.mat-form-field {
    font-size: 1rem;
    width: 98%;
    margin: 0 auto;
    display: block;
    margin-top: 0rem;
}

.container-btn-new {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin: 1rem -1rem;
    bottom: 100px !important;
}

.btn-new {
    cursor: pointer;
    margin-bottom: 10px;
}

.btn-new button:hover {
    background-color: #164ea1;
}

.ml {
    margin-left: 21.5rem;
}

.mat-checkbox {
    margin-right: 10px;
    margin-top: 5px;
}

@media only screen and (max-width: 1100px) {
    .mat-focus-indicator {
        display: flex;
        margin-top: 4px;
        justify-content: space-between;
    }

    .ml {
        margin-left: 3.5rem !important;
    }
}