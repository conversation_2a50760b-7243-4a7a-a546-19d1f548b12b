import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";
import {
  IRubrosContrato,
  IRubrosContratoResponse,
} from "../interfaces/rubros-contrato.interface";

@Injectable({
  providedIn: "root",
})
export class RubrosContratoService {
  private readonly urlBase = environment.apiUrlv1 + "/reaseguro";

  constructor(private readonly http: HttpClient) {}

  getData = () => this.getRubrosContratos();

  getRubrosContratos() {
    return this.http.get(`${this.urlBase}/rubros-contrato`).pipe(
      map((res: { data: IRubrosContratoResponse }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getRubrosContrato(id: number) {
    return this.http.get(`${this.urlBase}/rubros-contrato/${id}`).pipe(
      map((res: { data: IRubrosContrato }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getRubrosContratoByContratoId(id: number) {
    return this.http.get(`${this.urlBase}/rubros-contrato/contrato/${id}`).pipe(
      map((res: { data: IRubrosContrato[] }) => {
        if (res.data) return res.data;
      }),
    );
  }

  createRubrosContrato(rubrosContrato: IRubrosContrato) {
    return this.http
      .post(`${this.urlBase}/rubros-contrato`, rubrosContrato)
      .pipe(
        map((res: { data: IRubrosContratoResponse }) => {
          if (res.data) return res.data;
        }),
      );
  }

  updateRubrosContrato(id: number, rubrosContrato: IRubrosContrato) {
    return this.http
      .put(`${this.urlBase}/rubros-contrato/${id}`, rubrosContrato)
      .pipe(
        map((res: { data: IRubrosContratoResponse }) => {
          if (res.data) return res.data;
        }),
      );
  }

  deleteRubrosContrato(id: number) {
    return this.http.delete(`${this.urlBase}/rubros-contrato/${id}`).pipe(
      map((res: { data: IRubrosContratoResponse }) => {
        if (res.data) return res.data;
      }),
    );
  }
}
