<div>
  <div class="page_title">
    <h1>{{ pageTitle }}</h1>
  </div>
  <hr />
  <mat-form-field>
    <input matInput placeholder="Buscar" (keyup)="filtrar($event)" />
  </mat-form-field>

  <div class="table-responsive" perfectScrollbar>
    <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
      <!-- Producto Column -->
      <ng-container matColumnDef="producto">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Producto</th>
        <td mat-cell *matCellDef="let element">{{ element.producto }}</td>
      </ng-container>

      <!-- Numero de Póliza Column -->
      <ng-container matColumnDef="poliza">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Poliza</th>
        <td mat-cell *matCellDef="let element">{{ element.poliza }}</td>
      </ng-container>

      <!-- Numero de Cetificado Column -->
      <ng-container matColumnDef="certificado">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Cetificado</th>
        <td mat-cell *matCellDef="let element">{{ element.certificado }}</td>
      </ng-container>

      <ng-container matColumnDef="marca">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Marca</th>
        <td mat-cell *matCellDef="let element">{{ element.marca }}</td>
      </ng-container>

      <ng-container matColumnDef="placa">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Placa</th>
        <td mat-cell *matCellDef="let element">{{ element.placa }}</td>
      </ng-container>

      <ng-container matColumnDef="modelo">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Modelo</th>
        <td mat-cell *matCellDef="let element">{{ element.modelo }}</td>
      </ng-container>

      <ng-container matColumnDef="chasis">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Chasis</th>
        <td mat-cell *matCellDef="let element">{{ element.chasis }}</td>
      </ng-container>

      <!-- Contratante Column -->
      <ng-container matColumnDef="contratante">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Contratante</th>
        <td mat-cell *matCellDef="let element">{{ element.contratante }}</td>
      </ng-container>

      <!-- Descripción Column -->
      <ng-container matColumnDef="asegurado">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>Asegurado</th>
        <td mat-cell *matCellDef="let element">{{ element.asegurado }}</td>
      </ng-container>

      <!-- Action Column -->
      <ng-container matColumnDef="actions">
        <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
        <td mat-cell *matCellDef="let element">
          <button
            mat-flat-button
            color="primary"
            (click)="showSelection(element)"
            matTooltip="Seleccionar"
            class="ml-2"
          >
            Seleccionar<mat-icon>touch_app</mat-icon>
          </button>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
    </table>
  </div>
  <mat-paginator
    [pageSizeOptions]="[5, 10, 20]"
    showFirstLastButtons
  ></mat-paginator>
</div>

<div mat-dialog-actions>
  <button mat-button (click)="close()">Cerrar</button>
</div>
