import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";

import { CardModule } from "../content/partials/general/card/card.module";
import { MaterialModule } from "../material.module";
import { ComisionesComponent } from "./comisiones.component";
import { ComisionUnidadRamoComponent } from "./comision-unidad-ramo/comision-unidad-ramo.component";
import { FormUnidadRamoComponent } from "./comision-unidad-ramo/form/form.component";
import { ComisionesRoutingModule } from "./comisiones-routing.module";
import { ComisionprogramadaramoComponent } from './comision-programada-ramo/comision-programada-ramo.component';

@NgModule({
  declarations: [
    ComisionesComponent,
    ComisionUnidadRamoComponent,
    FormUnidadRamoComponent,
    ComisionprogramadaramoComponent,
  ],
  imports: [
    CommonModule,
    CardModule,
    ComisionesRoutingModule,
    MaterialModule,
    ReactiveFormsModule,
    RouterModule,
  ],
})
export class ComisionesModule {}
