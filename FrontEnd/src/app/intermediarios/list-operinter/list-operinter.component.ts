import { Component, OnInit, ViewChild, Inject } from "@angular/core";
import { Router, ActivatedRoute, Params } from "@angular/router";

import { OperinterService } from "../services/operinter.service";
import { OperInter } from "./interfaces/oper-inter";

import { global } from "src/app/_services/global";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { DetalleOperacionComponent } from "../detalle-operaciones/detalle-operacion.component";
import { ModalService } from "src/app/_services/modal.service";
import Swal from "sweetalert2";
import { DetalleObligacionComponent } from "../detalle-obligaciones/detalle-obligacion.component";
import { ReprogramarPagosComponent } from "../reprogramar-pagos-anticipos/reprogramar-pago-anticipo-comision.component";

@Component({
  selector: "app-list-operinter",
  templateUrl: "./list-operinter.component.html",
  styleUrls: ["./list-operinter.component.css"],
})
export class ListOperinterComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  //public acreencia: EntidadAcreedora;
  public dataSource = null;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _operinterService: OperinterService,

    private _dialogRef: MatDialogRef<ListOperinterComponent>, //DIALOGO
    private readonly modalService: ModalService,

    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.page_title = "Listado de Operaciones Intermediario";
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "foperinter",
    "tipooperinter",
    "idoperinter",
    "montoopermoneda",
    "stsoperinter",
    "acciones",
    //"idrequerimiento",
    //"idoperacion",
    //"descorigen",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getOperInters(this.data.element.idintermediario);
  }

  getOperInters(id: string) {
    //console.log("getFactura list-factura-caja $id ",$id);
    //console.log("getFactura list-factura-caja nNumRelingTemp ",this.nNumRelingTemp);
    this._operinterService.getOperInterById(id).subscribe(
      (response) => {
        console.log(response);
        this.dataSource = new MatTableDataSource<OperInter>(response.data);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      },
    );
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  verDetalle(element: any): void {
    this._operinterService
      .getDetalleOperacion(element.idoperinter)
      .subscribe((res) => {
        if (res.status === "success") {
          this.modalService.openDialog<DetalleOperacionComponent>({
            title: "Detalle Operación",
            component: DetalleOperacionComponent,
            width: "800px",
            element: {
              detalle: res.detalles,
              operacion: res.operacion,
            },
          });
        } else {
          Swal.fire("Error", "No se pudo obtener el detalle.", "error");
        }
      });
  }

  verObligacion(element: any): void {
    this._operinterService
      .getDetalleOperacion(element.idoperinter)
      .subscribe((res) => {
        if (res.status === "success") {
          this.modalService.openDialog<DetalleObligacionComponent>({
            title: "Detalle Obligación",
            component: DetalleObligacionComponent,
            width: "800px",
            element: {
              obligacion: res.obligacion,
            },
          });
        } else {
          Swal.fire("Error", "No se pudo obtener la obligación.", "error");
        }
      });
  }

  cambiarEstadoOperacion(
    element: any,
    nuevoEstado: "SUS" | "ANU" | "ACT",
  ): void {
    const estadoTexto = {
      SUS: "suspender",
      ANU: "anular",
      ACT: "reactivar",
    };

    Swal.fire({
      title: `¿Estás seguro que deseas ${estadoTexto[nuevoEstado]} esta operación?`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: `Sí, ${estadoTexto[nuevoEstado]}`,
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        this._operinterService
          .cambiarEstadoAnticipo(
            element.idintermediario,
            element.idoperinter,
            nuevoEstado,
          )
          .subscribe({
            next: (res) => {
              Swal.fire("Éxito", res.message, "success");
              this.getOperInters(this.data.element.idintermediario);
            },
            error: (err) => {
              Swal.fire(
                "Error",
                err?.error?.message || "No se pudo actualizar el estado.",
                "error",
              );
            },
          });
      }
    });
  }

  openModalReprogramarPagos(element: any): void {

    if (element.stsoperinter !== 'ACT') {
      Swal.fire(
        'Operación inactiva',
        'Solo puedes reprogramar pagos en operaciones activas.',
        'warning'
      );
      return;
    }

    this._operinterService
      .getDetalleOperacion(element.idoperinter)
      .subscribe((res) => {
        if (res.status === "success") {
          this.modalService.openDialog<ReprogramarPagosComponent>({
            title: "Reprogramar Pagos",
            component: ReprogramarPagosComponent,
            width: "600px",
            element: {
              cuotas: res.detalles,
              idoperinter: element.idoperinter,
              estado: element.stsoperinter,
            },
            actionClose: {
              next: (result) => {
                if (result) {
                  console.log("Payload para reprogramarPagos:", result);
                  const payload = {
                    idoperinter: result.idoperinter,
                    desde_cuota: result.desde_cuota,
                    fecha_efectiva: result.fecha_efectiva?.format?.("YYYY-MM-DD") || result.fecha_efectiva,
                  };
                  console.log("Datos que van en el payload: ", payload)
                  this._operinterService.reprogramarPagos(payload).subscribe({
                    next: (res) => {
                      Swal.fire("Éxito", res.message, "success");
                      this.getOperInters(element.idintermediario);
                    },
                    error: () => {
                      Swal.fire("Error", "No se pudo reprogramar.", "error");
                    },
                  });
                }
              },
            },
          });
        }
      });
  }
}
