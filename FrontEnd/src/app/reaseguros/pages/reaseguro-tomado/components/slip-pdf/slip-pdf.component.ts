import { ChangeDetectionStrategy, Component, Inject } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { TuiDialog } from "@taiga-ui/cdk";
import { TuiPdfViewerOptions, TuiPdfViewerService } from "@taiga-ui/kit";
import { NgxSpinnerService } from "ngx-spinner";
import { MaintenanceComponent } from "src/app/components/maintenance/maintenance.component";
import { ModalService } from "src/app/_services/modal.service";
import { ReaseguroTomadoService } from "../../services/reaseguro-tomado.service";
import { ContextSlip } from "../../../../../interface";

export type Buttons = ReadonlyArray<
  Readonly<{
    text: string;
    color: string;
    onClick(context: TuiDialog<TuiPdfViewerOptions<Buttons>, string>): void;
  }>
>;

@Component({
  selector: "app-slip-pdf",
  templateUrl: "./slip-pdf.component.html",
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SlipPdfComponent {
  constructor(
    private reaseguroTomadoService: ReaseguroTomadoService,
    private ngxSpinner: NgxSpinnerService,
    @Inject(DomSanitizer) private readonly sanitizer: DomSanitizer,
    @Inject(TuiPdfViewerService)
    private readonly pdfService: TuiPdfViewerService,
    private readonly modalService: ModalService,
  ) {}

  show(
    options: TuiPdfViewerOptions<Buttons>,
    idcotizacion: string,
    plantillas: number[],
    context?: ContextSlip,
  ) {
    this.ngxSpinner.show();

    this.reaseguroTomadoService
      .slipCotizacion(idcotizacion, plantillas.toString())
      .subscribe({
        next: (value) => {
          const b64 = `data:application/pdf;base64, ${value}`;

          this.pdfService
            .open(this.sanitizer.bypassSecurityTrustResourceUrl(b64), options)
            .subscribe();

          this.ngxSpinner.hide();
        },
        error: (_) => {
          this.modalService.openDialog<MaintenanceComponent>({
            component: MaintenanceComponent,
            title: "Mantenimiento",
            element: {
              paragraph: context?.paragraph
                ? context?.paragraph
                : "Estamos configurando la documentación muy pronto la podras visualizar.",
              buttons: context?.buttons,
            },
          });
          this.ngxSpinner.hide();
        },
      });
  }
}
