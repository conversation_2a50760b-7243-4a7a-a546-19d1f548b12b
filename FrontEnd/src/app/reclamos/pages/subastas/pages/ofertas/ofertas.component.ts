import { Component, OnInit } from "@angular/core";
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { VwOfertas } from "../../models/vw-oferta";
import { OfertaService } from "../../services/oferta.service";
import { DetalleComponent } from "./detalle/detalle.component";
import { FormularioOfertaComponent } from "./formulario-oferta/formulario-oferta.component";

@Component({
  selector: "app-ofertas",
  templateUrl: "./ofertas.component.html",
  styleUrls: ["./ofertas.component.css"],
})
export class OfertasComponent implements OnInit {
  listadoOfertas: VwOfertas[];

  constructor(
    private ofertaService: OfertaService,
    private dialog: MatDialog
  ) {}

  ngOnInit() {
    this.getListOfertas();
  }

  getListOfertas() {
    this.ofertaService.getOfertas().subscribe(
      (data) => {
        this.listadoOfertas = data;
      },
      (error) => {}
    );
  }

  openDialogo(element: VwOfertas = null, tipo: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      title:
        tipo == "new"
          ? `Ingreso de Oferta`
          : `Cotización . ${element.idcotizacion}`,
      element: element,
    };

    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = true;
    dialogConfig.height = "700px";
    dialogConfig.width = "1000px";

    const dialogRef = this.dialog.open(FormularioOfertaComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      this.getListOfertas();
    });
  }

  openDialogoDetalle(element: VwOfertas = null, tipo: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      title:
        tipo == "new"
          ? `Detalle de Oferta`
          : `Cotización . ${element.idcotizacion}`,
      element: element,
    };

    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = true;
    dialogConfig.height = "500px";
    dialogConfig.width = "700px";

    const dialogRef = this.dialog.open(DetalleComponent, dialogConfig);
  }
}
