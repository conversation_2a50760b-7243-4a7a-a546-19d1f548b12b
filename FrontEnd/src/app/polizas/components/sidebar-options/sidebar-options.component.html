<div class="col-md-0.7" style="width: 50px; padding-right: 0px">
  <div class="form-actions">
    <button
      *ngFor="let button of buttons"
      mat-flat-button
      [class]="button.class"
      [color]="button.color"
      (click)="button.onClick ? button.onClick(poliza) : null"
      [matTooltip]="button.tooltip"
      [disabled]="isDisabled[button.label]"
    >
      <span *ngIf="!button.isMatIcon" [innerHTML]="button.icon"></span>
      <mat-icon *ngIf="button.isMatIcon">{{ button.icon }}</mat-icon>
    </button>
  </div>
</div>
