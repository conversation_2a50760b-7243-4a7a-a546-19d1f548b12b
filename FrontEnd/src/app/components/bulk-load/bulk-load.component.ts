import { Component, Inject } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";

@Component({
  selector: "app-bulk-load",
  templateUrl: "./bulk-load.component.html",
  styleUrls: ["./bulk-load.component.css"],
})
export class BulkLoadComponent {
  files: any[] = [];
  validExtension: string;
  validation: CallableFunction;
  uploaded = false;
  size: Map<number, any> = new Map();
  maintenance = true;

  // TODO: validar que si `validExtension` no trae el . agregarlo al inicio del
  // string
  constructor(@Inject(MAT_DIALOG_DATA) private data: { element: BulkArgs }, private dialogRef: MatDialogRef<BulkLoadComponent>) {
    this.validExtension = this.data.element.extensions.join(",");
    this.maintenance = this.data.element.maintenance;
  }

  onFileDropped(event) {
    this.prepareFilesList(event);
  }

  fileBrowseHandler(files) {
    this.prepareFilesList(files);
  }

  deleteFile(index: number) {
    this.files.splice(index, 1);
  }

  uploadFilesSimulator(index: number) {
    this.size.set(
      index,
      this.formatBytes(this.files[index]?.size, 0, this.data.element.limitSize)
    );

    setTimeout(() => {
      if (index === this.files.length) {
        return;
      } else {
        const progressInterval = setInterval(() => {
          if (this.files[index].progress === 100) {
            this.uploaded = true;
            clearInterval(progressInterval);
            this.uploadFilesSimulator(index + 1);
          } else {
            this.files[index].progress += 5;
          }
        }, 100);
      }
    }, 500);
  }

  prepareFilesList(files: Array<any>) {
    for (const item of files) {
      item.progress = 0;
      this.files.push(item);
    }
    this.uploadFilesSimulator(0);
  }

  close() {
    this.dialogRef.close(this.files);
  }

  /**
   *
   * @param bytes
   * @param decimals
   * @param limitSize callback return boolean for validation file size
   * @returns
   */
  formatBytes(
    bytes: number,
    decimals: number,
    limitSize?: CallableFunction
  ): string {
    if (bytes === 0) {
      return "0 Bytes";
    }
    const k = 1024;
    const dm = decimals <= 0 ? 0 : decimals || 2;
    const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const parseSize =
      parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];

    if (parseSize.includes("MB") && limitSize) {
      limitSize(this.parseTotal(parseSize));
    }

    return parseSize;
  }

  private parseTotal(total: string): number {
    return Number(total.split(" ")[0]);
  }
}

export interface BulkArgs {
  extensions: string[];
  limitSize?: CallableFunction;
  maintenance?: boolean;
}
