import { createFeature, createReducer, on } from "@ngrx/store";
import { ICliente } from "../services";
import { clientesActions } from "./clientes.actions";

export interface ClientesState {
  cliente: ICliente;
  loading: boolean;
  inclomplete: boolean;
  meta: any;
  personaId: number;
}

export const clientesInitialState: ClientesState = {
  cliente: null,
  loading: false,
  inclomplete: false,
  meta: null,
  personaId: null,
};

export const clienteFeature = createFeature({
  name: "clientes",
  reducer: createReducer(
    clientesInitialState,
    on(clientesActions.loadClienteSuccess, (state, { cliente }) => ({
      ...state,
      inclomplete: true,
      cliente,
    })),
    on(clientesActions.metadata, (state, { meta }) => ({
      ...state,
      meta,
    })),
    on(clientesActions.resetStateClientes, () => ({ ...clientesInitialState })),
    on(clientesActions.selectedPersona, (state, { personaId }) => ({
      ...state,
      personaId,
    })),
  ),
});
