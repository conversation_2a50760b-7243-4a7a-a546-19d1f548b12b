import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { takeUntil } from "rxjs/operators";
import { IContacto_Telefono } from "src/app/reaseguros/interfaces/ficha-contacto.interface";
import { ContactoTelefonoService } from "src/app/reaseguros/services/contacto-telefono.service";
import { FichaContactosService } from "src/app/reaseguros/services/ficha-contactos.service";
import Swal from "sweetalert2";

@Component({
  selector: "app-modal-telefono",
  templateUrl: "./modal-telefono.component.html",
  styleUrls: ["./modal-telefono.component.css"],
})
export class ModalTelefonoComponent implements OnInit {
  formulario = new FormGroup({
    contacto_id: new FormControl("", Validators.required),
    telefono: new FormControl("", Validators.required),
    principal: new FormControl(""),
  });

  contacto$ = this.fichaContactosService.getContactos();

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { data: IContacto_Telefono; idcreacion: string } },
    private fichaContactosService: FichaContactosService,
    private contactoTelefonoService: ContactoTelefonoService,
    private dialogRef: MatDialogRef<ModalTelefonoComponent>,
  ) {}

  ngOnInit() {
    if (this.data.element.data) {
      this.formulario.patchValue(this.data.element.data);
    }
    if (this.data.element.idcreacion) {
      this.formulario.patchValue({ contacto_id: this.data.element.idcreacion });
    }
    this.listen();
  }

  listen() {
    const id =
      this.data.element?.data?.id || Number(this.data.element?.idcreacion) || 0;
    this.formulario.get("telefono").valueChanges.subscribe((value) => {
      if (!value) return;
      this.contactoTelefonoService
        .validateTelefono(value, id)
        .pipe(takeUntil(this.formulario.get("telefono").valueChanges))
        .subscribe({
          next: (res) => {
            if (res) {
              this.formulario.get("telefono").setErrors(null);
            }
          },
          error: (err) => {
            Swal.fire("Telefono ya existe", "", "warning");
            this.formulario.get("telefono").setErrors({ invalid: true });
          },
        });
    });
  }

  create() {
    const obj = this.formulario.value;
    //obj.principal = obj.principal === "true";
    this.contactoTelefonoService.createTelefono(obj).subscribe(() => {
      Swal.fire("Telefono creado", "", "success");
      this.dialogRef.close();
    });
  }

  update() {
    this.contactoTelefonoService
      .updateTelefono(this.data.element.data.id, this.formulario.value)
      .subscribe(() => {
        Swal.fire("Telefono actualizado", "", "success");
        this.dialogRef.close();
      });
  }

  save() {
    if (this.data.element.data) {
      this.update();
    } else {
      this.create();
    }
  }
}
