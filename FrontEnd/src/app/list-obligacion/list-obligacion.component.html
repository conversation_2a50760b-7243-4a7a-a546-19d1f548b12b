<div class="mat-elevation-z8">  
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>          
  <hr />    
  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">         

    <!-- Numero Obligacion Column -->
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Obligacion </th>
      <td mat-cell *matCellDef="let element"> {{element.id}} </td>
    </ng-container>

    <!-- Estado Column -->
    <ng-container matColumnDef="stsoblig">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
      <td mat-cell *matCellDef="let element"> {{element.stsoblig}} </td>
    </ng-container>   
    
     <!-- Tipo Obligación Column -->
     <ng-container matColumnDef="tipooblig">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Tipo </th>
      <td mat-cell *matCellDef="let element"> {{element.tipooblig}} </td>
    </ng-container> 

    <!-- Moneda Column -->
    <ng-container matColumnDef="codmoneda">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Moneda </th>
      <td mat-cell *matCellDef="let element"> {{element.codmoneda}} </td>
    </ng-container>

    <!-- Monto Column -->
    <ng-container matColumnDef="mtonetoobligmoneda">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto</th>
      <td mat-cell *matCellDef="let element"> {{element.mtonetoobligmoneda | currency :' ':'code':'.2-2'}} </td>
    </ng-container>

    <!-- Monto Column 
    <ng-container matColumnDef="mtonetoobliglocal">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto Q</th>
      <td mat-cell *matCellDef="let element"> {{element.mtonetoobliglocal | currency :' ':'code'}} </td>
    </ng-container>
    -->

    <!-- Fecha Emisión Column -->
    <ng-container matColumnDef="fecsts">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> F.Emisión </th>
      <td mat-cell *matCellDef="let element"> {{element.fecsts | date:'dd/MM/yyyy'}} </td>
    </ng-container>

     <!-- Nombre Cliente Column -->
   <ng-container matColumnDef="nombre">
     <th mat-header-cell *matHeaderCellDef mat-sort-header> Nombre Cliente</th>
     <td mat-cell *matCellDef="let element"> {{element.nombre}} </td>
   </ng-container>

    <!-- Nombre Cliente Column -->
   <ng-container matColumnDef="sldoobligmoneda">
     <th mat-header-cell *matHeaderCellDef mat-sort-header> Saldo</th>
     <td mat-cell *matCellDef="let element"> {{element.sldoobligmoneda | currency :' ':'code':'.2-2'}} </td>
   </ng-container>

    <!-- Nombre Cliente Column 
    <ng-container matColumnDef="sldoobliglocal">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Saldo Q</th>
      <td mat-cell *matCellDef="let element"> {{element.sldoobliglocal | currency :' ':'code'}} </td>
    </ng-container>   
    -->
    <!-- Action Column -->
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
      <td mat-cell *matCellDef="let element">
        <!-- <button mat-flat-button color="primary" routerLink="/editar-caja/, caja.id"> -->
        <button mat-flat-button color="primary" (click)="onEdit(element)" [disabled]="onDisabledBtnActivated(element.stsoblig)"
                matTooltip="Editar Acreencia"
                matTooltipClass="tooltip-red"
        >
          <mat-icon>edit</mat-icon>
        </button>
        &nbsp;
        <button mat-flat-button color="accent" (click)="onDelete(element.id)" [disabled]="onDisabledBtnActivated(element.stsoblig)"
                matTooltip="Borrar Acreencia"
                matTooltipClass="tooltip-red"
        >
          Borrar<mat-icon>delete</mat-icon>
        </button>
        &nbsp;
        <button mat-flat-button color="basic" 
        (click)="onDetail(element)"
                matTooltip="Ver Detalle de la Acreencia"
                matTooltipClass="tooltip-red">
          Detalle<mat-icon>subject</mat-icon>
        </button>
        &nbsp;
        <button mat-flat-button color="accent" (click)="onActivated(element)" [disabled]="onDisabledBtnActivated(element.stsoblig)"
                matTooltip="Activar Obligacion"
                matTooltipClass="tooltip-red"
        >
          Activar<mat-icon>fact_check</mat-icon>
        </button>
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>
