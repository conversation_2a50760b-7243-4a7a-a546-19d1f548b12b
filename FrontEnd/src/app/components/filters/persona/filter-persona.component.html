<mat-form-field class="mt-2">
  <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
</mat-form-field>

<a
  mat-raised-button
  [hidden]="hiddenButton"
  class="w-100"
  (click)="addRegister()"
  ><PERSON><PERSON><PERSON> registro</a
>

<table
  mat-table
  [dataSource]="dataSource"
  matSort
  class="mat-elevation-z8"
  *ngIf="hiddenButton"
>
  <ng-container matColumnDef="id">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Código.</th>
    <td mat-cell *matCellDef="let element">{{ element.id }}</td>
  </ng-container>

  <ng-container matColumnDef="tipo">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo</th>
    <td mat-cell *matCellDef="let element">
      {{ element.tipopersona.tipopersona }}
    </td>
  </ng-container>

  <ng-container matColumnDef="nombre">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Nombre completo</th>

    <td mat-cell *matCellDef="let element">
      {{
        element.tipopersona.tipopersona !== "JURIDICO"
          ? element.nombre1 +
            " " +
            (element.nombre2 || "") +
            " " +
            (element.nombre3 || "") +
            " " +
            element.apellido1 +
            (element.apellido2 || "") +
            " " +
            (element.apellido3 || "")
          : element.nombreempresa
      }}
    </td>
  </ng-container>

  <ng-container matColumnDef="actions">
    <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
    <td mat-cell *matCellDef="let element"></td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr
    mat-row
    (click)="clickedRow(row)"
    (outsideClick)="clickDirective(row)"
    [class.row-is-clicked]="clickedRows.has(row)"
    *matRowDef="let row; columns: displayedColumns"
  ></tr>
</table>
<mat-paginator
  [pageSizeOptions]="[5, 10, 20]"
  showFirstLastButtons
></mat-paginator>
