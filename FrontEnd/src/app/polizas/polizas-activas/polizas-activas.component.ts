import { IDatospoliza, IPolizaGeneral } from './../../interface/polizas.interface';
import { IOperpoliza } from './../../interface/operPoliza';
import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { IPoliza } from "src/app/interface/polizas.interface";
import { PolizaService } from "../services/poliza.service";
import { ListenService } from "../../_services/listen.service";
import { MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";

@Component({
  selector: "app-polizas-activas",
  templateUrl: "./polizas-activas.component.html",
  styleUrls: ["./polizas-activas.component.css"],
})
export class PolizasActivasComponent implements OnInit {
  public pageTitle = "Polizas Activas";
  dataSource!: MatTableDataSource<IPoliza>;
  displayedColumns: string[] = [
    "codigo_producto",
    "poliza_aseguradora",
    "cliente_nombre",
    "vigencia_inicio",
    "vigencia_fin",
    "btn_actions",
  ];
  loadig: boolean = false;
  constructor(
    private _dialogRef: MatDialogRef<PolizasActivasComponent>,
    private polizaService: PolizaService,
    private listenService: ListenService, 
    private router: Router
  ) {}
  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort!: MatSort;

  ngOnInit(): void {
    // console.log('on init');
    this.getPolizasActivas();
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }
  close(): void {
    this._dialogRef.close();
  }

  getPolizasActivas() {
    this.polizaService.getAllPolizas().subscribe({
      next: (value) => {
        this.dataSource = new MatTableDataSource(value);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  getIdPoliza(e: IPoliza) {
    this.listenService.sendData<string>(e.poliza_aseguradora);
    this.close();
  }
}
