<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div class="table-responsive" perfectScrollbar>
          <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="mat-elevation-z8"
          >
            <!-- Id Column -->
            <ng-container matColumnDef="IDPROVEEDOR">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Id</th>
              <td mat-cell *matCellDef="let element">
                {{ element.IDPROVEEDOR }}
              </td>
            </ng-container>

            <!-- Nombre Column -->
            <ng-container matColumnDef="NOMBRE">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Nombre</th>
              <td mat-cell *matCellDef="let element">{{ element.NOMBRE }}</td>
            </ng-container>

            <!-- Estado Column -->
            <ng-container matColumnDef="STSPROVEEDOR">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
              <td mat-cell *matCellDef="let element">
                {{ element.STSPROVEEDOR }}
              </td>
            </ng-container>

            <!-- Tipo Column -->
            <ng-container matColumnDef="TIPOPROVEEDOR">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo</th>
              <td mat-cell *matCellDef="let element">
                {{ element.TIPOPROVEEDOR }}
              </td>
            </ng-container>

            <!-- Descripcion Column -->
            <ng-container matColumnDef="DESC_TIPO_PROVEEDOR">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Descripcion
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.DESC_TIPO_PROVEEDOR }}
              </td>
            </ng-container>

            <!-- TipoISR Column -->
            <ng-container matColumnDef="TIPOISR">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>TipoISR</th>
              <td mat-cell *matCellDef="let element">{{ element.TIPOISR }}</td>
            </ng-container>

            <!-- RetIVA Column -->
            <ng-container matColumnDef="RETIVA">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>RetIVA</th>
              <td mat-cell *matCellDef="let element">{{ element.RETIVA }}</td>
            </ng-container>

            <!-- FECINC Column -->
            <ng-container matColumnDef="FECINC">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>FECINC</th>
              <td mat-cell *matCellDef="let element">{{ element.FECINC }}</td>
            </ng-container>

            <!-- NIT Column -->
            <ng-container matColumnDef="NIT">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>NIT</th>
              <td mat-cell *matCellDef="let element">{{ element.NIT }}</td>
            </ng-container>

            <!-- USUARIO Column -->
            <ng-container matColumnDef="USUARIO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>USUARIO</th>
              <td mat-cell *matCellDef="let element">{{ element.USUARIO }}</td>
            </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
              <td mat-cell *matCellDef="let element">
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onEdit(element)"
                  matTooltip="Editar Proveedor"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onAnular(element)"
                  [disabled]="onDisabledBtnStatus(element.STSPROVEEDOR)"
                  matTooltip="Anular Proveedor"
                  matTooltipClass="tooltip-red"
                >
                  Anular<mat-icon>highlight_off</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onCotizaciones(element)"
                  matTooltip="Cotizaciones Participación"
                  matTooltipClass="tooltip-red"
                >
                  Cotizaciones<mat-icon>view_list</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
