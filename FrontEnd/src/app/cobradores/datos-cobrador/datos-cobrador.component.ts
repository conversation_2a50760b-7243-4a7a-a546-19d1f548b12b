import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { CobradoresService } from "../services/cobradores.service";

@Component({
  selector: "app-datos-cobrador",
  templateUrl: "./datos-cobrador.component.html",
  styleUrls: ["./datos-cobrador.component.css"],
})
export class DatosCobradorComponent implements OnInit {
  datosCobrador: any;
  constructor(
    private cobradoresService: CobradoresService,
    private router: Router,
  ) {}

  ngOnInit() {
    const id = this.router.url.split("/")[3];
    this.getDatos(parseInt(id));
  }

  getDatos(id: number) {
    this.cobradoresService.getDetalleCobrador(id).subscribe({
      next: (value) => {
        this.datosCobrador = value[0];
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  goBack() {
    this.router.navigate(["/cobradores"]);
  }
}
