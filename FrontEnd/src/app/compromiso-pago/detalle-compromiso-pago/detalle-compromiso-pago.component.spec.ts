import { ComponentFixture, TestBed, waitForAsync as  } from '@angular/core/testing';

import { DetalleCompromisoPagoComponent } from './detalle-compromiso-pago.component';

describe('DetalleCompromisoPagoComponent', () => {
  let component: DetalleCompromisoPagoComponent;
  let fixture: ComponentFixture<DetalleCompromisoPagoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ DetalleCompromisoPagoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DetalleCompromisoPagoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
