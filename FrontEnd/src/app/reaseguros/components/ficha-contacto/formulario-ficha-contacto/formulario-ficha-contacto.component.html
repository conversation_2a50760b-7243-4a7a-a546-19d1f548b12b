<form [formGroup]="formularioContacto" class="p-2">
  <div class="row">
    <div class="col">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>Codigo</mat-label>
        <input matInput formControlName="codigo" name="codigo" required />
      </mat-form-field>
    </div>
    <div class="col">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>Nombre Completo</mat-label>
        <input
          matInput
          formControlName="nombre"
          name="nombre"
          required
          sanitizeUppercase
        />
      </mat-form-field>
    </div>
  </div>
  <div class="row">
    <div class="col">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>Descripcion</mat-label>
        <input
          matInput
          formControlName="descripcion"
          name="descripcion"
          required
          sanitizeUppercase
        />
      </mat-form-field>
    </div>
    <div class="col">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>Codigo transferencia</mat-label>
        <input
          matInput
          formControlName="codigo_trans"
          name="codigo_trans"
          required
          sanitizeUppercase
        />
      </mat-form-field>
    </div>
  </div>
  <div class="d-flex justify-content-end m-2">
    <button
      mat-flat-button
      color="primary"
      (click)="onSave()"
      [disabled]="!formularioContacto.valid"
    >
      <mat-icon>save</mat-icon>
    </button>
  </div>
</form>
