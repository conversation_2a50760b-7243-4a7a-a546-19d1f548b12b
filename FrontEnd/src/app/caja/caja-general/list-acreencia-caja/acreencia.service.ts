import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { global } from "../../../_services/global";

@Injectable()
export class AcreenciaService {
    
    public url: string;
    
    constructor(
        private _http: HttpClient
    ) {
        this.url = global.url;
    }

    findAcreenciaByIdPagador(id, $nNumRelingTemp): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.get(this.url+'entidad-acreedora/findAcreenciaByIdPagador/' + id + '/' + $nNumRelingTemp, {headers: headers});
    }

    onSaveForm(numSelected, $id): Observable<any>{        
        let params = numSelected;        
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        let $tipo = 'ACREENCIA';
        return this._http.post(this.url+'tempfacturarequerimiento/store/'+$id+'/'+$tipo, params, {headers: headers});
    }

}