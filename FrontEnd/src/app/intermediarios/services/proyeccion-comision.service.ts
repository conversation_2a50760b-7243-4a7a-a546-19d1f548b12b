import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class ProyeccionComisionService {
  private readonly url = environment.apiUrlv1 + "/intermediarios";

  constructor(private readonly http: HttpClient) {}

  getProyeccionComisiones(id: number | string) {
    return this.http.get(`${this.url}/proyeccion-comisiones/${id}`).pipe(
      map((res: any) => {
        const data = res.data || res || [];
        return data;
      }),
    );
  }
}
