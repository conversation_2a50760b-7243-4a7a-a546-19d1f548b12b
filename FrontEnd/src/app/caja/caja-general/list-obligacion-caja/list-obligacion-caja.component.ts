import { Component, OnInit, ViewChild, Input, Inject } from '@angular/core';

import { PageEvent } from '@angular/material/paginator';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';

import { ObligacionService } from './obligacion.service';
import { CompromisoPago } from './compromiso-pago';

import { global } from "../../../_services/global";

//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-list-obligacion-caja',
  templateUrl: './list-obligacion-caja.component.html',
  styleUrls: ['./list-obligacion-caja.component.css'],
  providers: [ObligacionService]
})
export class ListObligacionCajaComponent implements OnInit {

  public page_title: string;
  public subtitle: string;
  public status: string;
  public token;
  public identity;
  public url;    
  //public acreencia: EntidadAcreedora; 
  public dataSource = null; 
  @Input() relacionIngreso: any; 

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(    
    private _obligacionService: ObligacionService,    
    //
    private _dialogRef: MatDialogRef<ListObligacionCajaComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { 
    this.page_title = "Listado de Primas en Depósito";    
    this.url = global.url;
  }  
  
  displayedColumns: string[] = ['id', 'stsoblig', 'tipooblig', 'nombre', 'fecsts', 'mtonetoobligmoneda', 'sldoobligmoneda', 'codmoneda', 'actions'];  
  
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  ngOnInit() {
    //let {id} = this.relacionIngreso.element;    
    let idPagador: string = this.data.idPagador;
    let idRelingTemp: string = this.data.idRelingTemp;
    //let nombrePagador: string = this.data.nombrePagador;
    //this.subtitle = this.data.title;
    this.getObligacionesIngreso(idPagador, idRelingTemp);        
  }  

  getObligacionesIngreso($id, $idRelingTemp){
    console.log('getObligacionesIngreso');
    let obligacion: any[] = []; 
    this._obligacionService.findCompromisoPagoByIdPagador($id, $idRelingTemp).subscribe(
      response => 
      {        
        if (response.status == 'success') 
        {            
            response.obligacion.forEach((dato) => 
             {
                obligacion.push(
                  {
                    id: dato.id,
                    idcliente: dato.idcliente, 
                    nombre: dato.cliente.nombre,      
                    tipooblig: dato.tipooblig, 
                    stsoblig: dato.stsoblig, 
                    fecsts: dato.fecsts, 
                    fecgtiapago: dato.fecgtiapago, 
                    textoblig: dato.textoblig,
                    codmoneda: dato.codmoneda, 
                    mtonetoobliglocal: dato.mtonetoobliglocal, 
                    mtobrutoobligmoneda: dato.mtobrutoobligmoneda, 
                    mtonetoobligmoneda: dato.mtonetoobligmoneda, 
                    mtobrutoobliglocal: dato.mtobrutoobliglocal,
                    dptoemi: dato.dptoemi, 
                    sldoobliglocal: dato.sldoobliglocal, 
                    sldoobligmoneda: dato.sldoobligmoneda, 
                    idesin: dato.idesin, 
                    idintermediario: dato.idintermediario, 
                    idreling: dato.idreling, 
                    numoper: dato.numoper,
                    idrelegre: dato.idrelegre, 
                    notacreditofiscal: dato.notacreditofiscal, 
                    numobligant: dato.numobligant, 
                    fecanul: dato.fecanul, 
                    emitecheque: dato.emitecheque, 
                    serie: dato.serie, 
                    numnotadb: dato.numnotadb,
                    cae: dato.cae, 
                    numnotachq: dato.numnotachq
                  }
                );
              }
            );            
        }   
        this.dataSource = new MatTableDataSource<CompromisoPago>(obligacion);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      error => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }
  
  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput.split(',').map(str => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  } 

  onSelected(element){
    console.log('onSelected'); 
    //this._dialogRef.close(element.idcliente);
    this._dialogRef.close(<CompromisoPago>element);
   }

}