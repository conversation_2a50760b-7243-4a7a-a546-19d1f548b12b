import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { PrivateLayoutComponent } from "./private-layout/private-layout.component";
import { RouterModule } from "@angular/router";
import { NavigationComponent } from "./navigation/navigation.component";
import { FooterComponent } from "./footer/footer.component";
import { HeaderComponent } from "./header/header.component";
import { VerticalnavComponent } from "./navigation/verticalnav/verticalnav.component";
import { VerticalComponent } from "./header/vertical/vertical.component";
import { MaterialModule } from "../material.module";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";

@NgModule({
  declarations: [
    FooterComponent,
    HeaderComponent,
    NavigationComponent,
    PrivateLayoutComponent,
    VerticalnavComponent,
    VerticalComponent,
  ],
  exports: [PrivateLayoutComponent],
  imports: [
    CommonModule,
    MaterialModule,
    NgbModule,
    PerfectScrollbarModule,
    RouterModule,
  ],
})
export class LayoutModule {}
