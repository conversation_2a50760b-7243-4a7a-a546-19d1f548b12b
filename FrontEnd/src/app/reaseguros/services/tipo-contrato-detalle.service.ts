import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";
import {
  ITipoContratoDetalle,
  ITipoContratoDetalleResponse,
} from "../interfaces/tipo-contrato-detalle.interface";

@Injectable({
  providedIn: "root",
})
export class TipoContratoDetalleService {
  private readonly urlBase = environment.apiUrlv1 + "/reaseguro";

  constructor(private readonly http: HttpClient) {}

  getData = () => this.getTipoContratoDetalles();

  getTipoContratoDetalles() {
    return this.http.get(`${this.urlBase}/tipo-contrato-detalle`).pipe(
      map((res: { data: ITipoContratoDetalleResponse[] }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getTipoContratoDetalle(id: number) {
    return this.http.get(`${this.urlBase}/tipo-contrato-detalle/${id}`).pipe(
      map((res: { data: ITipoContratoDetalle }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getTipoContratoDetalleByTipoContratoId(id: number) {
    return this.http
      .get(`${this.urlBase}/tipo-contrato-detalle/tipo-contrato/${id}`)
      .pipe(
        map((res: { data: ITipoContratoDetalle[] }) => {
          if (res.data) return res.data;
        }),
      );
  }

  createTipoContratoDetalle(tipoContratoDetalle: ITipoContratoDetalle) {
    return this.http
      .post(`${this.urlBase}/tipo-contrato-detalle`, tipoContratoDetalle)
      .pipe(
        map((res: { data: ITipoContratoDetalle }) => {
          if (res.data) return res.data;
        }),
      );
  }

  updateTipoContratoDetalle(
    id: number,
    tipoContratoDetalle: ITipoContratoDetalle,
  ) {
    return this.http
      .put(`${this.urlBase}/tipo-contrato-detalle/${id}`, tipoContratoDetalle)
      .pipe(
        map((res: { data: ITipoContratoDetalle }) => {
          if (res.data) return res.data;
        }),
      );
  }

  deleteTipoContratoDetalle(id: number) {
    return this.http.delete(`${this.urlBase}/tipo-contrato-detalle/${id}`).pipe(
      map((res: { data: ITipoContratoDetalle }) => {
        if (res.data) return res.data;
      }),
    );
  }
}
