import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AuthGuard } from "../_guards/auth.guard";
import { DetalleComponent } from "./page/detalle/detalle.component";
import { FormularioPersonasComponent } from "./page/formulario-personas/formulario-personas.component";
import { PersonasComponent } from "./personas.component";

const routes: Routes = [
  {
    path: "",
    component: PersonasComponent,
    canActivate: [AuthGuard],
  },
  {
    path: ":id",
    component: FormularioPersonasComponent,
    canActivate: [AuthGuard],
  },
  {
    path: "detalle/:id",
    component: DetalleComponent,
    canActivate: [AuthGuard],
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class PersonasRoutingModule {}
