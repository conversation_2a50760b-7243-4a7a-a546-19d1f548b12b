import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { Router, ActivatedRoute, Params } from "@angular/router";
import { PolizaService } from './poliza.service';
import { Poliza } from './poliza';

import { global } from "../_services/global";
import swal from 'sweetalert2';

import { PageEvent } from '@angular/material/paginator';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';

//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-list-poliza',
  templateUrl: './list-poliza.component.html',
  styleUrls: ['./list-poliza.component.css'],
  providers: [PolizaService]
})
export class ListPolizaComponent implements OnInit {

  public page_title: string;
  public status: string;  
  public url;    
  //public acreencia: EntidadAcreedora; 
  public dataSource = null; 

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _polizaService: PolizaService,        
    //
    private _dialogRef: MatDialogRef<ListPolizaComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { 
    this.page_title = "Listado de Pólizas";    
    this.url = global.url;
  }

  displayedColumns: string[] = ['idcliente', 'nombre', 'numdoc', 'nit', 'fecha_nacimiento', 'correo', 'telefono', /*'direccion',*/ 'actions'];  
  
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  ngOnInit() {
    this.getPolizas();        
  }  

  getPolizas(){
    console.log('getPolizas');
    this._polizaService.getPolizas().subscribe(
      response => {        
        if (response.status == 'success') {          
          this.dataSource = new MatTableDataSource<Poliza>(response.polizas);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;          
        }else{
          //this.status = 'error';          
        }        
      },
      error => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }
  
  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput.split(',').map(str => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  } 

 onSelected(element){
  console.log('onSelected'); 
  //this._dialogRef.close(element.idcliente);
  this._dialogRef.close(<Poliza>element);
 }

}
