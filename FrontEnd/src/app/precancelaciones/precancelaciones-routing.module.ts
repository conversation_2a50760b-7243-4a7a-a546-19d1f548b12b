import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { DetallePrecancelacionesComponent } from "./pages/precancelaciones/detalle-precancelaciones/detalle-precancelaciones.component";
import { NuevoPrecancelacionesComponent } from "./pages/precancelaciones/nuevo-precancelaciones/nuevo-precancelaciones.component";
import { PrecancelacionesComponent } from "./pages/precancelaciones/precancelaciones.component";

const routes: Routes = [
  {
    path: "",
    component: PrecancelacionesComponent,
  },
  {
    path: "nuevo",
    component: NuevoPrecancelacionesComponent,
  },
  {
    path: ":id",
    component: DetallePrecancelacionesComponent
  }
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class PrecancelacionesRoutingModule {}
