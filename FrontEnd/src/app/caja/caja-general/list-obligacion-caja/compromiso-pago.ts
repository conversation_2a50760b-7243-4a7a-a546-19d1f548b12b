export interface CompromisoPago {
    id: string;
    idcliente: string;
    tipooblig: any;
    stsoblig: string;
    fecsts: any;
    fecgtiapago: any;
    textoblig: string;
    codmoneda: string;
    mtonetoobliglocal: string;
    mtobrutoobligmoneda: string;
    mtonetoobligmoneda: string;
    mtobrutoobliglocal: string;
    dptoemi: string;
    sldoobliglocal: string;
    sldoobligmoneda: string;
    idesin: string;
    idintermediario: string;    
    idreling: string;
    numoper: string;
    idrelegre: string;
    notacreditofiscal: string;
    numobligant: string;
    fecanul: any;
    emitecheque: string;
    serie: string;
    numnotadb: string;
    cae: string;
    numnotachq: string;
  }
  