<form [formGroup]="form">
  <div class="page_title">
    <h1>{{ page_title }}</h1>
  </div>

  <mat-grid-list cols="3" rowHeight="65px" gutterSize="1px">
    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="concepto"
          placeholder="Concepto Pago"
          formControlName="concepto"
          (selectionChange)="changeConcepto($event.value)"
          required
        >
          <ng-container *ngFor="let cpago of conceptoPago">
            <mat-option
              [hidden]="
                entidadAcreedora.stsacre !== 'ACT' && cpago.valor == 'GADMON'
              "
              [value]="cpago.valor"
            >
              {{ cpago.descripcion }}
            </mat-option>
          </ng-container>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="tipodocing"
          placeholder="Tipo Documento Ingreso"
          (selectionChange)="changeClient($event.value)"
          formControlName="tipodocing"
          required
        >
          <mat-option
            *ngFor="let tipodocing of tipoDocumentoIngreso"
            [value]="tipodocing.valor"
          >
            {{ tipodocing.descripcion }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="identfinan"
          placeholder="Entidad Financiera"
          formControlName="identfinan"
          (selectionChange)="getTarjetaCredito($event.value)"
        >
          <mat-option
            *ngFor="let entidadFinanciera of entidadesFinancieras"
            [value]="entidadFinanciera.identfinan"
          >
            {{ entidadFinanciera.descentfinan }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="idtarjeta"
          placeholder="Tarjeta"
          formControlName="idtarjeta"
          (selectionChange)="changeTarjeta($event.value)"
        >
          <mat-option
            *ngFor="let tarjetaCredito of tarjetasCredito"
            [value]="tarjetaCredito.IDTARJETA"
          >
            {{ tarjetaCredito.DESCTARJCRED }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="numrelcta"
          placeholder="Cuenta Bancaria"
          formControlName="numrelcta"
        >
          <mat-option
            *ngFor="let cuentaBancaria of cuentasBancaria"
            [value]="cuentaBancaria.idcuentabancaria"
          >
            {{ cuentaBancaria.numero_cuenta }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>No.Documento</mat-label>
        <input
          matInput
          placeholder="Número Documento"
          type="number"
          name="numrefdoc"
          formControlName="numrefdoc"
        />
      </mat-form-field>
    </mat-grid-tile>
    <!--
    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Fecha</mat-label>
        <input matInput placeholder="Fecha" name="fecvaldocing" formControlName="fecvaldocing" readonly>
      </mat-form-field>
    </mat-grid-tile>
    -->

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Fecha Documento</mat-label>
        <input
          matInput
          [matDatepicker]="fecha_documento"
          placeholder="Fecha Documento"
          name="fecha_documento"
          formControlName="fecha_documento"
        />
        <mat-datepicker-toggle
          matSuffix
          [for]="fecha_documento"
        ></mat-datepicker-toggle>
        <mat-datepicker #fecha_documento></mat-datepicker>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select
          name="codmoneda"
          placeholder="Seleccione Moneda Tasa de Cambio"
          formControlName="codmoneda"
          (selectionChange)="getTasaCambio($event.value)"
          required
        >
          <mat-option *ngFor="let moneda of monedas" [value]="moneda.id">
            {{ moneda.name }}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Tasa Cambio</mat-label>
        <input
          matInput
          placeholder="Tasa Cambio"
          name="tasacambio"
          formControlName="tasacambio"
          readonly
        />
      </mat-form-field>
    </mat-grid-tile>

    <!--
    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Monto</mat-label>
        <input matInput placeholder="Monto" name="monto" formControlName="monto" (change)="doActualizaMontos($event)"
          min="0" currencyMask [options]="{ prefix: ' ', thousands: ',', decimal: '.' }" required />
      </mat-form-field>
    </mat-grid-tile>
   -->

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Monto</mat-label>
        <input
          type="number"
          matInput
          placeholder="Monto"
          name="monto"
          formControlName="monto"
          (change)="doActualizaMontos($event)"
          class="currency"
        />
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>No. Autorización</mat-label>
        <input
          matInput
          placeholder="No. Autorización"
          type="number"
          name="numero_autorizacion"
          formControlName="numero_autorizacion"
        />
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Monto Q</mat-label>
        <input
          matInput
          placeholder="Monto Q"
          name="mtodocinglocal"
          formControlName="mtodocinglocal"
          min="0"
          currencyMask
          [options]="{ prefix: ' ', thousands: ',', decimal: '.' }"
          readonly
        />
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Monto US$</mat-label>
        <input
          matInput
          placeholder="Monto US$"
          name="mtodocingmoneda"
          formControlName="mtodocingmoneda"
          min="0"
          currencyMask
          [options]="{ prefix: ' ', thousands: ',', decimal: '.' }"
          readonly
        />
      </mat-form-field>
    </mat-grid-tile>
  </mat-grid-list>

  <mat-grid-list cols="3" rowHeight="100px" gutterSize="1px">
    <mat-grid-tile>
      <mat-dialog-actions>
        <button mat-flat-button color="accent" (click)="close()">
          <mat-icon>highlight_off</mat-icon>Cerrar
        </button>
      </mat-dialog-actions>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-dialog-actions>
        <button
          mat-flat-button
          color="accent"
          (click)="onSaveForm(form.value)"
          matTooltip="Guardar"
          matTooltipClass="tooltip-red"
          [disabled]="!disableGuardar()"
        >
          <mat-icon>save</mat-icon>Guardar
        </button>
      </mat-dialog-actions>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-dialog-actions>
        <button
          mat-flat-button
          color="primary"
          (click)="onProcess()"
          matTooltip="Finalizar"
          matTooltipClass="tooltip-red"
        >
          <mat-icon>verified_user</mat-icon>Finalizar
        </button>
      </mat-dialog-actions>
    </mat-grid-tile>
  </mat-grid-list>
</form>

<table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">
  <!-- Correlativo Column -->
  <ng-container matColumnDef="idrelingtemp">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Rel Ingreso</th>
    <td mat-cell *matCellDef="let element">{{ element.idrelingtemp }}</td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <!-- Póliza Column -->
  <ng-container matColumnDef="concepto">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Concepto</th>
    <td mat-cell *matCellDef="let element">{{ element.concepto }}</td>
    <td mat-footer-cell *matFooterCellDef>Total</td>
  </ng-container>

  <!-- Póliza Column -->
  <ng-container matColumnDef="monto">
    <th mat-header-cell *matHeaderCellDef mat-sort-header>Monto Q.</th>
    <td mat-cell *matCellDef="let element">
      {{ element.monto | currency : "Q. " : "symbol" }}
    </td>
    <td mat-footer-cell *matFooterCellDef>
      {{ getTotalCost() | currency : "Q. " : "symbol" }}
    </td>
  </ng-container>

  <!-- Action Column -->
  <ng-container matColumnDef="actions">
    <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
    <td mat-cell *matCellDef="let element">
      <button
        mat-flat-button
        color="accent"
        (click)="onDelete(element)"
        matTooltip="Eliminar"
        matTooltipClass="tooltip-red"
      >
        <mat-icon>delete</mat-icon>
      </button>
    </td>
    <td mat-footer-cell *matFooterCellDef></td>
  </ng-container>

  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
  <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  <tr mat-footer-row *matFooterRowDef="displayedColumns"></tr>
</table>

<cheque-rechazado-pdf></cheque-rechazado-pdf>
