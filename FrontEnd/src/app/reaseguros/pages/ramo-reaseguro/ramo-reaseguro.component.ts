import { Component, OnInit } from "@angular/core";
import { ModalService } from "src/app/_services/modal.service";
import { IActions } from "src/app/shared/interfaces/actions.interface";
import Swal from "sweetalert2";
import { AsignarClasificacionRiesgoComponent } from "../../components/roamos-reaseguro/asignar-clasificacion-riesgo/asignar-clasificacion-riesgo.component";
import { ClasificacionRiesgoRamoComponent } from "../../components/roamos-reaseguro/clasificacion-riesgo-ramo/clasificacion-riesgo-ramo.component";
import { FormularioRamoReaseguroComponent } from "../../components/roamos-reaseguro/formulario-ramo-reaseguro/formulario-ramo-reaseguro.component";
import { IRamoReaseguro } from "../../interfaces/ramo-reaseguro.interface";
import { RamoReaseguroService } from "../../services/ramo-reaseguro.service";

@Component({
  selector: "app-ramo-reaseguro",
  templateUrl: "./ramo-reaseguro.component.html",
  styleUrls: ["./ramo-reaseguro.component.css"],
})
export class RamoReaseguroComponent implements OnInit {
  rechargeFunction: Function;

  actions: IActions[] = [
    {
      color: "accent",
      icon: "edit",
      tooltipText: "Editar",
      onClick: ({ row }) => {
        this.edit(row);
      },
    },
    /*{
      color: "warn",
      icon: "delete",
      tooltipText: "Eliminar",
      onClick: ({ row }) => {
        this.delete(row);
      },
    },*/ //No se puede eliminar debido a que se relaciona con otras tablas
  ];

  multiActions: IActions[] = [
    {
      tooltipText: "Ver clasificación de riesgo por ramo",
      text: "Ver clasificación de riesgo por ramo",
      //icon: "list",
      onClick: ({ row }) => {
        this.clasificacionRiesgoRamo(row);
      },
    },

    {
      tooltipText: "Asignar clasificación de riesgo",
      text: "Asignar clasificación de riesgo",
      //icon: "add",
      onClick: ({ row }) => {
        this.asignarClasificacionesRiesgo(row);
      },
    },
    {
      tooltipText: "Eliminar clasificación de riesgo por ramo",
      text: "Eliminar clasificación de riesgo por ramo",
      //icon: "list",
      onClick: ({ row }) => {
        this.clasificacionRiesgoRamo(row, true);
      },
    },
  ];

  constructor(
    public ramoReaseguroService: RamoReaseguroService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {}
  onNew() {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioRamoReaseguroComponent>({
      component: FormularioRamoReaseguroComponent,
      title: "Nuevo Ramo Reaseguro",
      width: "720px",
      element: { data: null },
      actionClose: onClose,
    });
  }

  edit(row: IRamoReaseguro) {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioRamoReaseguroComponent>({
      component: FormularioRamoReaseguroComponent,
      title: "Editar Ramo Reaseguro",
      width: "720px",
      element: { data: row },
      actionClose: onClose,
    });
  }

  delete(row: IRamoReaseguro) {
    Swal.fire({
      title: "Eliminar",
      text: "¿Está seguro de eliminar la modalidad?",
      icon: "warning",
      showDenyButton: true,
      confirmButtonText: "Si",
      denyButtonText: "No",
    }).then((result) => {
      if (result.isConfirmed) {
        this.ramoReaseguroService.deleteRamoReaseguro(row.id).subscribe(() => {
          Swal.fire(
            "Eliminado",
            "La clasificación de riesgo ha sido eliminada",
            "success",
          );
          this.rechargeFunction();
        });
      }
    });
  }

  clasificacionRiesgoRamo(row: IRamoReaseguro, eliminar?: boolean) {
    const onClose = {
      next: (value: boolean) => {
        //this.rechargeFunction();
      },
    };
    this.modalService.openDialog<ClasificacionRiesgoRamoComponent>({
      component: ClasificacionRiesgoRamoComponent,
      title: "Ver Clasificación de Riesgo por Ramo",
      width: "720px",
      element: { id: row.id, eliminar },
      actionClose: onClose,
    });
  }

  asignarClasificacionesRiesgo(row: IRamoReaseguro) {
    const onClose = {
      next: (value: boolean) => {
        //this.rechargeFunction();
      },
    };
    this.modalService.openDialog<AsignarClasificacionRiesgoComponent>({
      component: AsignarClasificacionRiesgoComponent,
      title: "Agregar Clasificación de Riesgo",
      width: "720px",
      element: { id: row.id },
      actionClose: onClose,
    });
  }
}
