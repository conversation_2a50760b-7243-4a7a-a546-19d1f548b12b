import { Component, OnInit, ViewChild } from '@angular/core';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';

import { CargaMasiva } from '../../models/carga-masiva';
import { CargaMasivaService } from '../../services/carga-masiva.service';

@Component({
  selector: 'app-carga-masiva',
  templateUrl: './carga-masiva.component.html',
  styleUrls: ['./carga-masiva.component.css']
})
export class CargaMasivaComponent implements OnInit {

  public page_title: string;
  public status: string;
  public dataSource: any = [];

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private cargaMasivaService: CargaMasivaService
  ) {
    this.page_title = "Mantenimiento de Carga Masiva";
  }

  displayedColumns: string[] = [
    "idcargamasiva",
    "fecha",
    "estado",
    "tipo_carga",
    "tipo_facturacion",
    "usuario",
    "poliza",
    "total_asegurada",
    "total_prima",
    "actions"
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  ngOnInit(): void {
    this.getCargaMasiva();
  }

  getCargaMasiva() {
    this.cargaMasivaService.getVwCargaMasiva().subscribe((response) => {
      this.dataSource = new MatTableDataSource<CargaMasiva>(response.data ?? []);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }, (error) => {
      console.log(error);
    });
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onNew() {
    this.router.navigate([`carga-masiva/nuevo`]);
  }

  onDetail(element) {
    this.router.navigate([`carga-masiva/detalle/${element.idcargamasiva}`]);
  }

  onProcess(element) {
    console.log("procesar");
  }

  onCalculate(elment) {
    console.log("calcular");
  }

  onDelete(element) {
    console.log("eliminar");
  }

}
