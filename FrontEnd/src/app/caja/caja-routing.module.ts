import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { CompromisoPagoComponent } from "../compromiso-pago/compromiso-pago.component";
import { DetalleCompromisoPagoComponent } from "../compromiso-pago/detalle-compromiso-pago/detalle-compromiso-pago.component";
import { BitacoraManualComponent } from "../gestion-cartera/bitacora-manual/bitacora-manual.component";
import { DetalleGestionComponent } from "../gestion-cartera/detalle-gestion/detalle-gestion.component";
import { GestionCarteraComponent } from "../gestion-cartera/gestion-cartera.component";
import { RelacionIngresoComponent } from "../relacion-ingreso/relacion-ingreso.component";
import { AuthGuard } from "../_guards/auth.guard";
import { AperturaCajaComponent } from "./apertura-caja/apertura-caja.component";
import { CajaGeneralComponent } from "./caja-general/caja-general.component";
import { CajaComponent } from "./caja/caja.component";
import { CajeroComponent } from "./cajero/cajero.component";
import { DetalleCajeroComponent } from "./cajero/detalle-cajero/detalle-cajero.component";
import { DepositoComponent } from "./deposito/deposito.component";
import { DetalleDepositoComponent } from "./deposito/detalle-deposito/detalle-deposito.component";
import { DetalleEntidadAcreedoraComponent } from "./entidad-acreedora/detalle-entidad-acreedora/detalle-entidad-acreedora.component";
import { EntidadAcreedoraComponent } from "./entidad-acreedora/entidad-acreedora.component";
import { FacturasAntComponent } from "./facturas-ant/facturas-ant.component";
import { PrestamoVidaIndividualComponent } from "./prestamo-vida-individual/prestamo-vida-individual.component";
import { RefraccionamientoComponent } from "./refraccionamiento/refraccionamiento.component";

const routes: Routes = [
  {
    path: "",
    component: CajaComponent,
  },
  {
    path: "cajero",
    component: CajeroComponent,
  },
  {
    path: "cajero/detalle/:id",
    component: DetalleCajeroComponent,
  },
  {
    path: "aperturacierrecaja",
    component: AperturaCajaComponent,
  },
  {
    path: "entidadacreedora",
    component: EntidadAcreedoraComponent,
  },
  {
    path: "entidadacreedora/detalleentidadacreedora/:id",
    component: DetalleEntidadAcreedoraComponent,
  },
  {
    path: "prestamovidaindividual/generar",
    component: PrestamoVidaIndividualComponent,
  },
  {
    path: "facturasant",
    component: FacturasAntComponent,
  },
  {
    path: "deposito",
    component: DepositoComponent,
  },
  {
    path: "deposito/detalle/:id",
    component: DetalleDepositoComponent,
  },
  {
    path: "gestioncartera",
    component: GestionCarteraComponent,
  },
  {
    path: "gestioncartera/bitacora/:id",
    component: BitacoraManualComponent,
  },

  {
    path: "gestioncartera/detalle/:id",
    component: DetalleGestionComponent,
    canActivate: [AuthGuard],
  },
  {
    path: "compromisopago",
    component: CompromisoPagoComponent,
  },
  {
    path: "compromisopago/detallecompromisopago/:id",
    component: DetalleCompromisoPagoComponent,
  },
  {
    path: "relacioningreso",
    component: RelacionIngresoComponent,
  },
  {
    path: "cajageneral",
    component: CajaGeneralComponent,
  },
  {
    path: "refraccionamiento",
    component: RefraccionamientoComponent,
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class CajaRoutingModule {}
