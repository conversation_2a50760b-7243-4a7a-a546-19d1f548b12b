import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: 'root'
})
export class PolizaEstadoService {

  public url: string;

  constructor(
    private _http: HttpClient
  ) {
    this.url = environment.url;
  }

  getPolizas(): Observable<any>{
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.get(this.url+'polizas/activas', {headers: headers});
  }

}
