<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{page_title}}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-back">
            <button mat-flat-button color="primary" (click)="onBack()">
              Regresar<mat-icon>back</mat-icon>
            </button>
          </div>

        </div>

        <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

          <!-- Id Column -->
          <ng-container matColumnDef="idoferta">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Id </th>
            <td mat-cell *matCellDef="let element"> {{element.idoferta}} </td>
          </ng-container>

          <!-- codigo Column -->
          <ng-container matColumnDef="codigo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Código </th>
            <td mat-cell *matCellDef="let element"> {{element.codigo}} </td>
          </ng-container>

          <!-- fecha_cotizacion Column -->
          <ng-container matColumnDef="fecha_cotizacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha </th>
            <td mat-cell *matCellDef="let element"> {{element.fecha_cotizacion}} </td>
          </ng-container>

          <!-- usuario Column -->
          <ng-container matColumnDef="usuario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Usuario </th>
            <td mat-cell *matCellDef="let element"> {{element.usuario}} </td>
          </ng-container>

          <!-- montototal Column -->
          <ng-container matColumnDef="precio_autorizado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Precio Cot </th>
            <td mat-cell *matCellDef="let element"> {{element.precio_autorizado}} </td>
          </ng-container>

          <!-- cantidad_repuestos Column -->
          <ng-container matColumnDef="nombre">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Ofertante </th>
            <td mat-cell *matCellDef="let element"> {{element.nombre}} </td>
          </ng-container>

          <!-- estado Column -->
          <ng-container matColumnDef="monto_oferta">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Ofertado </th>
            <td mat-cell *matCellDef="let element"> {{element.monto_oferta}} </td>
          </ng-container>

          <!-- cantidad_ofertantes Column -->
          <ng-container matColumnDef="fecha_ingreso">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha </th>
            <td mat-cell *matCellDef="let element"> {{element.fecha_ingreso}} </td>
          </ng-container>

          <!-- cantidad_ofertantes Column -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
            <td mat-cell *matCellDef="let element"> {{element.estado}} </td>
          </ng-container>

          <!-- Action Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
            <td mat-cell *matCellDef="let element">

              <button mat-flat-button color="primary" (click)="onAceptar(element)" matTooltip="Aceptar"
                matTooltipClass="tooltip-red"  [disabled]="onDisabledBtnStatus(element.estado)">
                Aceptar <mat-icon>check_circle</mat-icon>
              </button>
              &nbsp;
              <button mat-flat-button color="accent" (click)="onAceptar(element)" matTooltip="Revertir"
                matTooltipClass="tooltip-red"  [disabled]="onDisabledBtnRevert(element.estado)">
                Revertir <mat-icon>history</mat-icon>
              </button>

            </td>
            <td mat-footer-cell *matFooterCellDef> </td>
          </ng-container>


          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        </table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>
</div>