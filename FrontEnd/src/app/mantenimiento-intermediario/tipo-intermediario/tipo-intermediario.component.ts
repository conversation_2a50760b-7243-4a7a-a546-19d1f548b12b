import { Component, OnInit } from "@angular/core";
import { TipoIntermediarioService } from "../Services/tipo-intermediario.service";
import { BehaviorSubject } from "rxjs";
import { TableColumn } from "src/app/components/icore-table/table";
import { switchMap } from "rxjs/operators";
import { ModalService } from "src/app/_services/modal.service";
import Swal from "sweetalert2";
import { IntTipoIntermediario } from "src/app/interface/tipoIntermediario.interface";
import { FormularioTipoIntermediarioComponent } from "./modal-tipo-intermediario/formulario-tipo-intermediario/formulario-tipo-intermediario.component";

@Component({
  selector: "app-tipo-intermediario",
  templateUrl: "./tipo-intermediario.component.html",
  styleUrls: ["./tipo-intermediario.component.css"],
})
export class TipoIntermediarioComponent implements OnInit {
  valueToSearch = "";
  filtroToSend: any;
  refreshData$ = new BehaviorSubject<void>(null);

  data$ = this.refreshData$.pipe(
    switchMap(() =>
      this.tipoIntermediarioService.getTipoIntermediario(this.filtroToSend),
    ),
  );

  displayedColumns: TableColumn<IntTipoIntermediario>[] = [
    {
      columnDef: "tipo",
      header: "Tipo",
      cell: (row) => row.tipo,
    },
    {
      columnDef: "descripcion",
      header: "Descripción",
      cell: (row) => row.descripcion,
    },
    {
      columnDef: "estado",
      header: "Estado",
      cell: (row) => row.estado,
      chip: [
        {
          validationProperty: "ACT",
          color: "primary",
          info: "Activo",
        },
        {
          validationProperty: "INA",
          color: "accent",
          info: "Inactivo",
        },
      ],
    },
    {
      columnDef: "actions",
      header: "Acciones",
      actions: [
        {
          color: "accent",
          icon: "edit",
          tooltipText: "Editar",
          onClick: (element) => {
            if (element.row.estado === "INA") {
              Swal.fire({
                icon: "warning",
                title: "No se puede editar",
                text: "Este registro está inactivo y no puede ser editado.",
                confirmButtonText: "Aceptar",
              });
              return;
            }
            this.openModal(element.row, element.row.id);
          },
        },
        {
          color: "primary",
          icon: "check_circle",
          tooltipText: "Activar",
          onClick: (element) => {
            this.activarTipoIntermediario(element.row.id);
          },
          condition: (element) => element.estado !== "INA",
        },
        {
          color: "warn",
          icon: "cancel",
          tooltipText: "Inactivar",
          onClick: (element) => {
            this.inactivarTipoIntermediario(element.row.id);
          },
          condition: (element) => element.estado !== "ACT",
        },
      ],
    },
  ];

  constructor(
    private tipoIntermediarioService: TipoIntermediarioService,
    private modalService: ModalService,
  ) {}

  ngOnInit(): void {
    this.refreshData$.next();
  }

  filtrar(event: Event) {
    const valor = (event.target as HTMLInputElement).value;
    this.valueToSearch = valor;
    this.filtroToSend = {
      param: ["tipo", "descripcion"],
      value: valor,
    };
    this.refreshData$.next();
  }

  openModal(data = null, id: number = null) {
    const actionClose = {
      next: () => this.refreshData$.next(),
    };

    this.modalService.openDialog<FormularioTipoIntermediarioComponent>({
      component: FormularioTipoIntermediarioComponent,
      title: data ? "Editar Tipo Intermediario" : "Crear Tipo Intermediario",
      element: { data, id },
      actionClose,
    });
  }

  activarTipoIntermediario(id: number) {
    Swal.fire({
      title: "¿Estás seguro?",
      text: "El tipo intermediario se marcará como activo",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Sí, activar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        this.tipoIntermediarioService.activarTipoIntermediario(id).subscribe({
          next: (res) => {
            Swal.fire(
              "Activado",
              res.message || "Tipo intermediario activado",
              "success",
            );
            this.refreshData$.next();
          },
          error: (err) => {
            Swal.fire(
              "Error",
              err.error?.message || "No se pudo activar",
              "error",
            );
          },
        });
      }
    });
  }

  inactivarTipoIntermediario(id: number) {
    Swal.fire({
      title: "¿Estás seguro?",
      text: "El tipo intermediario se marcará como inactivo",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Sí, inactivar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        this.tipoIntermediarioService.inactivarTipoIntermediario(id).subscribe({
          next: (res) => {
            Swal.fire(
              "Inactivado",
              res.message || "Tipo intermediario inactivado",
              "success",
            );
            this.refreshData$.next();
          },
          error: (err) => {
            Swal.fire(
              "Error",
              err.error?.message || "No se pudo inactivar",
              "error",
            );
          },
        });
      }
    });
  }
}
