import { Component, Inject, OnD<PERSON>roy, OnInit } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { takeUntil, tap } from "rxjs/operators";
import { DestroyState } from "src/app/@core";
import { BienesCotizacion, BienPaylaod } from "src/app/interface";
import { ReaseguroTomadoFacade } from "../../+state/reaseguro-tomado.facade";
import { ReaseguroTomadoService } from "../../services/reaseguro-tomado.service";

@Component({
  selector: "app-bienes-cotizacion",
  templateUrl: "./bienes-cotizacion.component.html",
  styleUrls: ["./bienes-cotizacion.component.css"],
})
export class BienesCotizacionComponent
  extends DestroyState
  implements OnInit, OnDestroy
{
  formularios = {};

  constructor(
    @Inject(MAT_DIALOG_DATA) private data: { element: BienesCotizacion },
    private dialogRef: MatDialogRef<BienesCotizacionComponent>,
    private reaseguroTomadoService: ReaseguroTomadoService,
    private reaseguroTomadoFacade: ReaseguroTomadoFacade,
  ) {
    super();
  }

  ngOnInit(): void {
    this.formularios = this.data.element.bienes;
  }

  ngOnDestroy(): void {
    this.onDestroy();
  }

  calcularPrima(key: string) {
    (this.formularios[key] as BienPaylaod[]).forEach((bien) => {
      bien.prima = Number.parseFloat(
        (bien.suma * (bien.tasa / 100)).toFixed(2),
      );
    });
  }

  save(key: string) {
    const parseFormulario = this.parseForm<BienesCotizacion, BienPaylaod>(
      this.formularios,
      key,
      (bien) => ({
        idbien: bien.idbien,
        tasa: Number(bien.tasa),
        prima: Number(bien.prima),
        suma: Number(bien.suma),
      }),
    );

    this.reaseguroTomadoFacade.reaseguroTomado$
      .pipe(
        takeUntil(this.destroy$),
        tap(({ idcotizacion, numcert }) => {
          this.addBienesSuscribe(idcotizacion, numcert, parseFormulario);
        }),
      )
      .subscribe();
  }

  close() {
    this.dialogRef.close();
  }

  private addBienesSuscribe(
    idcotizacion: number,
    numcert: number,
    data: BienPaylaod,
  ) {
    this.reaseguroTomadoService
      .addBienes(idcotizacion, numcert, data)
      .pipe(takeUntil(this.destroy$))
      .subscribe();
  }

  /**
   * parseForm return payload, 'ngModel' only, with strict types.
   * parseData form to number, default true
   *
   * @param data
   * @param key
   * @param prototyfn
   * @param returntypeNumber
   * @returns
   */
  private parseForm<T, K>(
    data: unknown,
    key: string,
    prototyfn: (value: K, index: number, array: K[]) => K,
  ): K {
    return (data as T)[key].map(prototyfn);
  }
}
