<div class="mat-elevation-z8">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr />

  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

    <!-- Numero Documento Ingreso Column -->
    <ng-container matColumnDef="id">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> DocIng </th>
      <td mat-cell *matCellDef="let element"> {{element.id}} </td>
    </ng-container>

    <!-- Numero Referencia Documento Pago Column -->
    <ng-container matColumnDef="numrefdoc">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Referencia </th>
      <td mat-cell *matCellDef="let element"> {{element.numrefdoc}} </td>
    </ng-container>

    <!-- Estado Column -->
    <ng-container matColumnDef="stsdocing">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
      <td mat-cell *matCellDef="let element"> {{element.stsdocing}} </td>
    </ng-container>

    <!-- Ent. Financiera Column -->
    <ng-container matColumnDef="descentfinan">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Ent. Financiera </th>
      <td mat-cell *matCellDef="let element"> {{element.descentfinan}} </td>
    </ng-container>

    <!-- Tipo Column -->
    <ng-container matColumnDef="tipodocing">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Tipo </th>
      <td mat-cell *matCellDef="let element"> {{element.tipodocing}} </td>
    </ng-container>

    <!-- Monto Column -->
    <ng-container matColumnDef="codmoneda">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Moneda</th>
      <td mat-cell *matCellDef="let element"> {{element.codmoneda}} </td>
    </ng-container>

    <!-- Monto Column 
    <ng-container matColumnDef="mtodocinglocal">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto Q</th>
      <td mat-cell *matCellDef="let element"> {{element.mtodocinglocal | currency :' ':'code'}} </td>
    </ng-container>
    -->

    <!-- Monto Column -->
    <ng-container matColumnDef="mtodocingmoneda">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto</th>
      <td mat-cell *matCellDef="let element"> {{element.mtodocingmoneda | currency :' ':'code'}} </td>
    </ng-container>

    <!-- Fecha Emisión Column -->
    <ng-container matColumnDef="fecsts">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha </th>
      <td mat-cell *matCellDef="let element"> {{element.fecsts | date: 'dd-MM-yyyy' }} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>