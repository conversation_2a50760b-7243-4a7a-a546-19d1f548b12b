.modal-container {
  padding: 20px;
  min-width: 500px;
}

.section-item {
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: background-color 0.2s;
}

.section-item:hover {
  background-color: #f5f5f5;
}

.section-item.has-data {
  font-weight: 500;
}

.no-data {
  color: #999;
  font-size: 0.8em;
}

.section-content {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  max-height: 400px;
  overflow-y: auto;
}

.form-data-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.form-field {
  display: flex;
  flex-direction: column;
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.field-label {
  font-weight: bold;
  color: #555;
  margin-bottom: 4px;
}

.field-value {
  color: #333;
}