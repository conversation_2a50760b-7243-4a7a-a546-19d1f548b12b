import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { ModalService } from "src/app/_services/modal.service";
import { IActions } from "src/app/shared/interfaces/actions.interface";
import { FormularioReaseguradorComponent } from "../../components/reasegurador/formulario-reasegurador/formulario-reasegurador.component";
import { IReaseguradores } from "../../interfaces/reaseguradores.interface";
import { ReaseguradoresService } from "../../services/reaseguradores.service";
import { FormularioReaseguradorReporteComponent } from "../../components/reasegurador/formulario-reasegurador-reporte/formulario-reasegurador-reporte.component";
import { EstadoCuentaReaseguradorComponent } from "../../components/reasegurador/estado-cuenta-reasegurador/estado-cuenta-reasegurador.component";
@Component({
  selector: "app-reaseguradores",
  templateUrl: "./reaseguradores.component.html",
  styleUrls: ["./reaseguradores.component.css"],
})
export class ReaseguradoresComponent implements OnInit {
  rechargeFunction: Function;

  actions: IActions[] = [
    {
      color: "accent",
      icon: "edit",
      tooltipText: "Ver",
      onClick: ({ row }) => {
        this.edit(row);
      },
    },
    {
      tooltipText: "Ver",
      icon: "info",
      onClick: ({ row }) => {
        this.router.navigate(["/reaseguros/ficha-contacto", row.id]);
      },
      color: "accent",
    },
    {
      tooltipText: "Activar",
      icon: "check",
      onClick: ({ row }) => {
        this.activate(row);
      },
      color: "primary",
      condition: (row) => row.estado !== "INA",
    },
    {
      tooltipText: "Desactivar",
      icon: "block",
      onClick: ({ row }) => {
        this.deactivate(row);
      },
      color: "warn",
      condition: (row) => row.estado !== "ACT",
    },
    {
      tooltipText: "Reporte",
      icon: "info",
      onClick: ({ row }) => {
        this.report(row);
      },
      color: "accent",
    },
    {
      tooltipText: "Estado de cuenta",
      icon: "article",
      onClick: ({ row }) => {
        this.estadoCuenta(row);
      },
      color: "primary",
    },
  ];
  constructor(
    public reaseguradoresService: ReaseguradoresService,
    private modalService: ModalService,
    private router: Router,
  ) {}

  ngOnInit() {}

  onNew() {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioReaseguradorComponent>({
      component: FormularioReaseguradorComponent,
      title: "Nuevo reasegurador",
      width: "720px",
      element: { data: null },
      actionClose: onClose,
    });
  }

  edit(row: IReaseguradores) {
    if (!this.rechargeFunction) {
      console.error("La función de recarga aún no se ha asignado");
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        console.log("cerrado");
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioReaseguradorComponent>({
      component: FormularioReaseguradorComponent,
      title: "Editar broker",
      width: "720px",
      element: { data: row },
      actionClose: onClose,
    });
  }

  activate(row: IReaseguradores) {
    const id = row.id;
    delete row.id;
    row.estado = "ACT";
    this.reaseguradoresService.updateReasegurador(id, row).subscribe(() => {
      this.rechargeFunction();
    });
  }

  deactivate(row: IReaseguradores) {
    const id = row.id;
    delete row.id;
    row.estado = "INA";
    this.reaseguradoresService.updateReasegurador(id, row).subscribe(() => {
      this.rechargeFunction();
    });
  }

  report(row: IReaseguradores) {
    if (!this.rechargeFunction) {
      console.error("La función de recarga aún no se ha asignado");
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        console.log("cerrado");
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioReaseguradorReporteComponent>({
      component: FormularioReaseguradorReporteComponent,
      title: "Generar reporte",
      width: "720px",
      element: { data: row },
      actionClose: onClose,
    });
  }

  estadoCuenta(row: IReaseguradores) {
    this.modalService.openDialog<EstadoCuentaReaseguradorComponent>({
      component: EstadoCuentaReaseguradorComponent,
      title: "Generar reporte",
      width: "720px",
      element: { data: row },
    });
  }
}
