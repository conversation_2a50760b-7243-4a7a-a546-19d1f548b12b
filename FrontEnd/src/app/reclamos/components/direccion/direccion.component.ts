import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MatDialogRef } from "@angular/material/dialog";
import { Observable, of } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { DestroyState } from "src/app/@core";
import { IDepartamento, IMunicipio, IPais, IZona } from "src/app/interface";
import { CountryService } from "src/app/_services/country.service";
import { ReclamoFacade } from "../../+state/reclamos.facade";

@Component({
  selector: "app-direccion",
  templateUrl: "./direccion.component.html",
  styleUrls: ["./direccion.component.css"],
})
export class DireccionComponent
  extends DestroyState
  implements OnInit, OnDestroy
{
  paises$: Observable<IPais[]>;
  departamentos$: Observable<IDepartamento[]>;
  municipios$: Observable<IMunicipio[]>;
  zonas$: Observable<IZona[]>;

  private idpais: number = 0;
  private iddepartamento: number = 0;
  private direccion: string = "";

  direccionForm = new FormGroup({
    idpais: new FormControl(19),
    iddepartamento: new FormControl(1),
    idmunicipio: new FormControl(10),
    idzona: new FormControl(5),
    domicilio: new FormControl(""),
  });

  constructor(
    private countryService: CountryService,
    private _dialogRef: MatDialogRef<DireccionComponent>,
    private facade: ReclamoFacade
  ) {
    super();
  }

  ngOnInit(): void {
    this.paises$ = this.countryService.getPaises();
    this.loadDefault();
    this.facade.direccionReclamo$.subscribe({
      next: (direccion) => {
        this.selectPais({ idpais: direccion.idpais, descripcion: "" });
        this.selectDepartamento({
          iddepartamento: direccion.iddepartamento,
          descripcion: "",
        });
        this.selectMunicipio({
          idmunicipio: direccion.idmunicipio,
          descripcion: "",
        });
        this.selectZona({
          idzona: direccion.idzona,
          descripcion: "",
        });

        this.direccionForm.patchValue({
          idpais: direccion.idpais,
          iddepartamento: direccion.iddepartamento,
          idmunicipio: direccion.idmunicipio,
          idzona: direccion.idzona,
          domicilio: direccion.domicilio,
        });
      },
    });
  }

  ngOnDestroy(): void {
    this.onDestroy();
  }

  close() {
    this._dialogRef.close();
  }

  selectPais(event: IPais) {
    this.departamentos$ = this.countryService
      .getDepartamentos(event.idpais)
      .pipe(takeUntil(this.destroy$));
    this.municipios$ = of([]);
    this.zonas$ = of([]);

    this.idpais = event.idpais;
    this.direccion += event.descripcion + ",";
  }

  selectDepartamento(event: IDepartamento) {
    this.municipios$ = this.countryService
      .getMunicipios(event.iddepartamento)
      .pipe(takeUntil(this.destroy$));

    this.zonas$ = of([]);
    this.iddepartamento = event.iddepartamento;
    this.direccion += event.descripcion + " ";
  }

  selectMunicipio(event: IMunicipio) {
    this.zonas$ = this.countryService
      .getZonas(this.idpais, this.iddepartamento, event.idmunicipio)
      .pipe(takeUntil(this.destroy$));

    this.direccion += event.descripcion + " ";
  }

  selectZona(event: IZona) {
    this.direccion += event.descripcion + " ";
  }

  saveDireccion() {
    const direccion = {
      ...this.direccionForm.value,
      direccion: `${this.direccion}, ${this.direccionForm.controls["domicilio"].value}`,
    };
    this.facade.setDireccionReclamo(direccion);
    this._dialogRef.close();
  }

  private loadDefault() {
    const idpais = this.direccionForm.get("idpais").value;
    const iddepartamento = this.direccionForm.get("iddepartamento").value;
    const idmunicipio = this.direccionForm.get("idmunicipio").value;
    const idzona = this.direccionForm.get("idzona").value;

    if (idpais !== "") this.selectPais({ idpais, descripcion: "GUATEMALA" });

    if (iddepartamento !== "")
      this.selectDepartamento({ iddepartamento, descripcion: "GUATEMALA" });

    if (idmunicipio !== "")
      this.selectMunicipio({ idmunicipio, descripcion: "GUATEMALA" });

    if (idzona !== "") this.selectZona({ idzona, descripcion: "ZONA 1" });
  }
}
