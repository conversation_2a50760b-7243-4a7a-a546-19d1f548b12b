import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { global } from "../../../_services/global";

@Injectable()
export class DetalleAcreenciaService {
    
    public url: string;
    public selected = {
        id: null, 
        numacre: '', 
        codclaingre: '', 
        codcptoacre: '',
        mtodetacrelocal: '', 
        natcptoacre: '',
        mtocptoacre: '', 
        porccptoacre: '',
        mtodetacremoneda: ''
    };


    constructor(
        private _http: HttpClient
    ) {
        this.url = global.url;
    }

    getDetalleAcreencias(): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.get(this.url+'detalle-entidad-acreedora', {headers: headers});
    }
    
    getDetalleAcreencia(id): Observable<any>{     
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'detalle-entidad-acreedora/detalleAcreencia/' + id, {headers: headers});
    }

    getGrupoIngreso(): Observable<any>{             
        let headers = new HttpHeaders().set('Content-Type', 'application/json');        
        return this._http.post(this.url+'concepto-ingreso/grupoingreso', {headers: headers});
    }

    getConceptoIngreso(grupo: string, concepto: string): Observable<any>{     
        let params = grupo; 
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'concepto-ingreso/conceptoingreso/'+grupo+"/"+concepto, params, {headers: headers});
        //return this._http.post(this.url+'concepto-ingreso/conceptoingreso/'+grupo, params, {headers: headers});
    }
    
    create(acreencia): Observable<any>{        
        let params = acreencia;
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'detalle-entidad-acreedora', params, {headers: headers});
    }
  
    update(acreencia): Observable<any>{
        console.log('update');
        let params = acreencia;
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        //console.log('params',params);
        return this._http.put(this.url+'detalle-entidad-acreedora/'+params.id,   params, {headers: headers});
    }

    delete(acreencia): Observable<any>{        
        console.log('delete');
        let json = JSON.stringify(acreencia);
        let params = 'json='+json;
        let headers = new HttpHeaders().set('Content-Type', 'application/x-www-form-urlencoded');        
        return this._http.post(this.url+'detalle-entidad-acreedora/delete', params, {headers: headers});
        
    }

}