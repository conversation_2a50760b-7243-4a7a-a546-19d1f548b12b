import { PIPES } from "./@types";

export interface TableColumn<T = any> extends ButtonProps<T> {
  columnDef: string;
  header: string;
  cell?: Function;
  pipes?: Pipes;
  chip?: Chip[];
  filter?: boolean;
  isChecked?: boolean;
  idPerso?: string; //idPersonalizado para filtrar si el nombre de la columna es diferente
}

export type EmitTableIcore<T = any> = {
  row: T;
  index?: number;
};

export type Chip = {
  validationProperty: string;
  color: "primary" | "accent" | "none";
  info?: string;
};

interface ButtonProps<T> {
  actions?: Button<T>;
  multiActions?: Pick<
    ButtonArgs<T>,
    "icon" | "tooltipText" | "text" | "onClick" | "condition"
  >[];
}

type Button<T> = ReadonlyArray<Readonly<ButtonArgs<T>>>;

type ButtonArgs<T> = {
  onClick(element: EmitTableIcore<T>): void;
  class?: string;
  color?: string;
  text?: string;
  tooltipText?: string;
  icon?: string | ((element: any) => string);
  condition?(chunk: any): boolean;
  hide?: ((element: any) => boolean) | boolean;
};

type Pipes = Readonly<{
  token: PIPES;
  format: string;
}>;
