<mat-form-field [appearance]="appearance" class="col">
  <mat-label>{{ placeholder }}</mat-label>
  <mat-select [formControl]="control" #selectorGet>
    <mat-form-field [floatLabel]="'never'" class="search-input">
      <input
        matInput
        [placeholder]="searchPlaceholder"
        [formControl]="filterFormControl"
      />

      <button
        matSuffix
        mat-icon-button
        (click)="filterFormControl.setValue('')"
      >
        <mat-icon>clear</mat-icon>
      </button>
    </mat-form-field>
    <mat-option disabled="true" style="display: none"></mat-option>

    <div *ngIf="items | async as retrievedItems; else waitingResponse">
      <mat-option
        *ngFor="let item of retrievedItems"
        [value]="item[bindValueKey]"
      >
        <span>{{ item[this.bindLabelKey] }}</span>
      </mat-option>
    </div>

    <ng-template #waitingResponse>
      <mat-option disabled="true">Espera...</mat-option>
    </ng-template>
  </mat-select>
</mat-form-field>
