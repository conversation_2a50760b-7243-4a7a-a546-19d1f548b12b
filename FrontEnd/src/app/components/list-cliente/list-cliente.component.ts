import { Component, OnInit, ViewChild, Inject } from "@angular/core";
import { UserService } from "../../_services/user.service";
import { ClienteService } from "./cliente.service";
import { Cliente } from "./cliente";

import { global } from "../../_services/global";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: "app-list-cliente",
  templateUrl: "./list-cliente.component.html",
  styleUrls: ["./list-cliente.component.css"],
  providers: [UserService, ClienteService],
})
export class ListClienteComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  //public acreencia: EntidadAcreedora;
  public dataSource = null;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _userService: UserService,
    private _clienteService: ClienteService,
    private _dialogRef: MatDialogRef<ListClienteComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = "Listado de Clientes";
    this.identity = this._userService.getIdentity();
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "idcliente",
    "nombre",
    "numdoc",
    "nit",
    "fecha_nacimiento",
    "correo",
    "telefono",
    "tipo",
    /*'direccion',*/ "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getClientes();
  }

  getClientes() {
    console.log("getClientes");
    this._clienteService.getClientes().subscribe(
      (response) => {
        if (response.status == "success") {
          this.dataSource = new MatTableDataSource<Cliente>(response.clientes);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onSelected(element) {
    console.log("onSelected");
    this._dialogRef.close(<Cliente>element);
  }
}
