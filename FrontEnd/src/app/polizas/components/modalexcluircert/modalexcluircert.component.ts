import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { MatSnackBar } from "@angular/material/snack-bar";
import { NgxSpinnerService } from "ngx-spinner";
import { map } from "rxjs/operators";
import Swal from "sweetalert2";
import { CertificadoService } from "../../services/certificado.service";
import { PolizaService } from "../../services/poliza.service";

@Component({
  selector: "app-anular",
  templateUrl: "./modalexcluircert.component.html",
  styles: [],
})
export class ModalexcluircertComponent implements OnInit {
  motivosAnulacion$ = this.polizaService
    .motivosAnulacion()
    .pipe(
      map((motivo) =>
        motivo.filter((m) => m.catalogo === "MOTANUL" || "TIPOANU"),
      ),
    );

  formAnulacionCert = new FormGroup({
    certificado: new FormControl(""),
    inicio_vigencia: new FormControl(""),
    fin_vigencia: new FormControl(""),
    descripcion: new FormControl(""),
    fecha_exclusion: new FormControl("", [Validators.required]),
    corto_plazo: new FormControl("", [Validators.required]),
    motivo: new FormControl("", [Validators.required]),
    faltoPago: new FormControl(false),
    texto_anulacion: new FormControl(""),
  });

  constructor(
    private polizaService: PolizaService,
    @Inject(MAT_DIALOG_DATA)
    private data: {
      element: { idpoliza: string; detallePoliza };
    },
    private dialogRef: MatDialogRef<ModalexcluircertComponent>,
    private certificadoService: CertificadoService,
    private snackBar: MatSnackBar,
    private spinner: NgxSpinnerService,
  ) {}

  ngOnInit() {
    this.listen();
    if (this.data.element.detallePoliza) {
      this.patch(this.data.element.detallePoliza);
    }
  }

  listen() {
    this.formAnulacionCert
      .get("fecha_exclusion")
      .valueChanges.subscribe((value) => {
        const fecha = new Date(value)
          .toISOString()
          .split("T")[0]
          .split("-")
          .reverse()
          .join("/");
        const inicio = this.formAnulacionCert.get("inicio_vigencia").value;
        if (fecha === inicio) {
          this.formAnulacionCert
            .get("texto_anulacion")
            .setValue("INICIO DE VIGENCIA");
        } else {
          if (
            this.formAnulacionCert.get("texto_anulacion").value ===
            "INICIO DE VIGENCIA"
          ) {
            this.formAnulacionCert.get("texto_anulacion").setValue("");
          }
        }
      });
  }

  patch(data) {
    this.formAnulacionCert.patchValue({
      certificado: data.certificado,
      inicio_vigencia: data.iniVigencia.replaceAll("-", "/"),
      fin_vigencia: data.finVigencia.replaceAll("-", "/"),
      descripcion: data.descripcion,
      fecha_exclusion: new Date(),
    });
  }

  anular() {
    this.spinner.show();
    const data = {
      numcert: this.formAnulacionCert.get("certificado").value,
      fecha_exclusion: this.formAnulacionCert.get("fecha_exclusion").value,
      corto_plazo: this.formAnulacionCert.get("corto_plazo").value,
      motivo: this.formAnulacionCert.get("motivo").value,
      texto_anulacion: this.formAnulacionCert.get("texto_anulacion").value,
    };
    if (this.formAnulacionCert.valid) {
      this.certificadoService
        .anularCertificado(this.data.element.idpoliza, data)
        .subscribe({
          next: () => {
            this.spinner.hide();
            this.anulacionSuccessfully();
          },
        });
    } else {
      this.spinner.hide();
      this.openSnackBar("Faltan datos");
    }
  }

  private anulacionSuccessfully() {
    Swal.fire({
      icon: "success",
      title: "Certificado anulado",
      text: "El certificado ha sido anulada exitosamente",
      showConfirmButton: false,
      timer: 2000,
      timerProgressBar: true,
    }).then(() => {
      this.dialogRef.close();
    });
  }

  private openSnackBar(message: string) {
    this.snackBar.open(message, null, {
      duration: 2000,
    });
  }

  close() {
    this.dialogRef.close();
  }
}
