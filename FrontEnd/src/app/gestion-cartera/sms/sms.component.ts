import { Component, OnInit, Inject } from "@angular/core";
import {
  FormGroup,
  FormControl,
  FormBuilder,
  Validators,
} from "@angular/forms";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { GestionCarteraService } from "../gestion-cartera.service";
//import { EntidadAcreedora } from '../entidad-acreedora';
import { global } from "../../_services/global";
//ALERTS
import swal from "sweetalert2";
//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
// ABRIR DIALOGO DESDE UNA PANTALLA DE TIPO DIALOGO
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";

@Component({
  selector: "app-sms",
  templateUrl: "./sms.component.html",
  styleUrls: ["./sms.component.css"],
  providers: [GestionCarteraService],
})
export class SmsComponent implements OnInit {
  public page_title: string;
  public status: string;
  public url;

  public cod: number = 165;
  public codigoTelefono;
  public usuario;
  public tipo_operacion;

  private idRequerimiento: number = 0;

  constructor(
    private formBuilder: FormBuilder,
    public _gestionCarteraService: GestionCarteraService,
    private _dialogRef: MatDialogRef<SmsComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = "Envío de SMS";
    this.url = global.url;
  }

  public form = this.formBuilder.group(
    {
      idetracking: ["", Validators.required],
      telefono: ["", Validators.required],
      texto: [""],
      tipo_operacion: this.cod,
      usuario: JSON.parse(localStorage.getItem("currentUser")),
    },
    { updateOn: "change" }
  );

  ngOnInit(): void {
    this.initValuesForm();
  }

  /* onSaveForm(acreencia: EntidadAcreedora){
    //console.log('onSaveForm');    
    
  
        this._acreenciaService.create(acreencia).subscribe(
        response => {
          //console.log(response);
          if (response.status == 'success') {
            
            this.status = response.status;
            swal('Nueva Acreencia', 'La acreencia se ha creado correctamente', 'success');          
            //this._router.navigate(['/cajas']);

          }else{
            this.status = 'error';
            swal('Nueva Acreencia', 'La nueva acreencia no se ha creado correctamente', 'error');
          }        
        },
        error => {
          console.log('error',<any>error);
          this.status = 'error';
          swal('Nueva Acreencia', 'La acreencia no se ha creado correctamente', 'error');
        }
      );
    
    this.close();
  }*/

  close(): void {
  
    this._dialogRef.close(this.form.value);
  }

  private initValuesForm(): void {
    if (this.data.element) {
      this.form.patchValue({
        idetracking: this.data.element.idetracking,
        telefono: this.data.element.telefono,
        tipo_operacion: this.data.element.tipo_operacion,
      });
      this.idRequerimiento = this.data.element.id_requerimiento;
      this.getDataControl(this.idRequerimiento);
    }
  }

  getDataControl(data: any) {
    this._gestionCarteraService.getDataTelefonoEmail(data).subscribe(
      (response) => {
        const value = response.data[0];
        this.form.patchValue({
          telefono: value.tel1 || value.tel2 || "",
        });
      },
      (error) => {
        console.log(error);
      }
    );
  }

  envSms(form) {
    console.log(form);
    let id: number;
    id = this.data.element.no_gestion;
    this.codigoTelefono = +"502" + this.form.get("telefono").value;

    var jsonForm = {
      telefono: this.codigoTelefono,
      codigo: this.form.get("tipo_operacion").value,
      mensaje: this.form.get("texto").value,
    };

    this._gestionCarteraService.envioSMS(jsonForm).subscribe((response) => {
      if (response.status == "success") {
        swal.fire(
          "Mensaje enviado",
          "Mensaje enviado y guardado en bitacora",
          "success"
        );
        this._gestionCarteraService
          .createDetalleSms(form, id)
          .subscribe((response) => {
            if (response.status == "success") {
              console.log("listo");
            }
          });
        this.close();
      } else {
        swal.fire({ text: "El mensaje no fue enviado!", icon: "error" });
      }
    });
  }
}
