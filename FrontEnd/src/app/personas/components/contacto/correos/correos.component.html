<form [formGroup]="personaForm">
  <div class="container">
    <div formArrayName="correos">
      <div *ngFor="let c of correos.controls; let i = index">
        <div formGroupName="{{ i }}">
          <div class="remove-form">
            <button
              *ngIf="i > 0"
              mat-icon-button
              (click)="removeDocumentos(i)"
              class="bg-danger"
            >
              <mat-icon>remove</mat-icon>
            </button>
          </div>
          <div class="row">
            <mat-form-field class="col" appearance="outline">
              <mat-label>Tipo de Correo</mat-label>
              <mat-select formControlName="tipocorreo" name="tipocorreo">
                <mat-option
                  *ngFor="let tc of tiposCorreos$ | async"
                  [value]="tc.id"
                  >{{ tc.tipocorreo }}</mat-option
                >
              </mat-select>
            </mat-form-field>

            <mat-form-field class="col" appearance="outline">
              <input
                matInput
                name="correo"
                placeholder="Correo"
                formControlName="correo"
                #correo
                type="email"
                sanitizeUppercase
              />
            </mat-form-field>
            <mat-error>
              {{ message }}
            </mat-error>

            <mat-checkbox
              name="correoprincipal"
              formControlName="correoprincipal"
              class="mt-2"
            >
              Correo principal
            </mat-checkbox>
          </div>
        </div>
      </div>
    </div>
  </div>

  <button mat-flat-button (click)="moreCorreos()" color="accent" class="ml-2">
    <mat-icon>add</mat-icon>
  </button>
</form>
