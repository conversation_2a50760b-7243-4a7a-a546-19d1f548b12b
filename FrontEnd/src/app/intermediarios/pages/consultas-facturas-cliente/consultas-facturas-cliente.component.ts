import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";

import {
  ConsultasFacturasService,
  QueryParamsFacturas,
} from "../../services/consultas-facturas.service";

import { NgxSpinnerService } from "ngx-spinner";
import { FormBuilder } from "@angular/forms";
import { formatDate } from "src/app/_helpers";
import { FacturaComponent } from "src/app/components/facturas/factura.component";
import { TuiPdfViewerOptions } from "@taiga-ui/kit";
import { PolymorpheusComponent } from "@tinkoff/ng-polymorpheus";
import { Buttons } from "src/app/cotizaciones/components/slip-pdf/slip-pdf.component";
import { ActionsButtons } from "src/app/cotizaciones/components/slip-pdf/actions-buttons";

@Component({
  selector: "app-consultas-facturas-cliente",
  templateUrl: "./consultas-facturas-cliente.component.html",
  styleUrls: ["./consultas-facturas-cliente.component.css"],
})
export class ConsultasFacturasClienteComponent implements OnInit {
  public page_title: string;
  public status: string;
  public dataSource: any;
  public nit: string = "";
  public dpi: string = "";

  public resultados: any[] = [
    {
      descripcion: 10,
      valor: 10,
    },
    {
      descripcion: 25,
      valor: 25,
    },
    {
      descripcion: 50,
      valor: 50,
    },
  ];

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  displayedColumns: string[] = [
    "poliza",
    "numcert",
    "requerimiento",
    "dpi",
    "nit",
    "fechaFac",
    "serie_fel",
    "factura_fel",
    "actions",
  ];

  constructor(
    private ngxSpinnerService: NgxSpinnerService,
    private consultasFacturasService: ConsultasFacturasService,
    private formBuilder: FormBuilder
  ) {
    this.page_title = "Consulta de Facturas por NIT o DPI";
  }

  public form = this.formBuilder.group(
    {
      nit: [""],
      dpi: [""],
      cant: [25],
    },
    { updateOn: "change" }
  );
  range = this.formBuilder.group({
    start: [null],
    end: [null],
  });
  disableSearch: boolean = false;

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  @ViewChild(FacturaComponent) facturaComponent!: FacturaComponent;

  ngOnInit(): void {
    this.form.controls["nit"].valueChanges.subscribe(
      (value) => (this.disableSearch = value !== "")
    );

    this.form.controls["dpi"].valueChanges.subscribe(
      (value) => (this.disableSearch = value !== "")
    );
  }

  getPreviewFactura(element: any) {
    const options: TuiPdfViewerOptions<Buttons> = {
      label: "Factura",
      actions: new PolymorpheusComponent(ActionsButtons),
      data: [
        {
          text: "",
          color: "",
          onClick: () => {},
        },
      ],
    };
    const numeroFel = `${element.seriefel}_${element.numerodocumentofel}`;
    this.facturaComponent.show(options, numeroFel);
  }

  onDisablePreviewFactura(element: any) {
    return (element.ruta_fel || "") == "" ? true : false;
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onConsultar() {
    const data: QueryParamsFacturas = {
      nit: this.form.controls["nit"].value,
      dpi: this.form.controls["dpi"].value,
      total: this.form.controls["cant"].value,
      startDate: this.range.controls["start"].value
        ? formatDate(new Date(this.range.controls["start"].value))
        : null,
      endDate: this.range.controls["end"].value
        ? formatDate(new Date(this.range.controls["end"].value))
        : null,
    };

    this.ngxSpinnerService.show();
    this.consultasFacturasService.getConsultaFacturasCliente(data).subscribe(
      (response) => {
        this.dataSource = new MatTableDataSource(response ?? []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.error(error);
        this.ngxSpinnerService.hide();
      }
    );
  }
}
