import { HttpClient, HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import {
  IPolizaReclamo,
  IReclamos,
  IResponseData,
  ILval,
  IPayloadReclamos,
  IResponseData2,
  Pagination,
  IDetalleReclamo,
  IReclamoBitacoraPayload,
  ValEstadisticas,
  IPayloadEstadisticas,
  IReclamoEstadistica,
  IReclamosRequisitos,
  ICodigosRequisitos,
  IReclamosRequisitosPayload,
  ITerceroReclamo,
  IReclamoTerceroPayload,
  ICoberturas,
  IReservas,
  IReservasPayload,
  HistoryReservas,
} from "src/app/interface";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class ReclamosService {
  private readonly urlBase: string = environment.apiUrl;
  constructor(private http: HttpClient) {}

  getReclamos(
    pagination: Pagination
  ): Observable<{ data: IReclamos[]; total: number }> {
    let params = new HttpParams()
      .set("page", pagination.currentPage)
      .set("size", pagination.pageSize);

    return this.http.get(`${this.urlBase}/reclamos`, { params }).pipe(
      map((res: IResponseData<IReclamos>) => {
        return { data: res.data, total: res.meta.total };
      })
    );
  }

  getReclamoDetalle(
    idreclamo: string,
    numcert: string
  ): Observable<IDetalleReclamo> {
    return this.http
      .get(`${this.urlBase}/reclamo/detalle/${idreclamo}/${numcert}`)
      .pipe(map((res: IResponseData2<IDetalleReclamo>) => res.data));
  }

  getReclamoById(idreclamo: string): Observable<IReclamos> {
    return this.http
      .get(`${this.urlBase}/reclamo/${idreclamo}`)
      .pipe(map((res: IResponseData2<IReclamos>) => res.data));
  }

  getPolizas(): Observable<IPolizaReclamo[]> {
    return this.http
      .get(`${this.urlBase}/reclamos/polizas`)
      .pipe(map((res: IResponseData<IPolizaReclamo>) => res.data));
  }

  getPolizaById(idpoliza: number): Observable<IPolizaReclamo> {
    return this.http
      .get(`${this.urlBase}/reclamos/poliza/${idpoliza}`)
      .pipe(map((res: IResponseData2<IPolizaReclamo>) => res.data));
  }

  getAjustadores(): Observable<ILval[]> {
    return this.http.get<ILval[]>(`${this.urlBase}/reclamos/ajustadores`);
  }

  getBitacoraReclamo(idreclamo: string): Observable<any[]> {
    return this.http.get<any[]>(
      `${this.urlBase}/bitacora/reclamos/${idreclamo}`
    );
  }

  getBitacoraById(
    idbitacora_manual: string
  ): Observable<{ descripcion: string }> {
    return this.http.get<{ descripcion: string }>(
      `${this.urlBase}/bitacora/${idbitacora_manual}`
    );
  }

  getEstadisticaReclamos(idreclamo: string): Observable<IReclamoEstadistica[]> {
    return this.http
      .get(`${this.urlBase}/reclamo/estadisticas/${idreclamo}`)
      .pipe(map((res: IResponseData<IReclamoEstadistica>) => res.data));
  }

  getReclamoEstadiscaById(
    idreclamoEstadistica: string
  ): Observable<IReclamoEstadistica> {
    return this.http.get<IReclamoEstadistica>(
      `${this.urlBase}/estadisticas/reclamos/${idreclamoEstadistica}`
    );
  }

  getRequisitoById(
    idreclamoRequisito: string
  ): Observable<IReclamosRequisitos> {
    return this.http
      .get(`${this.urlBase}/requisito/reclamo/${idreclamoRequisito}`)
      .pipe(map((res: IResponseData2<IReclamosRequisitos>) => res.data));
  }

  getTercero(idreclamo: string): Observable<ITerceroReclamo[]> {
    return this.http
      .get(`${this.urlBase}/reclamos/terceros/${idreclamo}`)
      .pipe(map((res: IResponseData<ITerceroReclamo>) => res.data));
  }

  getTerceroById(idreclamoTercero: string): Observable<ITerceroReclamo> {
    return this.http
      .get(`${this.urlBase}/reclamos/tercero/${idreclamoTercero}`)
      .pipe(map((res: IResponseData2<ITerceroReclamo>) => res.data));
  }

  getTipoCliTerc() {
    return this.http
      .get(`${this.urlBase}/reclamos/cliterc`)
      .pipe(map((res: IResponseData<any>) => res.data));
  }

  getCodEst(): Observable<ValEstadisticas[]> {
    return this.http
      .get(`${this.urlBase}/estadisticas/codval`)
      .pipe(map((res: IResponseData<ValEstadisticas>) => res.data));
  }

  getValEst(codest: string): Observable<ValEstadisticas[]> {
    return this.http
      .get(`${this.urlBase}/estadisticas/codval/${codest}`)
      .pipe(map((res: IResponseData<ValEstadisticas>) => res.data));
  }

  getCodigoRequisitos(): Observable<ICodigosRequisitos[]> {
    return this.http
      .get(`${this.urlBase}/requisitos/codigos`)
      .pipe(map((res: IResponseData<ICodigosRequisitos>) => res.data));
  }

  getRequisitosReclamos(idreclamo: string): Observable<IReclamosRequisitos[]> {
    return this.http
      .get(`${this.urlBase}/requisitos/reclamo/${idreclamo}`)
      .pipe(map((res: IResponseData<IReclamosRequisitos>) => res.data));
  }

  getCoberturasPolizas(idreclamo: string): Observable<ICoberturas[]> {
    return this.http
      .get(`${this.urlBase}/reserva/reclamo/coberturas/${idreclamo}`)
      .pipe(map((res: IResponseData<ICoberturas>) => res.data));
  }

  getReservas(idreclamo: string): Observable<IReservas[]> {
    return this.http
      .get(`${this.urlBase}/reservas/reclamo/${idreclamo}`)
      .pipe(map((res: IResponseData<IReservas>) => res.data));
  }

  getReservaById(idreservacobertura: string): Observable<IReservas> {
    return this.http.get<IReservas>(
      `${this.urlBase}/reserva/reclamo/${idreservacobertura}`
    );
  }

  getOtherConcepts(): Observable<ILval[]> {
    return this.http
      .get(`${this.urlBase}/reservas/concepts`)
      .pipe(map((res: IResponseData2<ILval[]>) => res.data));
  }

  getReclamosCabina(): Observable<IReclamos[]> {
    return this.http.get(`${this.urlBase}/cabina`).pipe(
      map((res: IResponseData2<IReclamos[]>) => res.data)
    );
  }

  createReclamo(body: IPayloadReclamos): Observable<IPayloadReclamos> {
    return this.http.post<IPayloadReclamos>(`${this.urlBase}/reclamos`, body);
  }

  createBitacora(
    body: IReclamoBitacoraPayload
  ): Observable<IReclamoBitacoraPayload> {
    return this.http.post<IReclamoBitacoraPayload>(
      `${this.urlBase}/bitacora/reclamos`,
      body
    );
  }

  createEstadistica(
    body: IPayloadEstadisticas
  ): Observable<IPayloadEstadisticas> {
    return this.http.post<IPayloadEstadisticas>(
      `${this.urlBase}/estadisticas/reclamos`,
      body
    );
  }

  createRequisito(
    body: IReclamosRequisitosPayload
  ): Observable<IReclamosRequisitosPayload> {
    return this.http.post<IReclamosRequisitosPayload>(
      `${this.urlBase}/requisitos/reclamo`,
      body
    );
  }

  createTercero(
    body: IReclamoTerceroPayload
  ): Observable<IReclamoTerceroPayload> {
    return this.http.post<IReclamoTerceroPayload>(
      `${this.urlBase}/reclamos/terceros`,
      body
    );
  }

  createReserva(body: IReservasPayload): Observable<IReservasPayload> {
    return this.http.post<IReservasPayload>(
      `${this.urlBase}/reservas/reclamo`,
      body
    );
  }

  updateReclamo(
    body: IPayloadReclamos,
    idreclamo: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/reclamos/${idreclamo}`,
      body
    );
  }

  updateBitacora(
    body: { observaciones: string },
    idbitacora_manual: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/bitacora/${idbitacora_manual}`,
      body
    );
  }

  updateEstadistica(
    body: IPayloadEstadisticas,
    idreclamoEstadistica: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/estadisticas/reclamos/${idreclamoEstadistica}`,
      body
    );
  }

  updateRequisito(
    body: IPayloadEstadisticas,
    idreclamoRequisito: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/requisitos/reclamo/${idreclamoRequisito}`,
      body
    );
  }

  updateTercero(
    body: IReclamoTerceroPayload,
    idreclamotercero: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/reclamos/terceros/${idreclamotercero}`,
      body
    );
  }

  updateReserva(
    body: IReservasPayload,
    idreservacobertura: string
  ): Observable<boolean> {
    return this.http.put<boolean>(
      `${this.urlBase}/reservas/reclamo/${idreservacobertura}`,
      body
    );
  }

  createHistoryReserva(
    idreservacobertura: string,
    payload: { usuario: string }
  ): Observable<boolean> {
    return this.http.post<boolean>(
      `${this.urlBase}/reservas/history/${idreservacobertura}`,
      payload
    );
  }

  getHistoryReserva(
    idreclamo: string,
    idreserva: string
  ): Observable<HistoryReservas[]> {
    return this.http.get<HistoryReservas[]>(
      `${this.urlBase}/reservas/history/${idreclamo}/${idreserva}`
    );
  }

  activarReclamo(idreclamo: string): Observable<boolean> {
    return this.http.patch<boolean>(
      `${this.urlBase}/reclamo/${idreclamo}/activar`,
      {}
    );
  }

  deleteBitacora(idbitacora_manual: string): Observable<boolean> {
    return this.http.delete<boolean>(
      `${this.urlBase}/bitacora/reclamos/${idbitacora_manual}`
    );
  }

  deleteEstadistica(idreclamoEstadistica: string): Observable<boolean> {
    return this.http.delete<boolean>(
      `${this.urlBase}/estadisticas/reclamos/${idreclamoEstadistica}`
    );
  }

  deleteRequisito(idreclamoRequisito: string): Observable<boolean> {
    return this.http.delete<boolean>(
      `${this.urlBase}/requisitos/reclamo/${idreclamoRequisito}`
    );
  }

  deleteTercero(idreclamotercero: string): Observable<boolean> {
    return this.http.delete<boolean>(
      `${this.urlBase}/reclamos/terceros/${idreclamotercero}`
    );
  }

  deleteReserva(idreservacobertura: string): Observable<boolean> {
    return this.http.delete<boolean>(
      `${this.urlBase}/reservas/reclamo/${idreservacobertura}`
    );
  }

  // TODO: mover a un servicio independiente
  getMarcas() {
    return this.http
      .get(`${this.urlBase}/reclamos/auto/marca`)
      .pipe(map((res: IResponseData<any>) => res.data));
  }

  getLineas(marca: string) {
    return this.http
      .get(`${this.urlBase}/reclamos/auto/linea/${marca}`)
      .pipe(map((res: IResponseData<any>) => res.data));
  }

  getMonedas() {
    return this.http
      .get(`${this.urlBase}/reserva/monedas`)
      .pipe(map((res: IResponseData<any>) => res.data));
  }

  tipoCambio(query: string) {
    let params = new HttpParams().set("moneda", query);

    return this.http.get(`${this.urlBase}/reservas/miscellaneous`, { params });
  }

  tipoReservas() {
    return this.http
      .get(`${this.urlBase}/reservas/tipos`)
      .pipe(map((res: IResponseData2<any[]>) => res.data));
  }

  tipoEventos() {
    return this.http
      .get(`${this.urlBase}/reclamos/tipoevents`)
      .pipe(map((res: IResponseData2<any>) => res.data));
  }
}
