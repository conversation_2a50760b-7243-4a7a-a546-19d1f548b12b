import { Component, Inject, OnInit, ViewChild } from "@angular/core";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { MatSort } from "@angular/material/sort";
import { VwDetalleCotizacion } from "../../../interface/vwdetallecotizacion";
import { OfertaService } from "../../../services/oferta.service";
import { environment } from "src/environments/environment";

import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";

@Component({
  selector: "app-detalle",
  templateUrl: "./detalle.component.html",
  styleUrls: ["./detalle.component.css"],
})
export class DetalleComponent implements OnInit {
  displayedColumns: string[] = ["descripcion_repuesto", "observaciones"];
  dataSource = null;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  public page_title: string;
  public url;

  listadoDetalle: VwDetalleCotizacion[];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(
    private ofertaService: OfertaService,
    private dialogRef: MatDialogRef<DetalleComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = "Detalle Oferta";
    this.url = environment.apiUrl;
  }

  ngOnInit() {
    this.getDetalleCotizacionOferta(this.data.element.idcotizacion);
  }

  getDetalleCotizacionOferta(codigo: number) {
    this.ofertaService.getDetalleCotizacion(codigo).subscribe(
      (data) => {
        this.listadoDetalle = data.vwDetalleCotizacion;
        this.dataSource = new MatTableDataSource<VwDetalleCotizacion>(
          this.listadoDetalle
        );
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {}
    );
  }

  close(): void {
    this.dialogRef.close();
  }
}
