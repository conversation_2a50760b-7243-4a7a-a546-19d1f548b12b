import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { global } from "src/app/_services/global";

@Injectable({
  providedIn: "root",
})
export class ListChequeService {
  public url: string;

  constructor(private _http: HttpClient) {
    this.url = global.url;
  }

  findChequeRechazadoByIdPagador(id, nNumRelingTemp): Observable<any> {
    let headers = new HttpHeaders().set("Content-Type", "application/json");
    return this._http.get(this.url + "cheque-rechazado/findChequeRechazadoByIdPagador/" + id + "/" + nNumRelingTemp, { headers: headers });
  }

  onSaveForm(numSelected, id): Observable<any>{        
    let params = numSelected;        
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    let tipo = 'CHR';
    return this._http.post(this.url+'tempfacturarequerimiento/store/'+id+'/'+tipo, params, {headers: headers});
  }
}
