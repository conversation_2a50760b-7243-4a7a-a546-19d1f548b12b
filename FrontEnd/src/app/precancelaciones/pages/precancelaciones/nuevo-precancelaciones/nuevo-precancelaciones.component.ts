import { Component, OnInit, ViewChild } from "@angular/core";
import { Location } from "@angular/common";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";

import Swal from "sweetalert2";
import { NgxSpinnerService } from "ngx-spinner";
import { PrecancelacionesService } from "../../../services/precancelaciones.service";

@Component({
  selector: "app-nuevo-precancelaciones",
  templateUrl: "./nuevo-precancelaciones.component.html",
  styleUrls: ["./nuevo-precancelaciones.component.css"],
})
export class NuevoPrecancelacionesComponent implements OnInit {
  public page_title: string;
  public status: string;
  public dataSource: any;
  public idTipo: number = 0;
  public tipoConfiguracion: any = [];
  public tipoData: any = Object.create(null);

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private ngxSpinnerService: NgxSpinnerService,
    private precancelacionesService: PrecancelacionesService,
    private location: Location
  ) {
    this.page_title = "Nuevo registro";
  }

  public displayedColumns: string[] = [
    "morosidad",
    "poliza",
    "id_intermediario",
    "email_corredor",
    "cobrador",
    "operacion",
    "dias_vence",
    "fecvenc",
    "nopago",
    "email_asegurado",
    "tel1",
    "tel2",
    "nombreaseurado",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  ngOnInit(): void {
    this.getTipoMorosidad();
  }

  getPreviewDetallePrecancelaciones(id: number) {
    this.ngxSpinnerService.show();
    this.precancelacionesService.getPreviewAvisoMorosidad(id).subscribe(
      (response) => {
        this.dataSource = new MatTableDataSource<any>(response.data || []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  getTipoMorosidad() {
    this.precancelacionesService
      .getTipoConfiguracionMorosidad()
      .subscribe((response) => {
        this.tipoConfiguracion = response.data || [];
      });
  }

  onChange(value) {
    this.idTipo = value;
    this.tipoData = this.filterData(this.idTipo);
  }

  onPreview() {
    this.getPreviewDetallePrecancelaciones(this.idTipo);
  }

  onProcess() {
    this.ngxSpinnerService.show();
    this.precancelacionesService
      .postConfiguracionMorosidadProcess(this.idTipo)
      .subscribe(
        (response) => {
          Swal.fire({
            text: "Proceso finalizado correctamente!",
            icon: "success",
          });
          this.ngxSpinnerService.hide();
          this.onBack();
        },
        (error) => {
          console.log(<any>error);
          Swal.fire({
            text: "Ha fallado el proceso. Vuelva a intentarlo.",
            icon: "error",
          });
          this.ngxSpinnerService.hide();
        }
      );
  }

  filterData(id) {
    const filter = this.tipoConfiguracion.filter(
      (t: any) => t.id_configuracion_morosidad === id
    );
    return filter[0];
  }

  onBack() {
    this.location.back();
  }
}
