<form [formGroup]="form">
  <div class="page_title">
    <h1>{{page_title}}</h1>
    <p> {{this.data.title}} </p>
  </div>
  <hr />

  <div class="alert alert-success col-md-5 mt-3" *ngIf="status == 'success'">
    El registro se ha guardado correctamente.
  </div>

  <div class="alert alert-danger col-md-5" *ngIf="status == 'error'">
    El registro no se ha completado correctamente, vuelve a intentarlo.
  </div>


  <mat-grid-list cols="2" rowHeight="100px" gutterSize="1px">

    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Nombre Tercero" name="nombre" formControlName="nombre" readonly>
      </mat-form-field>
    </mat-grid-tile>
    <mat-grid-tile>
      <button mat-flat-button color="primary" (click)="openDialogo()" matTooltip="Lista de Valores"
        matTooltipClass="tooltip-red">
        <mat-icon>youtube_searched_for</mat-icon>
      </button>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Nombre Usuario" name="usuario" formControlName="usuario" readonly>
      </mat-form-field>
    </mat-grid-tile>
    <mat-grid-tile>
      <button mat-flat-button color="primary" (click)="openDialogoUsuario()" matTooltip="Lista de Valores"
        matTooltipClass="tooltip-red">
        <mat-icon>youtube_searched_for</mat-icon>
      </button>
    </mat-grid-tile>  

    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Caja Default" name="desccaja" formControlName="desccaja" readonly>
      </mat-form-field>
    </mat-grid-tile>
    <mat-grid-tile>
      <button mat-flat-button color="primary" (click)="openDialogoCaja()" matTooltip="Lista de Valores"
        matTooltipClass="tooltip-red">
        <mat-icon>youtube_searched_for</mat-icon>
      </button>
    </mat-grid-tile>  

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Fecha Ingreso</mat-label>
        <input matInput placeholder="Fecha Ingreso" name="fecing" formControlName="fecing" readonly>       
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-label>Fecha Anulación</mat-label>
        <input matInput placeholder="Fecha Anulación" name="fecanu" formControlName="fecanu" readonly>       
      </mat-form-field>
    </mat-grid-tile> 
    
    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Estado" name="stscajero" formControlName="stscajero" readonly>
      </mat-form-field>
    </mat-grid-tile>    

    <mat-grid-tile>
      <mat-dialog-actions>
        <button mat-flat-button color="primary" (click)="close()">
          <mat-icon>highlight_off</mat-icon>Cerrar
        </button>
      </mat-dialog-actions>
      &nbsp;&nbsp;
      <mat-dialog-actions>
        <button mat-flat-button color="accent" (click)="onSaveForm(form.value)" [disabled]="form.invalid">
          <mat-icon>save</mat-icon>Guardar
        </button>
      </mat-dialog-actions>
    </mat-grid-tile>
  </mat-grid-list>

</form>