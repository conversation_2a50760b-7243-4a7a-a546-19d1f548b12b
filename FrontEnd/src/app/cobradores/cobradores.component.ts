import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from "@angular/core";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { Subscription } from "rxjs";

import { ActivatedRoute, Router } from "@angular/router";
import { filter } from "rxjs/operators";
import Swal from "sweetalert2";
import { ListenService } from "../_services/listen.service";
import { ModalService } from "../_services/modal.service";
import { ICobrador } from "../interface";
import { FormularioEjecutivoComponent } from "./formulario-ejecutivo/formulario-ejecutivo.component";
import { FormularioCobradorComponent } from "./formulario/formulario.component";
import { ModalDesactivarComponent } from "./modal-desactivar/modal-desactivar.component";
import { CobradoresService } from "./services/cobradores.service";

@Component({
  selector: "app-cobradores",
  templateUrl: "./cobradores.component.html",
  styleUrls: ["./cobradores.component.css"],
})
export class CobradoresComponent implements OnInit, OnDestroy {
  public pageTitle: string = "";
  displayedColumns: string[] = [
    "id",
    "codigo_cobrador",
    "nombre",
    "apellido",
    "estado",
    "actions", // more columns,
  ];
  dataSource!: MatTableDataSource<ICobrador>;
  loading: boolean = false;
  message: string = "";
  subscription!: Subscription;

  constructor(
    private cobradoresService: CobradoresService,
    private modalService: ModalService,
    private listenService: ListenService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    this.pageTitle = "Listado de cobradores";
  }
  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort!: MatSort;

  ngOnInit(): void {
    this.getCobradores();
    this.subscription = this.listenService.ok$.subscribe((v) =>
      v ? this.getCobradores() : "",
    );

    this.route.queryParams
      .pipe(
        filter((params) => {
          return params.add === "cobradornotfound";
        }),
      )
      .subscribe(() => {
        this.onNew();
      });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getCobradores() {
    this.cobradoresService.getAllCobradores().subscribe({
      next: (value) => {
        this.dataSource = new MatTableDataSource(value);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.loading = false;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onNew() {
    this.modalService.openDialog<FormularioCobradorComponent>({
      component: FormularioCobradorComponent,
      title: "Nuevo cobrador",
      element: null,
    });
  }

  asignarEjecutivo(idcobrador: number) {
    this.modalService.openDialog<FormularioEjecutivoComponent>({
      title: "Asignar intermediario",
      component: FormularioEjecutivoComponent,
      element: { idcobrador },
    });
  }

  desactivarActivarCobrador(id: number, estado: string) {
    if (estado === "ACT") {
      this.desactivarCobrador(id);
    } else {
      this.activarCobrador(id);
    }
  }

  desactivarCobrador(id: number) {
    this.cobradoresService.desactivarCobrador(id).subscribe((data) => {
      Swal.fire({
        title: "Cobrador desactivado",
        text: "El cobrador ha sido desactivado",
        icon: "success",
        confirmButtonText: "Aceptar",
      });
      if (data.length > 0) {
        this.listarIntermediarios(data);
      }
      this.getCobradores();
    });
  }

  listarIntermediarios(data) {
    this.modalService.openDialog<ModalDesactivarComponent>({
      title: "Listado de Intermediarios",
      component: ModalDesactivarComponent,
      element: data,
      disableAutoClose: true,
    });
  }

  activarCobrador(id: number) {
    this.cobradoresService.activarCobrador(id).subscribe(() => {
      this.getCobradores();
    });
  }

  onEdit(element: ICobrador) {
    this.modalService.openDialog<FormularioCobradorComponent>({
      component: FormularioCobradorComponent,
      title: "Editar cobrador",
      element,
    });
  }

  datosGenerales(id: number) {
    this.router.navigate(["/cobrador/detalle", id]);
  }
}
