import { Component, Inject, OnInit } from "@angular/core";
import { FormBuilder, FormControl, FormGroup } from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import Swal from "sweetalert2";
import { ListenService } from "../../_services/listen.service";
import { IEjecutivo, ITipoEjecutivo } from "src/app/interface";
import { EjecutivoServices } from "../ejecutivo.service";

@Component({
  selector: "app-formulario",
  templateUrl: "./formulario.component.html",
  styleUrls: ["./formulario.component.css"],
})
export class FormularioComponent implements OnInit {
  public loading: boolean = false;
  public pageTitle: string = "";
  public indexTab: number = 0;
  public ejecutivoForm!: FormGroup;
  public idPersona!: number;
  public tiposEjecutivos: ITipoEjecutivo[];
  options = {
    minimize: true,
  };

  constructor(
    private _dialogRef: MatDialogRef<FormularioComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { title: string; element: any },
    private fb: FormBuilder,
    private listenService: ListenService,
    private ejecutivoService: EjecutivoServices
  ) {}

  ngOnInit(): void {
    this.pageTitle = this.data.title;
    this.buildForm();
    this.getTiposEjecutivos();
  }

  buildForm() {
    this.ejecutivoForm = this.fb.group({
      idtipoejecutivo: new FormControl(""),
      codigo: new FormControl(""),
    });
  }

  close(): void {
    this._dialogRef.close();
  }

  receiveIdPersona(e: number) {
    this.idPersona = e;
  }

  onNewTab() {
    this.indexTab++;
  }

  saveForm() {
    const newEjecutivo: IEjecutivo = {
      ...this.ejecutivoForm.value,
      idpersona: this.idPersona,
    };
    this.ejecutivoService.createEjecutivo(newEjecutivo).subscribe({
      next: () => {
        this.loading = false;
        Swal.fire("Registro guardado", "", "success").then(() => {
          this.close();
          this.listenService.reloadComponent(true);
        });
      },
      error: (e) => {
        Swal.fire("Error", "comuniquese con un administrador", "error").then(
          () => {
            this.close();
          }
        );
        console.error(e);
      },
    });
  }

  getTiposEjecutivos() {
    this.ejecutivoService.getTiposEjecutivos().subscribe({
      next: (value) => {
        this.tiposEjecutivos = value;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }
}
