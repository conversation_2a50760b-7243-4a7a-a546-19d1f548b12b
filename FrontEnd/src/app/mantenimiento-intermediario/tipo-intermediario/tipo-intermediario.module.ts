import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { ComponentsModule } from 'src/app/components/components.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { TipoIntermediarioComponent } from './tipo-intermediario.component';
import { FormularioTipoIntermediarioComponent } from './modal-tipo-intermediario/formulario-tipo-intermediario/formulario-tipo-intermediario.component';
import { TipoIntermediarioRoutingModule } from './tipo-intermediario-routing.module';

@NgModule({
  declarations: [
    TipoIntermediarioComponent,
    FormularioTipoIntermediarioComponent
  ],
  imports: [
    CommonModule,
    TipoIntermediarioRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatDividerModule,
    NgxSkeletonLoaderModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    ComponentsModule,
    SharedModule
  ]
})
export class TipoIntermediarioModule { }
