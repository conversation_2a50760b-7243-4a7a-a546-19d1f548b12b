<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <button-back class="ml-2"></button-back>
      <div class="mat-elevation-z8 p-2">
        <div class="page_title"><h1>Ficha de Contacto</h1></div>
        <mat-divider></mat-divider>
        <mat-stepper
          [linear]="true"
          #stepper
          (selectionChange)="selectionChange($event)"
        >
          <mat-step label="Dato generales">
            <app-formulario-ficha-contacto
              (idSaved)="setIdCreacion($event)"
            ></app-formulario-ficha-contacto>
          </mat-step>
          <mat-step label="Datos Contacto">
            <app-contacto-telefono
              [telefonos]="datosTelefono"
              [idcreacion]="idCreacion"
              (recharge)="recargar()"
            ></app-contacto-telefono>
            <app-contacto-correo
              [correos]="datosCorreo"
              [idcreacion]="idCreacion"
              (recharge)="recargar()"
            ></app-contacto-correo>
            <app-contacto-direccion
              [direcciones]="datosDireccion"
              [idcreacion]="idCreacion"
              (recharge)="recargar()"
            ></app-contacto-direccion>
            <app-contacto-cuenta
              [cuentas]="datosCuenta"
              [idcreacion]="idCreacion"
              (recharge)="recargar()"
            ></app-contacto-cuenta>
          </mat-step>
        </mat-stepper>
        <div
          [ngClass]="
            stepper.selectedIndex === 1
              ? 'd-flex justify-content-between m-2'
              : 'd-flex justify-content-end m-2'
          "
        >
          <button
            mat-flat-button
            color="accent"
            (click)="stepper.previous()"
            *ngIf="stepper.selectedIndex > 0"
          >
            <mat-icon>arrow_back</mat-icon>
          </button>
          <button
            mat-flat-button
            color="accent"
            (click)="stepper.next()"
            *ngIf="stepper.selectedIndex < 1"
            [disabled]="!idCreacion"
          >
            <mat-icon>arrow_forward</mat-icon>
          </button>
          <button
            *ngIf="stepper.selectedIndex === 1"
            mat-flat-button
            color="accent"
            (click)="save()"
          >
            <mat-icon>save</mat-icon>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
