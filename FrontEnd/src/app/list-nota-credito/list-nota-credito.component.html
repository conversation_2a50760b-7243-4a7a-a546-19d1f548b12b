<div class="mat-elevation-z8">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr />

  <mat-form-field>
    <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
  </mat-form-field>

  <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

    <!-- Id Column -->
    <ng-container matColumnDef="idnotacred">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Id </th>
      <td mat-cell *matCellDef="let element"> {{element.idnotacred}} </td>
    </ng-container>

     <!-- Pagador Column -->
     <ng-container matColumnDef="nombre_pagador">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Pagador </th>
      <td mat-cell *matCellDef="let element"> {{element.nombre_pagador}} </td>
    </ng-container>

    <!-- Tipo Column -->
    <ng-container matColumnDef="tipo">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Tipo </th>
      <td mat-cell *matCellDef="let element"> {{element.tipo}} </td>
    </ng-container>

    <!-- Días Vence Column -->
    <ng-container matColumnDef="dias_vence">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Días Vence </th>
      <td mat-cell *matCellDef="let element"> {{element.dias_vence}} </td>
    </ng-container>
    
    <!-- Fecha Estado Column -->
    <ng-container matColumnDef="fechaestado">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> F.Estado </th>
      <td mat-cell *matCellDef="let element"> {{element.fechaestado | date:'dd/MM/yyyy'}} </td>
    </ng-container>


    <!-- Monto Column -->
    <ng-container matColumnDef="totalnotacred">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto </th>
      <td mat-cell *matCellDef="let element"> {{element.totalnotacred | currency :' ':'code'}} </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

  </table>
  <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
</div>