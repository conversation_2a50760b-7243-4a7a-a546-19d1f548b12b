import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { TipoRegimenService } from 'src/app/mantenimiento-intermediario/Services';
import { ITipoRegimen } from 'src/app/interface/tipoRegimen.interface';

@Component({
  selector: 'app-formulario-tipo-regimen',
  templateUrl: './formulario-tipo-regimen.component.html',
})
export class FormularioTipoRegimenComponent implements OnInit {
  formRegimen = new FormGroup({
    codregimen: new FormControl('', [Validators.required, Validators.maxLength(10)]),
    descripcion: new FormControl('', Validators.required),
  });

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { data: ITipoRegimen; id: number } },
    private tipoRegimenService: TipoRegimenService,
    private dialogRef: MatDialogRef<FormularioTipoRegimenComponent>
  ) {}

  ngOnInit(): void {
    this.asignarValores();
  }

  asignarValores(): void {
    if (this.data?.element?.data) {
      this.formRegimen.patchValue({
        codregimen: this.data.element.data.codregimen,
        descripcion: this.data.element.data.descripcion,
      });
    }
  }

  guardar(): void {
    const body: Partial<ITipoRegimen> = {
      codregimen: this.formRegimen.get('codregimen')?.value,
      descripcion: this.formRegimen.get('descripcion')?.value,
    };
  
    const esEdicion = !!this.data?.element?.data;
  
    const accion = esEdicion
      ? this.tipoRegimenService.updateTipoRegimen(this.data.element.id, body)
      : this.tipoRegimenService.createTipoRegimen(body);
  
      accion.subscribe(
        (res) => {
          if (!res?.success) {
            this.mostrarMensaje(
              res.message || 'Ocurrió un error en la operación',
              'error',
              'Error'
            );
            return;
          }
          this.mostrarMensaje(
            res?.message || 'Operación realizada con éxito',
            'success',
            'Éxito'
          );
          this.dialogRef.close();
        },
        (err) => {
          this.mostrarMensaje(
            err.error?.message || 'Ocurrió un error al guardar',
            'error',
            'Error'
          );
        }
      );
  }
  mostrarMensaje(text: string, icon: any, title: string): void {
    Swal.fire({
      title,
      text,
      icon,
      confirmButtonText: 'Aceptar',
    });
  }
}
