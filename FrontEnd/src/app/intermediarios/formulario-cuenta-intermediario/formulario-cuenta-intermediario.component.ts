import { Component, Input, OnInit } from "@angular/core";
import { FormGroup } from "@angular/forms";
import { CuentaIntermediarioService } from "../services/cuenta-intermediario.service";

@Component({
  selector: "app-formulario-cuenta-intermediario",
  templateUrl: "./formulario-cuenta-intermediario.component.html",
  styleUrls: ["./formulario-cuenta-intermediario.component.css"],
})
export class FormularioCuentaIntermediarioComponent implements OnInit {
  @Input() formulario: FormGroup;
  @Input() idIntermediario: number;

  dataBanco;

  constructor(private cuentaIntermediarioService: CuentaIntermediarioService) {}

  ngOnInit() {
    if (this.idIntermediario) this.getData();
    this.getBancos();
  }

  getData() {
    this.cuentaIntermediarioService
      .getCuentaByIntermediario(this.idIntermediario)
      .subscribe((data) => {
        this.buildForm(data[0]);
      });
  }

  getBancos() {
    this.cuentaIntermediarioService.getBancos().subscribe((data) => {
      this.dataBanco = data;
    });
  }

  buildForm(data) {
    this.formulario.patchValue(data);
  }
}
