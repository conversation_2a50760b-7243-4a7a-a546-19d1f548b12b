import {
  Component,
  OnInit,
  ViewChild /*, ChangeDetectorRef*/,
} from "@angular/core";
import { Router, ActivatedRoute, Params } from "@angular/router";
import {
  FormGroup,
  FormControl,
  FormBuilder,
  Validators,
} from "@angular/forms";
import { CierreVidaService } from "./cierre-vida.service";
import { Cierre_Poliza_Vida } from "./cierrePolizaVida";

import { global } from "../_services/global";
import swal from "sweetalert2";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: "app-cierre-vida",
  templateUrl: "./cierre-vida.component.html",
  styleUrls: ["./cierre-vida.component.css"],
  providers: [CierreVidaService],
})
export class CierreVidaComponent implements OnInit {
  public page_title: string;
  public status: string;
  public url;
  public listProductos;
  public dataSource = null;
  public isDisabled: boolean = true;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _cierreVidaService: CierreVidaService,
    private formBuilder: FormBuilder //private changeDetectorRefs: ChangeDetectorRef
  ) {
    this.page_title = "Proceso de Cierre de Vida";
    //this.identity = this._userService.getIdentity();
    this.url = global.url;
  }

  public form = this.formBuilder.group(
    {
      producto: ["VAL", Validators.required],
      fecha: ["", Validators.required],
    },
    { updateOn: "change" }
  );

  ngOnInit() {}

  doGenerarCierreVida(form) {
    console.log("doGenerarCierreVida");

    swal
      .fire({
        title: "Proceso de Cierre",
        text: "¿Deseas Ejecutar el Proceso de Cierre?",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete) {
          this._cierreVidaService.doGenerarCierre(form).subscribe(
            (response) => {
              if (response.status == "success") {
                swal.fire({
                  text: "Proceso finalizado correctamente!",
                  icon: "success",
                });
              } else {
                //this.status = 'error';
                swal.fire(
                  "Proceso de Cierre",
                  "No se ha podido ejecutar el proceso de cierre de vida, reporte al área de Sistemas",
                  "error"
                );
              }
            },
            (error) => {
              console.log(<any>error);
              swal.fire(
                "Generación de Prestamos de Vida Individual",
                "No se ha podido generar los prestamos automáticos, reporte al área de Sistemas",
                "error"
              );
              //this.status = 'error';
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }
}
