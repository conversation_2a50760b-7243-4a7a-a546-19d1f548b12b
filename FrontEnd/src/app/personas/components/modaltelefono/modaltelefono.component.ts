import { TelefonosService } from "./../../services/telefonos.service";
import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ITelefono, ITelefonos } from "src/app/interface";
import Swal from "sweetalert2";

@Component({
  selector: "app-modaltelefono",
  templateUrl: "./modaltelefono.component.html",
  styleUrls: ["./modaltelefono.component.css"],
})
export class ModaltelefonoComponent implements OnInit {
   formularioPersonas = new FormGroup({
    idtipotelefono: new FormControl("TIPO TELEFONO"),
    numtelefono: new FormControl(""),
    telefonoprincipal: new FormControl(false),
  });

  disabledIndex = true;

  constructor(
    @Inject(MAT_DIALOG_DATA) 
    private data: { element: { data: ITelefono; idPersona: string } },
    private telefonosService: TelefonosService,
    private dialogRef: MatDialogRef<ModaltelefonoComponent>
  ) {}

  ngOnInit(): void {
    if (this.data.element.data)this.cargarFormulario(this.data.element.data);
  }
  private cargarFormulario(data: ITelefono) {
    this.formularioPersonas.patchValue({
      idTelefono: data.id,
      idtipotelefono: data.idtipotelefono.toString(),
      numtelefono: data.numtelefono,
      telefonoprincipal: data.telefonoprincipal,
    });
  }
  createOrUpdateDataTelPersona() {
    const swalWithBootstrapButtons = Swal.mixin({
      customClass: {
        confirmButton: "btn btn-success",
      },
      buttonsStyling: true,
    });
    //obtener datos para actualizar
    if (this.data.element.data) {
      const dataTel = {
        ...this.formularioPersonas.value,
      };

    const idTelefono = this.data.element.data.id;

    swalWithBootstrapButtons
      .fire({
        title: "ACTUALIZAR TELEFONO DE LA PERSONA",
        text: "Esta seguro de realizar el cambio?",
        icon: "warning",
        showDenyButton: true,
        confirmButtonText: "Grabar",
        denyButtonText: "Cancelar",
        reverseButtons: true,
      })
      .then((result) => {
        if (result.isConfirmed) {
          this.telefonosService
            .updateTelefono(dataTel, idTelefono.toString())
            .subscribe({
              next: () => {
                Swal.fire("Registro actualizado", "", "success");
                this.dialogRef.close(true);
              },
              error: (err) => {
                Swal.fire("Error al actualizar el registro",err.error.message,"error");
                this.dialogRef.close();
              }
            }
          );         
        }
      });
  } else {
    const dataTel = {
      ...this.formularioPersonas.value,
    };
    delete dataTel["idTelefono"];
    const telefonos = [];

    telefonos.push(dataTel);

    const data: ITelefonos = {
      idpersona: Number(this.data.element.idPersona),
      telefonos,
    };

    swalWithBootstrapButtons
      .fire({
        title: "AGREGAR TELEFONO DE LA PERSONA",
        text: "Esta seguro de realizar el cambio?",
        icon: "warning",
        showDenyButton: true,
        confirmButtonText: "Grabar",
        denyButtonText: "Cancelar",
        reverseButtons: true,
      })
      .then((result) => {
        if (result.isConfirmed) {
          this.telefonosService.crearTelefono(data).subscribe();
          Swal.fire("Registro agregado", "", "success");
          this.dialogRef.close(true);
        }
      });
  }
  }
}
