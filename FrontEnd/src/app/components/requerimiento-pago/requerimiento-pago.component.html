<div>
  <h3><b>Requerimiento de pago</b></h3>
  
  <div class="row flex-detalle">
    <icore-table
      [columns]="displayedColumns"
      [data]="detRequerimiento"
      [pageSize]="10"
      [showPaginator]="false"
      (onClick)="showDetRequerimiento($event)"
      class="cursor-pointer"
    >
    </icore-table>

    <div>
      <app-rubro-requerimiento
        *ngIf="detalleRequerimiento$ | async as detalle; else noData"
        [requerimiento]="detalle"
        [certificados]="certificados"
      ></app-rubro-requerimiento>

      <ng-template #noData>
        <div class="content-no-data">
          <alert-select
            message="Selecciona un requerimiento de pago para mostrar el detalle"
          ></alert-select>
        </div>
      </ng-template>
    </div>
  </div>

  <div *ngIf="detRequerimiento">
    <button
      mat-flat-button
      color="primary"
      type="button"
      (click)="cambiarPlan()"
    >
      <mat-icon>swap_horiz</mat-icon> Cambio de Plan
    </button>
  </div>
</div>
