.row {
    flex-wrap: nowrap !important;
    padding-left: 10px;
  }
  input {
    font-size: 1.3rem;
    
  }
  
  @media (min-width: 580px) {
    .col-md-11 {
      flex: 0 0 91.6666666667%;
      max-width: 91.6666666667%;
    }
    .col-md-4 {
      flex: 0 0 33%;
      max-width: 33%;
    }
    .col-md-2 {
      flex: 0 0 16%;
      max-width: 16%;
    }
    .col-md-3 {
      flex: 0 0 25%;
      max-width: 25%;
    }
  
    .btn-primary {
      border-color: #ffffff !important;
      /*background-color: #0b5ef6 !important;
      color: #FFFFFF;*/
    }
    
  }
  .container-btn-new {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    margin: 1rem -1rem;
    bottom: 100px !important;
}

:host ::ng-deep.content .content-wrapper {
    padding: 1.1rem;
  }