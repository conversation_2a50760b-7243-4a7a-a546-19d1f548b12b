<mat-list *ngIf="cuentas">
  <div mat-subheader>Cuentas</div>
  <mat-list-item *ngFor="let cuenta of cuentas" style="width: 350px">
    <div
      class="d-flex flex-row-reverse cursor-pointer ml-2"
      (click)="createOrEdit(cuenta)"
    >
      <i style="font-size: 20px" class="feather ft-edit"></i>
    </div>

    <div matListItemTitle class="mr-2">
      {{ cuenta.nombre }} - {{ cuenta.numero_cuenta }}
    </div>

    <button mat-icon-button (click)="deleteCuenta(cuenta.id)">
      <mat-icon>delete</mat-icon>
    </button>
  </mat-list-item>

  <mat-list-item *ngIf="cuentas.length === 0" style="width: 600px">
    <div
      class="d-flex flex-row-reverse cursor-pointer ml-2"
      (click)="createOrEdit(cuenta)"
    >
      <i style="font-size: 20px" class="feather ft-edit"></i>
    </div>
    <i class="feather ft-minus"></i>
    <div matListItemTitle>
      No hay cuentas registradas. Haz clic para agregar una nueva.
    </div>
  </mat-list-item>
</mat-list>

<div class="d-flex justify-content-end">
  <button mat-flat-button (click)="createOrEdit()" color="accent" class="">
    <mat-icon>add</mat-icon>
  </button>
</div>
