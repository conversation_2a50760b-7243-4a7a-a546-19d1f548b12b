<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>
        <div class="container-btn-new">
        
          <div class="btn-new">
              <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
              </button>
          </div>
        </div>

        <div class="table-responsive" perfectScrollbar>
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="mat-elevation-z8"
        >
          <!-- idrecuperacion Column -->
          <ng-container matColumnDef="idrecuperacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              ID Recuperacion
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.IdRecuperacion }}
            </td>
          </ng-container>

          <!-- estado Column -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
            <td mat-cell *matCellDef="let element">{{ element.estado }}</td>
          </ng-container>

          <!-- fecha_ingreso Column -->
          <ng-container matColumnDef="fecha_ingreso">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Fecha Ingreso
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.fecha_ingreso }}
            </td>
          </ng-container>

          <!-- tipo Column -->
          <ng-container matColumnDef="tipo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tipo
            </th>
            <td mat-cell *matCellDef="let element">{{ element.tipo }}</td>
          </ng-container>

          <!-- reclamo Column -->
          <ng-container matColumnDef="reclamo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Reclamo
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.poliza_aseguradora }}
            </td>
          </ng-container>

          <!-- tipo_riesgo Column -->
          <ng-container matColumnDef="tipo_riesgo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo Riesgo</th>
            <td mat-cell *matCellDef="let element">
              {{ element.tiporiesgo }}
            </td>
          </ng-container>

          <!-- caso Column -->
          <ng-container matColumnDef="caso">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Caso
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.caso }}
            </td>
          </ng-container>

          <!-- recuperador Column -->
          <ng-container matColumnDef="recuperador">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Recuperador</th>
            <td mat-cell *matCellDef="let element">{{ element.nombre_recuperador }}</td>
          </ng-container>

          <!-- titular Column -->
          <ng-container matColumnDef="titular">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Titular</th>
            <td mat-cell *matCellDef="let element">{{ element.nombre_comprador }}</td>
          </ng-container>

          <!-- datos_vehiculos Column -->
          <ng-container matColumnDef="datos_vehiculos">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Datos Vehiculo</th>
            <td mat-cell *matCellDef="let element">{{ element.datos_vehiculo }}</td>
          </ng-container>

          <!-- Action Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-raised-button
                color="basic"
                (click)="onDetail(element)"
                matTooltip="Detalle"
                matTooltipClass="tooltip-red"
              >
                 <mat-icon>subject</mat-icon>
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="primary"
                (click)="onEdit(element)"
                matTooltip="Editar"
                matTooltipClass="tooltip-red"
              >
                 <mat-icon>edit</mat-icon>
              </button>
              &nbsp;
              <!--<button
                mat-flat-button
                color="accent"
                (click)="onSendEstadoCuenta(element)"
                matTooltip="Generar Estado de Cuenta"
                matTooltipClass="tooltip-red"
              >
                <mat-icon>description</mat-icon>
              </button>
              &nbsp;-->
            <button mat-raised-button color="primary" 
                    (click)="onActivate(element)" 
                    matTooltip="Activar" 
                    matTooltipClass="tooltip-red"
                    [disabled]="element.estado === 'ACT'"
                    >
              <mat-icon>check</mat-icon>
            </button>

              &nbsp;
         
          
              <button
                mat-flat-button
                color="accent"
                (click)="openDialogoOperUsuarioAcceso(element)"
                matTooltip="Descuento"
                matTooltipClass="tooltip-red"
              >
                 <mat-icon>description</mat-icon>
              </button>
              &nbsp;
                <button mat-flat-button color="accent" (click)="onCerrar(element)"
                  [disabled]="element.estado === 'VAL' || element.estado==='CER'" matTooltip="Cerrar" matTooltipClass="tooltip-red">
                  <mat-icon>close</mat-icon>
                </button>

            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>

