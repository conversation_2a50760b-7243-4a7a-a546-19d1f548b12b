import { element } from "protractor";
import {
  Component,
  OnInit,
  ViewChild,
  Input,
  Output,
  EventEmitter,
} from "@angular/core";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { CajaGeneralService } from "../cajageneral.service";
import { Factura } from "./factura";
//import { RelacionIngreso } from '../relacion-ingreso/relacion-ingreso';

import { global } from "../../../_services/global";
import swal from "sweetalert2";

@Component({
  selector: "app-temp-factura",
  templateUrl: "./temp-factura.component.html",
  styleUrls: ["./temp-factura.component.css"],
  providers: [CajaGeneralService],
})
export class TempFacturaComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  //public acreencia: EntidadAcreedora;
  public dataSource = null;

  @Input() relacionIngreso: any;
  //@Output () valueResponse = new EventEmitter();
  @Output() montoLocal = new EventEmitter();
  @Output() montoMoneda = new EventEmitter();

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(private _facturaService: CajaGeneralService) {
    this.page_title = "Listado de Requerimientos";
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "id_temp_factura",
    "producto",
    "poliza_aseguradora",
    "fecoperacion",
    "pagador",
    "factura",
    "monto_local",
    "monto_moneda",
    "fecha_vencimiento",
    "nombre_plan_frac",
    "moneda",
    "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getFactura(
      this.relacionIngreso == "undefined" ? 0 : this.relacionIngreso
    );
  }

  getFactura($id?: string) {
    console.log("getFactura");
    this._facturaService.findFacturaByIdRelIng($id).subscribe(
      (response) => {
        //console.log("response", response);
        if (response.status == "success") {
          this.dataSource = new MatTableDataSource<Factura>(
            response.tempFacturaRequerimiento
          );
          //this.dataSource = new MatTableDataSource<Factura>(response.factura);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onDelete(element) {
    console.log("onDelete");
    let { id_temp_factura, factura_posterior, factura } = element;
    const numSelected = this.dataSource._data._value;
    let bandera: boolean = false;
    swal.fire({
      title: "¿Estas seguro?",
      text: "Una vez borrado no podrás recuperarlo!",
      icon: "warning",
      showCancelButton: true,
      showConfirmButton: true
    }).then((willDelete) => {
      if (willDelete) {
        if (factura_posterior !== null) {
          let requerimiento = numSelected.filter(
            (factura) => factura.factura === factura_posterior
          );

          if (requerimiento.length !== 0) {
            swal.fire(
              "Eliminación de Facturas",
              `La factura seleccionada: (${factura}), no puede eliminarse. Por Favor Verifique`,
              "error"
            );
          } else {
            bandera = true;
          }
        } else {
          bandera = true;
        }
        //
        if(bandera){
          this._facturaService.deleteFactura(id_temp_factura).subscribe(
            (response) => {
              swal.fire({ text: "Registro eliminado!",
                icon: "success",
              });
              this.getFactura(
                this.relacionIngreso == "undefined" ? 0 : this.relacionIngreso
              );
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        }  
      } else {
        swal.fire("Los datos no han cambiado");
      }
    });
  }

  getTotalLocal() {
    let sum: number = 0.0;
    if (this.dataSource)
      for (let row of this.dataSource.data) {
        if (row.id != 0) sum += parseFloat(row.monto_local);
      }
    this.montoLocal.emit({ totalFacturaLocal: sum });
    return sum;
  }

  getTotalMoneda() {
    let sum: number = 0.0;
    if (this.dataSource)
      for (let row of this.dataSource.data) {
        if (row.id != 0) sum += parseFloat(row.monto_moneda);
      }
    this.montoMoneda.emit({ totalFacturaMoneda: sum });
    return sum;
  }
}
