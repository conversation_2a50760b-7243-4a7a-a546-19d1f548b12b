import { Component, OnInit, Inject, ViewChild } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
//
import { UserService } from "src/app/_services/user.service";
import { RefraccionamientoService } from "../refraccionamiento.service";

import { Refraccionamiento } from "../refraccionamiento";
import { RequerimientoTemporal } from "./requerimiento-temporal";
import { RequerimientoRubrosDetTemporal } from "./requerimiento-rubros-det-temporal";

//ALERTS
import swal from "sweetalert2";
import { NgxSpinnerService } from "ngx-spinner";
//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
//STEPPER
import { MatStepper } from "@angular/material/stepper";
// ABRIR DIALOGO DESDE UNA PANTALLA DE TIPO DIALOGO
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { ListPlanFraccionamientoComponent } from "../../../list-plan-fraccionamiento/list-plan-fraccionamiento.component";
import { VwPlanFraccionamiento } from "../../../list-plan-fraccionamiento/vw-plan-fraccionamiento";
//TABLE
import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { MAT_DATE_FORMATS } from '@angular/material/core';
import { ListClienteComponent } from "src/app/components/list-cliente/list-cliente.component";
import { Cliente } from "src/app/components/list-cliente/cliente";
import { isNull } from "util";


export const MY_DATE_FORMATS = {
  parse: {
    dateInput: 'DD/MM/YYYY',
  },
  display: {
    dateInput: 'DD/MM/YYYY',
    monthYearLabel: 'MMMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};

@Component({
  selector: "app-detalle-refraccionamiento",
  templateUrl: "./detalle-refraccionamiento.component.html",
  styleUrls: ["./detalle-refraccionamiento.component.css"],
  providers: [RefraccionamientoService,UserService, { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }],
})
export class DetalleRefraccionamientoComponent implements OnInit {
  //
  @ViewChild("stepper") private stepper: MatStepper;
  //
  public isLinear = true;
  public firstFormGroup: FormGroup;
  public secondFormGroup: FormGroup;
  //
  public identity: string = '';
  public page_title: string;
  public status: string;
  public url;
  public refraccionamiento: Refraccionamiento;
  public planesFinanciamiento;
  isEditable = true;
  public dataSource = null;
  public dataSource2 = null;

  public facturaSeleccionada: number;

  public nNumRefraccTemp: number = 0;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private ngxSpinnerService: NgxSpinnerService,
    private _refraccionamientoService: RefraccionamientoService,
    private _formBuilder: FormBuilder,
    private _userService: UserService,
    private _dialogRef: MatDialogRef<DetalleRefraccionamientoComponent>, //DIALOGO
    private dialog: MatDialog, // ABRIR DIALOGO DESDE UNA PANTALLA DE TIPO DIALOGO
    
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = data.title;
    this.identity = this._userService.getIdentity();
  }

  displayedColumns: string[] = [
    "idrequerimiento",
    "numpago",
    "estado",
    "fecvenc",
    "totalrequerimiento",
    "actions",
  ];
  displayedColumns2: string[] = ["idrubro", "descripcion", "mtorubro"];

  @ViewChild('master', { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  @ViewChild('detalle', { static: true }) paginator2: MatPaginator;
  ngOnInit() {
    this.firstFormGroup = this._formBuilder.group({
      nIdPlanFracc: ["", Validators.required],
      nombre_fraccionamiento: ["", Validators.required],
      frecuencia: ["", Validators.required],
      idplantilla: ["", Validators.required],
      idplangastos: ["", Validators.required],
      nombre_plan_gasto: ["", Validators.required],
      numpagos: ["", Validators.required],
      codigo: ["", Validators.required],
      idpoliza: [this.data.element.idpoliza, Validators.required],
      idpagador: [this.data.element.idpagador, Validators.required],
      porcpagador: [this.data.element.porcpagador, Validators.required],
      nNumRefraccTemp: [this.data.nNumRefraccTemp, Validators.required],
      operacion: [this.data.element.operacion, Validators.required],
      pagosiguales: [""],
      motivo: [""],
      idcliente: [""],
      nombre: [""],
      nuevafechavenc: [""]
    });
    this.secondFormGroup = this._formBuilder.group({
      secondCtrl: ["", Validators.required],
    });
    this.nNumRefraccTemp = this.data.nNumRefraccTemp;
    this.initValuesForm();
  }

  getRequerimientoTemporal(id: number) {
    this.ngxSpinnerService.show();
    this._refraccionamientoService.getRequerimientoTemporalById(id).subscribe(
      (response) => {
        this.ngxSpinnerService.hide();
        if (response.status == "success") {
          this.dataSource = new MatTableDataSource<RequerimientoTemporal>(
            response.requerimientoTemporal
          );
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
        this.ngxSpinnerService.hide();
      }
    );
  }

  sendPostSiguiente(data: any){
    this.ngxSpinnerService.show();
    this._refraccionamientoService.postRequerimientoNext(data).subscribe(
      (response) => {
        this.ngxSpinnerService.hide();
        this.getRequerimientoTemporal(this.nNumRefraccTemp);
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
        this.ngxSpinnerService.hide();
      }
    );
  }

  sendPostFinalizar(data: any){
    data.usuario = this.identity;
    this.ngxSpinnerService.show();
    this._refraccionamientoService.postRequerimientoDefinitivo(data).subscribe(
      (response) => {
        this.ngxSpinnerService.hide();
        this.close();
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
        this.ngxSpinnerService.hide();
      }
    );
  }

  getRequerimientoRubrosDetTemporal(factura: number) {
    this.ngxSpinnerService.show();
    this._refraccionamientoService
      .getRequerimientoRubrosDetTemporal(factura)
      .subscribe(
        (response) => {
          this.ngxSpinnerService.hide();
          //console.log('response',response);
          this.dataSource2 = new MatTableDataSource<RequerimientoRubrosDetTemporal>(response.requerimientoRubrosDetTemporal);
          this.dataSource2.paginator = this.paginator2;
          //this.dataSource2.sort = this.sort;
        },
        (error) => {
          console.log(<any>error);
          //this.status = 'error';
          this.ngxSpinnerService.hide();
        }
      );
  }

  private initValuesForm(): void {
    if (this.data.planesFinanciamiento) {
      this.planesFinanciamiento = this.data.planesFinanciamiento;
    }
  }

  onNextForm(step, data) {
    if (this.firstFormGroup.get("nIdPlanFracc").value) {
      if(this.firstFormGroup.get('nuevafechavenc').value != '' && isNull(this.firstFormGroup.get('nuevafechavenc').value) != true){
        this.ngxSpinnerService.show();
        this._refraccionamientoService.postValidaFecha(data).subscribe(
          (response) => {
            this.ngxSpinnerService.hide();
            
            if (response.data.validacionFecha != 'S') { 
              swal.fire(
                "Refraccionamiento",
                "No es posible refraccionar con la fecha ingresada",
                "error"
              );
              return;
            } else {
              this.sendPostSiguiente(data);
              this.stepper.next();
            }
            
          },
          (error) => {
            console.log(<any>error);
            //this.status = 'error';
            this.ngxSpinnerService.hide();
          }
        );
      } else if(this.firstFormGroup.get('nuevafechavenc').value == '' || isNull(this.firstFormGroup.get('nuevafechavenc').value) == true) {
        this.sendPostSiguiente(data);
        this.stepper.next();
      }
    } else {
      swal.fire(
        "Refraccionamiento",
        "No ha seleccionado Plan de Fraccionamiento",
        "error"
      );
    }
  }

  openDialogo(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(
      ListPlanFraccionamientoComponent,
      dialogConfig
    );

    dialogRef.afterClosed().subscribe((result: VwPlanFraccionamiento) => {
      //let cliente = {idcliente: result.idcliente, nombre: result.nombre};
      //console.log('cliente', cliente);
      if (result) {
        this.firstFormGroup.patchValue({
          nIdPlanFracc: result.idplanfracc,
          frecuencia: result.frecuencia,
          idplantilla: result.idplantilla,
          idplangastos: result.idplangastos,
          numpagos: result.numpagos,
          pagosiguales: result.pagosiguales,
          nombre_fraccionamiento: result.nombre_fraccionamiento,
          nombre_plan_gasto: result.nombre_plan_gasto,
          codigo: result.codigo,
        });
        this.getRequerimientoTemporal(this.nNumRefraccTemp);
        this.getRequerimientoRubrosDetTemporal(this.facturaSeleccionada);
      }
    });
  }

  openDialogoCliente(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListClienteComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: Cliente) => {
      
      if (result) {
        this.firstFormGroup.patchValue({
          idcliente: result.idcliente,
          nombre: result.nombre,
        });
      }
    });
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onSelected(element) {
    console.log("onSelected");
    this.facturaSeleccionada = element.idrequerimiento;
    this.getRequerimientoRubrosDetTemporal(this.facturaSeleccionada);
    //this._dialogRef.close(element.idcliente);
    //this._dialogRef.close(<VwPlanFraccionamiento>element);
  }

  getTotalLocal() {
    let sum: number = 0.0;
    if (this.dataSource2)
      for (let row of this.dataSource2.data) {
        if (row.id != 0) sum += parseFloat(row.mtorubro);
      }
    //this.montoLocal.emit({totalFacturaLocal: sum});
    return sum;
  }

  doRefraccionar(stepper: MatStepper, data: any): void {
    console.log("doRefraccionar");

    swal.fire({
      title: "Refraccionamiento",
      text: "Deseas Finalizar el Proceso de Refraccionamiento?",
      icon: "warning",
      showCancelButton: true,
      showConfirmButton: true,
    }).then((willDelete) => {
      if (willDelete.isConfirmed) {
        this.sendPostFinalizar(data);
        swal.fire({
          text: "Proceso finalizado correctamente!",
          icon: "success",
        });
      }
    });
  }

  close(): void {
    this._dialogRef.close();
  }
}
