export interface Poliza {
    idpoliza: number;
    idtipo_poliza: number;
    idtipo_vigencia: number;
    idcliente: number;
    idaseguradora: number;
    numren: number;
    poliza_aseguradora: number;
    poliza_corredor: number;
    vigencia_inicio: any;
    vigencia_fin: any;
    indclientepaga: string;
    idproducto: number;
    estado: string;
    moneda: string;
    idcotizacion: string;
    grabacion: any;
    grabador: string;
    campo: string;
    id_intermediario: string;
    marcar_anular: string;
    hora_inicio_vig: string;
    test: string;
    idcobrador: number;
    idcanal: number;
    idtipo_suscripcion: number;
    suscriptor: string;
    calculo: number
    forma_factura: number;
    y_o: string;
    idclasifriesgo: string;
    IDEPOL_MIGRA: number;
    CODPOL_MIGRA: string;
    NUMPOL_MIGRA: string;
  }