<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8 mt-2">
        <div class="page_title">
          <h1>{{ pageTitle }}</h1>
        </div>
        <mat-divider></mat-divider>
        <mat-form-field class="mt-2">
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="mat-elevation-z8"
        >
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID.</th>
            <td mat-cell *matCellDef="let element">{{ element.idcobrador }}</td>
          </ng-container>

          <ng-container matColumnDef="codigo_cobrador">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Código</th>
            <td mat-cell *matCellDef="let element">
              {{ element.codigo_cobrador }}
            </td>
          </ng-container>

          <ng-container matColumnDef="nombre">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Nombre</th>

            <td mat-cell *matCellDef="let element">
              {{ element.nombre_combrador }}
            </td>
          </ng-container>

          <ng-container matColumnDef="apellido">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Apellido</th>

            <td mat-cell *matCellDef="let element">
              {{ element.apellido_cobrador }}
            </td>
          </ng-container>

          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>

            <td mat-cell *matCellDef="let element">
              {{ element.estado | slice : 0 : 3 }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element" class="actions">
              <button
                mat-flat-button
                color="primary"
                (click)="onEdit(element)"
                matTooltip="Editar cobrador"
                class="mr-1"
              >
                <mat-icon>edit</mat-icon>
              </button>
              <button
                mat-flat-button
                color="accent"
                (click)="datosGenerales(element.idcobrador)"
                matTooltip="Ver detalles"
                class="mr-1"
              >
                <mat-icon>info</mat-icon>
              </button>
              <button mat-button [matMenuTriggerFor]="menu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #menu="matMenu">
                <button
                  mat-menu-item
                  (click)="asignarEjecutivo(element.idcobrador)"
                >
                  Asignar ejecutivo
                </button>
                <button
                  mat-menu-item
                  (click)="
                    desactivarActivarCobrador(
                      element.idcobrador,
                      element.estado
                    )
                  "
                >
                  {{ element.estado === "ACT" ? "Desactivar" : "Activar" }}
                  cobrador
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
