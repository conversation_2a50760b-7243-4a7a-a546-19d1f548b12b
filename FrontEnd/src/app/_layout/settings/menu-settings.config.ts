// Default menu settings configurations

export interface MenuItem {
  title: string;
  icon: string;
  page: string;
  isExternalLink?: boolean;
  issupportExternalLink?: boolean;
  badge: { type: string, value: string };
  submenu: {
    items: Partial<MenuItem>[];
  };
  section: string;
}

export interface MenuConfig {
  horizontal_menu: {
    items: Partial<MenuItem>[]
  };
  vertical_menu: {
    items: Partial<MenuItem>[]
  };
}

export const MenuSettingsConfig: MenuConfig = {
  horizontal_menu: {
    items: [
     
      {
        title: 'Templates',
        icon: 'la-television',
        page: 'null',
        submenu: {
          items: [
            {
              title: 'Horizontal',
              page: 'null'
            },
            {
              title: 'Vertical',
              page: 'null'
            },
          ]
        }
      },
      {
        title: 'Raise Support',
        icon: 'la-support',
        page: 'https://aseguate.com/',
        isExternalLink: true
      },
    ]
  },
  vertical_menu: {
    items: [
      {
        title: 'Produccion',
        icon: 'la-television',
        page: 'null',
        submenu: {
          items: [
            {
              title: 'Ejecutivo',
              page: '/ejecutivo'

            }
          ]
        }
      },
      {
        title: 'Reclamos',
        icon: 'la-television',
        page: 'null',
        submenu: {
          items: [
            {
              title: 'Ajustadores',
              page: 'null'
            },
            {
              title: 'Siniestros Generales',
              page: 'null'
            },
          ]
        }
      },
      {
        title: 'Cobros',
        icon: 'feather ft-layout',
        page: 'null',
        submenu: {
          items: [
            
            {
              title: 'Caja',
              page: '/caja'

            },
            {
              title: 'Cajero',
              page: '/cajero'

            },
            {
              title: 'Apertura/Cierre Caja',
              page: '/aperturacierrecaja'

            },
            {
              title: 'Entidad Acreedora',
              page: '/entidadacreedora'

            },
            {
              title: 'Compromiso Pago',
              page: '/compromisopago'

            },
            {
              title: 'Relacion de Ingreso',
              page: '/relacioningreso'

            },
            {
              title: 'Caja General',
              page: '/cajageneral'

            }
          ]
        }
      },
      { section: 'Sitios de Interés', icon: 'la-ellipsis-h' },
      {
        title: 'Aseguate.com',
        icon: 'la-support',
        page: 'https://aseguate.com/',
        isExternalLink: true
      },
    ]
  }

};





