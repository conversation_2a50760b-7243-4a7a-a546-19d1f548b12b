import { Injectable } from "@angular/core";
import { select, Store } from "@ngrx/store";
import { Subscription } from "rxjs";
import { delay, filter, map, take } from "rxjs/operators";
import { ICotizador, PayloadPagadores } from "src/app/interface";
import { reaseguroTomadoActions } from "./reaseguro-tomado.actions";
import { ReaseguroTomadoState } from "./reaseguro-tomado.reducers";
import { cotizacionesQuery } from "./reaseguro-tomado.selector";

@Injectable({ providedIn: "root" })
export class ReaseguroTomadoFacade {
  updatingCotizacion$ = this.store.select(
    cotizacionesQuery.selectCotizacionUpdating,
  );
  cotizacionCurrentState$ = this.store.select(
    cotizacionesQuery.selectReaseguroTomadoState,
  );
  cotizacionRender$ = this.store.select(cotizacionesQuery.selectRenderViews);
  plantillasCotizadas$ = this.store.select(
    cotizacionesQuery.selectPlantillasCotizadas,
  );
  pagos$ = this.store.select(cotizacionesQuery.selectPagos);
  isLoading$ = this.store.select(cotizacionesQuery.selectLoading);
  valueKeys$ = this.store.select(cotizacionesQuery.selectValueKeys);

  reaseguroTomado$ = this.store
    .select(cotizacionesQuery.selectCotizacion)
    .pipe(filter((cotizacion) => cotizacion !== null));

  cotizacionError$ = this.store
    .select(cotizacionesQuery.selectCotizacionCreateError)
    .pipe(filter((v) => v));

  constructor(private store: Store<ReaseguroTomadoState>) {}

  loadPlantillasCotizadas(idcotizacion: string) {
    this.store.dispatch(
      reaseguroTomadoActions.loadPlantillasCotizadas({ idcotizacion }),
    );
  }

  updatingCotizacion(cotizacion?: ICotizador) {
    this.store.dispatch(
      reaseguroTomadoActions.updatedCotizacion({ cotizacion }),
    );
  }

  createCotizacion(cotizacion: ICotizador) {
    this.store.dispatch(
      reaseguroTomadoActions.createCotizacion({ cotizacion }),
    );
    return this.cotizacionCurrentState$.pipe(
      filter((state) => !!state.cotizacion?.idcotizacion),
      take(1),
      map((state) => state.cotizacion),
    );
  }
  createCaracteristicas(
    payload: {
      idcotizacion: number;
      numcert: number;
      caracteristicas: {
        valueKeys: any;
        plantillas?: number[];
        plantilla?: number;
        idplanfracc?: number;
        pagadores?: PayloadPagadores[];
      };
    },
    event: string,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      let sub: Subscription = new Subscription();
      this.store.dispatch(
        reaseguroTomadoActions.createCaracteristicas({ payload, event }),
      );

      sub.add(
        this.store
          .pipe(delay(1000), select(cotizacionesQuery.selectLoading))
          .subscribe((result) => {
            if (!result) {
              resolve("success");
              sub.unsubscribe();
            }
          }),
      );
    });
  }

  setCaracteristicasCotizacion(valuesKeys: any) {
    this.store.dispatch(
      reaseguroTomadoActions.setCaracteristicasCotizacion({ valuesKeys }),
    );
  }

  setCotizacion(cotizacion: ICotizador) {
    this.store.dispatch(
      reaseguroTomadoActions.createCotizacionSuccess({ cotizacion }),
    );
  }

  newCertificadoCotizacion() {
    this.store.dispatch(reaseguroTomadoActions.newCotizacionCertificado());
  }

  resetState() {
    this.store.dispatch(reaseguroTomadoActions.resetState());
  }

  updateSecciones(secciones: { SI?: any; SII?: any; SIII?: any }) {
    this.store.dispatch(reaseguroTomadoActions.updateSecciones({ secciones }));
  }
}
