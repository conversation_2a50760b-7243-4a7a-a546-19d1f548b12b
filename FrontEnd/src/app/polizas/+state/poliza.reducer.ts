import { createFeature, createReducer, on } from "@ngrx/store";
import { PayloadContratante, PayloadPagadores } from "src/app/interface";
import { IOperpoliza } from "src/app/interface/operPoliza";
import { polizaActions } from "./poliza.actions";

type PagadorCert = { numcert: number; idpersona: number };

export interface PolizaState {
  pagadores: PayloadPagadores[];
  pagadorCert: PagadorCert[];
  operacion: IOperpoliza;
  contratante: PayloadContratante[];
}

export const polizaInitialState: PolizaState = {
  pagadores: [],
  pagadorCert: [],
  operacion: null,
  contratante: null,
};

export const polizaFeature = createFeature({
  name: "poliza",
  reducer: createReducer(
    polizaInitialState,
    on(polizaActions.loadPagadores, (state, action) => ({
      ...state,
      pagadores: [...state.pagadores, action.pagadores],
    })),
    on(polizaActions.loadPagadorCert, (state, { payload }) => ({
      ...state,
      pagadorCert: state.pagadorCert.some(
        (pagador) => pagador.numcert === payload.numcert,
      )
        ? [...state.pagadorCert]
        : [...state.pagadorCert, payload],
    })),
    on(polizaActions.loadContratante, (state, action) => ({
      ...state,
      contratante: [action.contratante],
    })),
    on(polizaActions.resetPagador, (state, { session }) => ({
      ...state,
      pagadores: session === "poliza" || "all" ? [] : state.pagadores,
      pagadorCert: session === "cert" || "all" ? [] : state.pagadorCert,
    })),

    on(polizaActions.getOperacionesSuccess, (state, action) => ({
      ...state,
      //isLoading: true,
      operacion: action.operacion,
    })),
    on(polizaActions.editPagador, (state, action) => ({
      ...state,
      pagadores: state.pagadores.map((pagador) =>
        pagador.pagador === action.pagador.pagador
          ? { ...pagador, ...action.pagador }
          : pagador,
      ),
    })),
  ),
});
