<mat-card>
  <mat-card-content>
    <div *ngFor="let key of keys">
      <mat-card-subtitle>
        <span>{{ key === "DATPART" ? "DATOS PARTICULARES" : key }}</span>
      </mat-card-subtitle>

      <div class="row">
        <mat-form-field
          appearance="outline"
          class="col"
          *ngFor="let particulares of particulares[key]"
        >
          <mat-label>{{ particulares.etiqueta }}</mat-label>
          <input
            matInput
            readonly
            [value]="getInputTypeOf(particulares.valor)"
          />
        </mat-form-field>
      </div>
    </div>
  </mat-card-content>
  <mat-card-actions>
    <ng-content></ng-content>
  </mat-card-actions>
</mat-card>
