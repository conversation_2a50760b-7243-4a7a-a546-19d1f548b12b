export interface ITipoContratoDetallePoliza {
  id?: number;
  tipo_contrato_detalle_id: number;
  poliza_id: number;
  cantidad: number;
  certificado: number;
  comision?: number;
  needsVerify?: boolean;
}

export interface ITipoContratoDetallePolizaResponse
  extends ITipoContratoDetallePoliza {
  poliza: number;
  tipo_contrato: number;
  contrato: number;
  vigencia_inicio: string;
  vigencia_fin: string;
  producto: string;
}

export interface ITipoContratoDetallePolizaForAssign {
  data: IDataContratoAssign[];
  datosPoliza: DatosPoliza[];
}

export interface IDataContratoAssign {
  capacidad: string;
  codigo_tipo_contrato: string;
  created_at: Date;
  dias_cobertura: number | null;
  dias_garantia: number | null;
  id: number;
  moneda_tipo_contrato: string;
  nombre_tipo_contrato: string;
  observaciones: null | string;
  porcentaje_cuota_parte: number | null;
  porcentaje_retencion: number | null;
  prioridad: number | null;
  tasa_reaseguro: number | null;
  tipo_contrato_id: number;
  updated_at: Date;
}

export interface DatosPoliza {
  moneda: string;
  valor: string;
  numcert: number;
  comision?: number;
  isr?: number;
}
