<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ pageTitle }}</h1>
        </div>
        <mat-divider></mat-divider>
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button
              mat-flat-button
              color="primary"
              class="mr-1"
              matTooltip="Nuevo"
              (click)="limpiarLS()"
              [routerLink]="['/reaseguros/tomado', 'nuevo']"
            >
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div *ngIf="data$ | async as data; else loading">
          <icore-table
            [columns]="displayedColumns"
            [data]="data.data"
            [length]="data.total"
            (setPage)="setPage($event)"
            (onNotData)="onNew()"
            [valueToSearch]="valueToSearch"
            (params)="setFiltro($event)"
          >
            <h1>No hay cotizaciones</h1>
          </icore-table>
        </div>

        <ng-template #loading>
          <div class="mx-2">
            <ngx-skeleton-loader
              [count]="displayedColumns.length"
              appearance="line"
            ></ngx-skeleton-loader>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <app-slip-pdf> </app-slip-pdf>
</div>
