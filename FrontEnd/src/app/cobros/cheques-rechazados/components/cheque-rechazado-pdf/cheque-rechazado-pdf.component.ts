import {
  ChangeDetectionStrategy,
  Component,
  Inject,
  OnInit,
} from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { TuiDialog } from "@taiga-ui/cdk";
import { TuiPdfViewerOptions, TuiPdfViewerService } from "@taiga-ui/kit";
import { NgxSpinnerService } from "ngx-spinner";
import { ModalService } from "src/app/_services/modal.service";
import { MaintenanceComponent } from "src/app/components/maintenance/maintenance.component";
import { ContextSlip } from "src/app/interface";
import { ChequeRecuperacionService } from "../../services/cheque-recuperacion.service";

export type Buttons = ReadonlyArray<
  Readonly<{
    text: string;
    color: string;
    onClick(context: TuiDialog<TuiPdfViewerOptions<Buttons>, string>): void;
  }>
>;

@Component({
  selector: "cheque-rechazado-pdf",
  templateUrl: "./cheque-rechazado-pdf.component.html",
  changeDetection: ChangeDetectionStrategy.OnPush,
})

export class ChequeRechazadoPdfComponent {
  codusr: string;
  constructor(
    public chequeRecuperacionService: ChequeRecuperacionService,
    private ngxSpinner: NgxSpinnerService,
    @Inject(DomSanitizer) private readonly sanitizer: DomSanitizer,
    @Inject(TuiPdfViewerService)
    private readonly pdfService: TuiPdfViewerService,
    private readonly modalService: ModalService,
  ) {

    // codusr = JSON.parse(localStorage.getItem("currentUser") || "{}");
    const usuarioString = localStorage.getItem("currentUser");
    const usuario = usuarioString ? JSON.parse(usuarioString) : null;
    
    // Asigna solo el nombre si el usuario existe
    this.codusr = usuario || "";
  }
   
  
  show(
    options: TuiPdfViewerOptions<Buttons>,
    id: string,
    context?: ContextSlip,
  ) {
    this.ngxSpinner.show();
    
    
    this.chequeRecuperacionService
      .getPdfChequeRechazado(id, this.codusr)
      .subscribe({
        next: (value) => {
          const b64 = `data:application/pdf;base64, ${value}`;

          this.pdfService
            .open(this.sanitizer.bypassSecurityTrustResourceUrl(b64), options)
            .subscribe();

          this.ngxSpinner.hide();
        },
        error: (_) => {
          this.modalService.openDialog<MaintenanceComponent>({
            component: MaintenanceComponent,
            title: "Mantenimiento",
            element: {
              paragraph: context?.paragraph
                ? context?.paragraph
                : "Estamos configurando la documentación muy pronto la podras visualizar.",
              buttons: context?.buttons,
            },
          });
          this.ngxSpinner.hide();
        },
      });
  }
}
