<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="page_title">
        <h1>{{ pageTitle }}</h1>
      </div>

      <mat-divider></mat-divider>
      <mat-form-field>
        <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
      </mat-form-field>

      <div class="container-btn-new">
        <div class="btn-new">
          <button
            mat-flat-button
            color="primary"
            matTooltip="Nueva Reclamo"
            [routerLink]="['nuevo']"
          >
            <mat-icon>add</mat-icon>
          </button>
        </div>
      </div>

      <div class="table-responsive" perfectScrollbar>
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          *ngIf="dataSource?.data.length > 0 && isLoadingResults; else Skeleton"
        >
          <ng-container matColumnDef="idreclamo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>#</th>
            <td mat-cell *matCellDef="let element">
              {{ element.idreclamo }}
            </td>
          </ng-container>

            <ng-container matColumnDef="codigo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Producto</th>
            <td mat-cell *matCellDef="let element">
              {{ element.codigo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="poliza_aseguradora">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Poliza</th>
            <td mat-cell *matCellDef="let element">
              {{ element.poliza_aseguradora }}
            </td>
          </ng-container>

          <ng-container matColumnDef="certificado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Certificado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.certificado || "sin certificado" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="fechaingresosistema">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Fecha</th>
            <td mat-cell *matCellDef="let element">
              {{ element.fechaingresosistema | date : "dd/MM/YYYY" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
            <td mat-cell *matCellDef="let element">
              <mat-chip-list>
                <mat-chip
                  color="primary"
                  selected
                  [color]="element.estado === 'VAL' ? 'none' : 'primary'"
                  (click)="
                    activar(
                      element.idreclamo,
                      element.estado,
                      element.activa_reclamo
                    )
                  "
                  [disabled]="!element.activa_reclamo"
                >
                  {{ element.estado }}
                </mat-chip>
              </mat-chip-list>
            </td>
          </ng-container>
          <ng-container matColumnDef="fechaocurrencia">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Fecha ocurrencia
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.fechaocurrencia | date : "dd/MM/YYYY" }}
            </td>
          </ng-container>

          <ng-container matColumnDef="nombre">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Asegurado</th>
            <td mat-cell *matCellDef="let element">
              {{ element.nombre }}
            </td>
          </ng-container>

        

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-flat-button
                color="primary"
                matTooltip="Activar"
                (click)="
                  activar(
                    element.idreclamo,
                    element.estado,
                    element.activa_reclamo
                  )
                "
                [disabled]="!element.activa_reclamo || element.estado === 'ACT'"
                class="mr-1"
              >
                <mat-icon>done</mat-icon>
              </button>

              <button
                mat-flat-button
                color="accent"
                (click)="edit(element.idreclamo)"
                matTooltip="Editar"
                class="mr-1"
              >
                <mat-icon>edit_icon</mat-icon>
              </button>

              <button
                mat-flat-button
                [matMenuTriggerFor]="moreOptions"
                class="mr-1"
              >
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #moreOptions="matMenu">
                <button
                  mat-menu-item
                  (click)="
                    detailReclamo(element.idreclamo, element.certificado)
                  "
                  matTooltip="Detalle"
                >
                  <mat-icon>info</mat-icon>
                  <span class="mr-2">Detalle</span>
                </button>

                <button
                  mat-menu-item
                  [routerLink]="['requisitos', element.idreclamo]"
                  matTooltip="Requisitos"
                >
                  <mat-icon>app_registration</mat-icon>
                  <span class="mr-2">Requisitos</span>
                </button>
                <button
                  mat-menu-item
                  [routerLink]="['reservas', element.idreclamo]"
                  matTooltip="Reservas"
                >
                  <mat-icon>bookmark</mat-icon>
                  <span class="mr-2">Reservas</span>
                </button>
                <button
                  mat-menu-item
                  [routerLink]="['terceros', element.idreclamo]"
                  matTooltip="Terceros"
                >
                  <mat-icon>groups</mat-icon>
                  <span class="mr-2">Terceros</span>
                </button>

                <button
                  mat-menu-item
                  matTooltip="Estadisticas"
                  [routerLink]="['estadisticas', element.idreclamo]"
                >
                  <mat-icon>bar_chart</mat-icon>
                  <span class="mr-2">Estadisticas</span>
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr
            mat-row
            *matRowDef="let row; columns: displayedColumns"
            (dblclick)="detailReclamo(row.idreclamo, row.certificado)"
          ></tr>
        </table>

        <ng-template #Skeleton>
          <div class="mx-2">
            <ngx-skeleton-loader
              [count]="displayedColumns.length"
              appearance="line"
            ></ngx-skeleton-loader>
          </div>
        </ng-template>
      </div>
      <mat-paginator
        [length]="length"
        [pageSize]="pageSize"
        [pageSizeOptions]="[5, 10, 20]"
        (page)="setPage($event)"
        showFirstLastButtons
      ></mat-paginator>
    </div>
  </div>
</div>
