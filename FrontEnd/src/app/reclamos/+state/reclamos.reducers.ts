import { createFeature, createReducer, on } from "@ngrx/store";
import {
  ICoberturas,
  IDireccionReclamo,
  IPolizaReclamo,
  IReclamos,
} from "src/app/interface";
import { reclamoActions } from "./reclamos.actions";

export interface ReclamoState {
  entities: IReclamos[];
  poliza: IPolizaReclamo;
  direccion: IDireccionReclamo;
  cobertura: ICoberturas;
  loading: boolean;
}

export const reclamoInitialState: ReclamoState = {
  entities: [],
  poliza: null,
  direccion: null,
  cobertura: null,
  loading: false,
};

export const reclamoFeature = createFeature({
  name: "reclamos",
  reducer: createReducer(
    reclamoInitialState,
    on(reclamoActions.loadReclamos, (state, { entities }) => ({
      ...state,
      entities,
    })),
    on(reclamoActions.loadPolizaReclamo, (state, action) => ({
      ...state,
      poliza: action.poliza,
    })),
    on(reclamoActions.setDireccionReclamo, (state, action) => ({
      ...state,
      direccion: action.direccion,
    })),
    on(reclamoActions.setCoberturas, (state, action) => ({
      ...state,
      cobertura: action.cobertura,
    })),
    on(reclamoActions.resetState, () => ({ ...reclamoInitialState }))
  ),
});
