<mat-dialog-content>
  <form [formGroup]="formAnulacion">
    <h3>
      <B>ANULACIÓN DE PÓLIZA</B>
    </h3>
    <div class="container">
      <div>
        <div class="row">
          <label class="col-md-12 label-control" style="border-top-width: 1px">ASEGURADO:</label>
        </div>
        <mat-form-field class="col" appearance="outline">
          <input readonly type="text" matInput [value]="polizaDetalle.cliente" placeholder="Asegurado" />
        </mat-form-field>
        <div class="row">
          <label class="col label-control" style="border-top-width: 2px">PÓLIZA:</label>
          <label class="col label-control" style="border-top-width: 2px">NÚMERO:</label>
          <label class="col label-control" style="border-top-width: 2px">VIGENCIA:</label>
        </div>
        <div class="row">
          <mat-form-field class="col" appearance="outline">
            <input readonly type="text" matInput [value]="polizaDetalle.producto" />
          </mat-form-field>
          <mat-form-field class="col" appearance="outline">
            <input readonly type="text" matInput [value]="polizaDetalle.numpol" />
          </mat-form-field>
          <mat-form-field class="col" appearance="outline">
            <input readonly type="text" matInput [value]="polizaDetalle.vigencia_fin" />
          </mat-form-field>
        </div>
      </div>
      <div class="row">
        <mat-form-field class="col" appearance="outline">
          <mat-label>Tipo anulación</mat-label>
          <mat-select formControlName="tipoAnulacion">
            <mat-option *ngFor="let tipo of tiposAnulacion$ | async" [value]="tipo.value">
              {{ tipo.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="col" appearance="outline" *ngIf="motivosAnulacion$ | async as motivos">
          <mat-label>Motivo</mat-label>
        
            <mat-select formControlName="motivo">
              <mat-option *ngFor="let motivo of motivos" [value]="motivo.value" (onSelectionChange)="motivoAnulacionDesc($event,motivo.label)">
                {{ motivo.label }}
              </mat-option>
            </mat-select>
 
        </mat-form-field>
      </div>
      <div class="row">
        <mat-form-field class="col" appearance="outline">
          <mat-label>Fecha Anulación</mat-label>
          <input matInput [matDatepicker]="fechAnular" placeholder="Fecha Anulación" name="fechAnular" formControlName="fechaAnular"/>
          <mat-hint>DD/MM/YYYY</mat-hint>
          <mat-datepicker-toggle matSuffix [for]="fechAnular"></mat-datepicker-toggle>
          <mat-datepicker #fechAnular></mat-datepicker>
        </mat-form-field>
        
        <div>
          <mat-checkbox *ngIf="mostrarCheckbox" formControlName="faltoPago" class="mt-2">
            Falto pago
          </mat-checkbox>
        </div>
      </div>
      <div class="row">
        <mat-form-field class="col" appearance="outline">
          <mat-label>Texto Anulación</mat-label>
          <input matInput formControlName="textAnul" placeholder="Escribe aquí" />
        </mat-form-field>
      </div>
    </div>
  </form>
</mat-dialog-content>

<mat-dialog-actions>
  <button mat-flat-button class="bg-danger" matTooltip="Anular" (click)="anular()">
    <mat-icon>close</mat-icon>
  </button>
</mat-dialog-actions>
