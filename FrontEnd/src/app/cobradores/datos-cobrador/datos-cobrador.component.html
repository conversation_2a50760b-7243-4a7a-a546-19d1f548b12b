<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <button-back></button-back>
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>Detalle de Cobrador</h1>
        </div>
        <mat-divider></mat-divider>
        <div class="row p-3" *ngIf="datosCobrador; else noData">
          <div class="col-9">
            <div class="row">
              <mat-form-field class="col" appearance="outline">
                <mat-label>Código</mat-label>
                <input
                  matInput
                  [value]="datosCobrador.codigo_cobrador"
                  disabled
                />
              </mat-form-field>
              <mat-form-field class="col" appearance="outline">
                <mat-label>Nombre</mat-label>
                <input
                  matInput
                  [value]="datosCobrador.nombre_combrador"
                  disabled
                />
              </mat-form-field>
              <mat-form-field class="col" appearance="outline">
                <mat-label>Apellido</mat-label>
                <input
                  matInput
                  [value]="datosCobrador.apellido_cobrador"
                  disabled
                />
              </mat-form-field>
            </div>
            <div class="row">
              <mat-form-field class="col" appearance="outline">
                <mat-label>Estado</mat-label>
                <input matInput [value]="datosCobrador.estado" disabled />
              </mat-form-field>
              <mat-form-field class="col" appearance="outline">
                <mat-label>I.S.R.</mat-label>
                <input matInput [value]="datosCobrador.isr" disabled />
              </mat-form-field>
              <mat-form-field class="col" appearance="outline">
                <mat-label>Tipo de Cobrador</mat-label>
                <input matInput [value]="datosCobrador.tipo" disabled />
              </mat-form-field>
            </div>
          </div>
          <div class="col-4 text-center">
            <p>Direccion:</p>
            <p>{{ datosCobrador.direccion }}</p>
          </div>
        </div>
        <ng-template #noData>
          <div class="text-center">
            <h1>No hay datos</h1>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>
