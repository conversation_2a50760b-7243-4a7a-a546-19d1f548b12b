import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { UserService } from "src/app/_services/user.service";
import Swal from "sweetalert2";
import { loginServices } from "../../../login/login.service";

@Component({
  selector: "app-verify-credential-modal",
  templateUrl: "./verify-credential-modal.component.html",
  styleUrls: ["./verify-credential-modal.component.css"],
})
export class VerifyCredentialModalComponent implements OnInit {
  formUser = new FormGroup({
    user: new FormControl("", Validators.required),
    password: new FormControl("", Validators.required),
  });

  isShowingMessage = false;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { roles: string[] } },
    private loginService: loginServices,
    private userService: UserService,
    private dialogRef: MatDialogRef<VerifyCredentialModalComponent>,
  ) {}

  ngOnInit() {}

  verify() {
    const obj = {
      codusr: this.formUser.get("user")?.value,
      clave: this.formUser.get("password")?.value,
      op: "conectar",
    };
    this.loginService.login(obj).subscribe((data) => {
      if (data.access_token) {
        if (this.verificacionUsuario(data.access_token)) {
          if (this.isShowingMessage) return;
          this.isShowingMessage = true;
          Swal.fire({
            title: "Correcto",
            text: "Usuario verificado",
            icon: "success",
            confirmButtonText: "Aceptar",
            timer: 2000,
          }).then(() => {
            this.isShowingMessage = false;
          });
          this.dialogRef.close(true);
        } else {
          if (this.isShowingMessage) return;
          this.isShowingMessage = true;
          Swal.fire({
            title: "Error",
            text: "Error al verificar usuario, no coincide con el usuario actual",
            icon: "error",
            confirmButtonText: "Aceptar",
            timer: 2000,
          }).then(() => {
            this.isShowingMessage = false;
          });
        }
      } else {
        if (this.isShowingMessage) return;
        this.isShowingMessage = true;
        Swal.fire({
          title: "Error",
          text: "Credenciales incorrectas",
          icon: "error",
          confirmButtonText: "Aceptar",
          timer: 2000,
        }).then(() => {
          this.isShowingMessage = false;
        });
        this.dialogRef.close();
      }
    });
  }

  verificacionUsuario(token: string) {
    const decoded = this.userService.getDecodedToken().sub;
    const decodedByToken = this.userService.getDecodedToken(token).sub;

    const rol = this.userService.getDecodedToken(token).rol;

    let isRolValid = false;
    if (this.data.element.roles.includes(rol)) {
      isRolValid = true;
    } else {
      this.isShowingMessage = true;
      Swal.fire({
        title: "Error",
        text: "No tiene permisos para acceder a esta funcionalidad",
        icon: "error",
        confirmButtonText: "Aceptar",
        timer: 2000,
      }).then(() => {
        this.isShowingMessage = false;
      });
    }
    return decoded === decodedByToken && isRolValid;
  }
}
