<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>
        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="table-responsive mat-elevation-z8"
          perfectScrollbar
        >
          <!-- idcargamasiva Column -->
          <ng-container matColumnDef="idcargamasiva">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              ID Carga
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.idcargamasiva }}
            </td>
          </ng-container>

          <!-- estado Column -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
            <td mat-cell *matCellDef="let element">{{ element.estado }}</td>
          </ng-container>

          <!-- fecha Column -->
          <ng-container matColumnDef="fecha">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Fecha</th>
            <td mat-cell *matCellDef="let element">
              {{ element.fechacarga }}
            </td>
          </ng-container>

          <!-- tipo_carga Column -->
          <ng-container matColumnDef="tipo_carga">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tipo Carga
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.descripcion_tipocarga }}
            </td>
          </ng-container>

          <!-- tipo_facturacion Column -->
          <ng-container matColumnDef="tipo_facturacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tipo Facturación
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.descripcion_tipofacturacion }}
            </td>
          </ng-container>

          <!-- usuario Column -->
          <ng-container matColumnDef="usuario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Usuario</th>
            <td mat-cell *matCellDef="let element">
              {{ element.usuario }}
            </td>
          </ng-container>

          <!-- poliza Column -->
          <ng-container matColumnDef="poliza">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Póliza</th>
            <td mat-cell *matCellDef="let element">
              {{ element.Poliza }}
            </td>
          </ng-container>

          <!-- total_asegurada Column -->
          <ng-container matColumnDef="total_asegurada">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Suma</th>
            <td mat-cell *matCellDef="let element">
              {{ element.Total_Suma_Asegurada | currency:'USD':' ' }}
            </td>
          </ng-container>

          <!-- total_prima Column -->
          <ng-container matColumnDef="total_prima">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Total Prima</th>
            <td mat-cell *matCellDef="let element">
              {{ element.Total_Prima | currency:'USD':' ' }}
            </td>
          </ng-container>

          <!-- Action Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-raised-button
                color="basic"
                (click)="onDetail(element)"
                matTooltip="Detalle"
                matTooltipClass="tooltip-red"
              >
                Detalle <mat-icon>subject</mat-icon>
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="primary"
                (click)="onProcess(element)"
                matTooltip="Procesar"
                matTooltipClass="tooltip-red"
              >
                Procesar
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="accent"
                (click)="onCalculate(element)"
                matTooltip="Ver Calculo"
                matTooltipClass="tooltip-red"
              >
                Ver Calculo
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="accent"
                (click)="onDelete(element)"
                matTooltip="Eliminar"
                matTooltipClass="tooltip-red"
              >
                Eliminar
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
