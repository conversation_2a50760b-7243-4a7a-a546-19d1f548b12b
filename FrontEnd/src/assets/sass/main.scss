@use "@angular/material" as mat;
@use "~ag-grid-community/styles" as ag;
@use '@ng-matero/extensions' as mtx;

@include mat.core();

// AG-GRID
@import "ag-grid-community/styles/ag-grid.css";
@import "ag-grid-community/styles/ag-theme-alpine.css";

@import "scss/bootstrap.scss";
@import "scss/bootstrap-extended.scss";
@import "scss/colors.scss";
@import "scss/components.scss";
//  @import "scss/custom-rtl.scss";
@import "scss/core/menu/navigation";
@import "scss/core/menu/menu-types/vertical-menu-modern.scss";
@import "scss/core/menu/menu-types/horizontal-menu.scss";
@import "scss/core/colors/palette";
@import "scss/core/colors/palette-gradient.scss";
@import "scss/core/colors/palette-callout.scss";
@import "scss/plugins/loaders/loaders.scss";
@import "scss/plugins/animate/animate.scss";
@import "scss/pages/timeline.scss";
@import "scss/pages/login-register.scss";
@import "scss/pages/app-todo.scss";
@import "scss/pages/app-todoapp.scss";
@import "scss/pages/app-contacts.scss";
@import "scss/pages/app-email.scss";
@import "scss/pages/app-chat.scss";
@import "scss/pages/users.scss";
@import "scss/pages/news-feed.scss";
@import "scss/pages/user-feed.scss";
@import "scss/pages/gallery.scss";
@import "scss/pages/search.scss";
@import "scss/pages/invoice.scss";
@import "scss/pages/error.scss";
@import "scss/pages/coming-soon.scss";
@import "scss/pages/under-maintenance.scss";
@import "scss/commansocial.scss";
@import "scss/_palette";

th.mat-sort-header {
  color: #f5efef;
  background-color: #0555c6 !important;
}

.table-responsive {
  display: block;
  width: 100%;
  position: relative;
  .mat-table {
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem;
    display: table;
    border-collapse: collapse;
    margin: 0px;
  }
  .mat-row,
  .mat-header-row {
    display: table-row;
  }
  .mat-cell,
  .mat-header-cell {
    word-wrap: initial;
    display: table-cell;
    padding: 0px 5px;
    line-break: unset;
    /*width: auto;*/
    white-space: nowrap;
    overflow: hidden;
    vertical-align: middle;
  }
}

.row-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(25rem, 1fr));
  gap: 1rem;
}

.col-grid {
  padding: 1rem;
}
