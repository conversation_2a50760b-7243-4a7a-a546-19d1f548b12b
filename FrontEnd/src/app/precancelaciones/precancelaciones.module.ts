import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { PrecancelacionesComponent } from "./pages/precancelaciones/precancelaciones.component";
import { DetallePrecancelacionesComponent } from "./pages/precancelaciones/detalle-precancelaciones/detalle-precancelaciones.component";
import { NuevoPrecancelacionesComponent } from "./pages/precancelaciones/nuevo-precancelaciones/nuevo-precancelaciones.component";
import { MaterialModule } from "../material.module";
import { ReactiveFormsModule } from "@angular/forms";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";
import { PrecancelacionesRoutingModule } from "./precancelaciones-routing.module";

@NgModule({
  declarations: [
    PrecancelacionesComponent,
    DetallePrecancelacionesComponent,
    NuevoPrecancelacionesComponent,
  ],
  exports: [PrecancelacionesComponent],
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    PerfectScrollbarModule,
    PrecancelacionesRoutingModule,
  ],
})
export class PrecancelacionesModule {}
