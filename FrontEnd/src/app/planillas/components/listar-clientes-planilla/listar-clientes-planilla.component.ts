import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { TableColumn } from "src/app/components/icore-table/table";
import Swal from "sweetalert2";
import { CodigoPlanillaClienteService } from "../../services/codigo-planilla-cliente.service";

@Component({
  selector: "app-listar-clientes-planilla",
  templateUrl: "./listar-clientes-planilla.component.html",
  styleUrls: ["./listar-clientes-planilla.component.css"],
})
export class ListarClientesPlanillaComponent implements OnInit {
  dataTable;

  displayedColumns: TableColumn[] = [
    {
      columnDef: "id",
      header: "ID",
    },
    {
      columnDef: "cliente",
      header: "Cliente",
    },
    {
      columnDef: "actions",
      header: "Acciones",
      actions: [
        {
          color: "warn",
          icon: "delete",
          tooltipText: "Eliminar",
          onClick: ({ row }) => {
            this.desasignarCliente(row.id);
          },
        },
      ],
    },
  ];

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { data: any } },
    private codigoPlanillaClienteService: CodigoPlanillaClienteService,
    private dialogRef: MatDialogRef<ListarClientesPlanillaComponent>,
  ) {}

  ngOnInit() {
    this.dataTable = this.data.element.data;
  }

  onClose() {
    this.dialogRef.close();
  }

  desasignarCliente(id: string) {
    this.codigoPlanillaClienteService.desasignarCliente(id).subscribe(() => {
      Swal.fire("Cliente desasignado", "", "success");
      this.dialogRef.close(true);
    });
  }
}
