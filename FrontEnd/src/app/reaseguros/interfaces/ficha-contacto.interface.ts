export interface IFichaContactoResponse extends IFichaContacto {
  correo?: string;
  telefono?: string;
  direccion?: string;
}

export interface IFichaContacto {
  id?: number;
  codigo: string;
  nombre: string;
  descripcion: string;
  codigo_trans: string;
}

export interface IContacto_Correo {
  id?: number;
  contacto_id: number;
  correo: string;
  principal: boolean;
}

export interface IContacto_Telefono {
  id?: number;
  contacto_id: number;
  telefono: string;
  principal: boolean;
}

export interface IContacto_Direccion {
  id?: number;
  contacto_id: number;
  direccion: string;
  principal: boolean;
}
