import { Component, OnInit, ViewChild } from "@angular/core";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { UserService } from "../_services/user.service";
import { ObligacionService } from "./obligacion.service";
import { CompromisoPago } from "./compromiso-pago";

import { global } from "../_services/global";
import swal from "sweetalert2";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { FormularioComponent } from "./formulario/formulario.component";
import { DocumentoIngresoHistoricoComponent } from "./documento-ingreso-historico/documento-ingreso-historico.component";
import { SelectionModel } from "@angular/cdk/collections";

@Component({
  selector: "app-compromiso-pago",
  templateUrl: "./compromiso-pago.component.html",
  styleUrls: ["./compromiso-pago.component.css"],
  providers: [UserService, ObligacionService],
})
export class CompromisoPagoComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  //public acreencia: EntidadAcreedora;
  public dataSource = null;
  public isDisabled: boolean = true;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;
  selection = new SelectionModel<any>(true, []);

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _userService: UserService,
    private _obligacionService: ObligacionService,
    private dialog: MatDialog
  ) {
    this.page_title = "Listado de Compromisos de Pago (Obligaciónes)";
    this.identity = this._userService.getIdentity();
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "select",
    "id",
    "stsoblig",
    "tipooblig",
    "nombre",
    "codmoneda",
    "mtonetoobligmoneda",
    "sldoobligmoneda",
    "fecsts",
    "idreling",
    "actions" /*, 'new'*/,
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getObligaciones();
  }

  getObligaciones() {
    console.log("getObligaciones");
    let obligacion: any[] = [];
    this._obligacionService.getObligaciones().subscribe(
      (response) => {
        //console.log('response',response);
        if (response.status == "success") {
          response.obligacion.forEach((dato) => {
            obligacion.push({
              id: dato.id,
              idcliente: dato.idcliente,
              nombre: dato.cliente?.nombre ? dato.cliente.nombre : null,
              //dato.cliente.nombre,
              //cantidadDetalle: this.getCantidadDetalle(dato.id),
              tipooblig: dato.tipooblig,
              stsoblig: dato.stsoblig,
              fecsts: dato.fecsts,
              fecgtiapago: dato.fecgtiapago,
              textoblig: dato.textoblig,
              codmoneda: dato.codmoneda,
              mtonetoobliglocal: dato.mtonetoobliglocal,
              mtobrutoobligmoneda: dato.mtobrutoobligmoneda,
              mtonetoobligmoneda: dato.mtonetoobligmoneda,
              mtobrutoobliglocal: dato.mtobrutoobliglocal,
              dptoemi: dato.dptoemi,
              sldoobliglocal: dato.sldoobliglocal,
              sldoobligmoneda: dato.sldoobligmoneda,
              idesin: dato.idesin,
              idintermediario: dato.idintermediario,
              idreling: dato.idreling,
              numoper: dato.numoper,
              idrelegre: dato.idrelegre,
              notacreditofiscal: dato.notacreditofiscal,
              numobligant: dato.numobligant,
              fecanul: dato.fecanul,
              emitecheque: dato.emitecheque,
              serie: dato.serie,
              numnotadb: dato.numnotadb,
              cae: dato.cae,
              numnotachq: dato.numnotachq,
            });
          });
        }
        this.dataSource = new MatTableDataSource<CompromisoPago>(obligacion);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  onDelete(id) {
    //console.log('Eliminar', id);
    swal
      .fire({
        title: "¿Estas seguro?",
        text: "Una vez borrado no podrás recuperarlo!",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete) {
          this._obligacionService.delete(id).subscribe(
            (response) => {
              swal.fire({ text: "La acreencia fue borrada!", icon: "success" });
              this.getObligaciones();
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }

  onEdit(element) {
    console.log("onEdit");
    this.openDialogo(element, "edit");
  }

  onNew(element?: any) {
    console.log("onNew");
    this.openDialogo(element, "new");
  }

  onActivated(element) {
    console.log("onActivated");
    this.getCantidadDetalle(element);
  }

  onDetail(element) {
    this._router.navigate([
      "caja/compromisopago/detallecompromisopago/" + element.id,
    ]);
  }

  onAnular(element) {
    console.log("onAnular");
    this.cambiarEstadoObligacion(element);
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  openDialogo(element: CompromisoPago = null, tipo: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      title:
        tipo == "new"
          ? `Nueva Obligacion`
          : `Modificacion de Datos de la Obligacion No. ${element.id}`,
      element: element,
    };
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "900px";
    dialogConfig.width = "500px";

    const dialogRef = this.dialog.open(FormularioComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      //console.log(`Dialog result ${result}`);
      this.getObligaciones();
    });
  }

  relacionIngresoCAOHistorial(
    element: CompromisoPago = null,
    tipo: string
  ): void {
    const dialogConfig = new MatDialogConfig();
    //console.log('element', element);
    dialogConfig.data = {
      title: `Listado de Documentos Ingreso, Obligación No.: ${element.id} Monto: ${element.mtonetoobligmoneda} Saldo: ${element.sldoobligmoneda}`,
      element: element,
    };
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "400px";
    dialogConfig.width = "1400px";

    const dialogRef = this.dialog.open(
      DocumentoIngresoHistoricoComponent,
      dialogConfig
    );
    /*dialogRef.afterClosed().subscribe(result => {
      //console.log(`Dialog result ${result}`);      
      this.getObligaciones();
    });*/
  }

  cambiarEstadoObligacion(obligacion) {
    console.log("cambiarEstadoObligacion");
    swal
      .fire({
        title: "¿Cambiar Estado de la  Obligacion?",
        text: "Deseas Cambiar el Estado del Compromiso de Pago?",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete.isConfirmed) {
          this._obligacionService.cambiarEstadoObligacion(obligacion).subscribe(
            (response) => {
              swal.fire({
                text: "Proceso finalizado correctamente!",
                icon: "success",
              });
              this.getObligaciones();
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        }
      });
  }

  getCantidadDetalle(element): void {
    let $cantidad: number = 0;
    this._obligacionService
      .getCantidadDetalle(element.id)
      .subscribe((response) => {
        //console.log('response.cantidad',response.cantidad);
        if (response.cantidad == 0) {
          swal.fire(
            "¿Cambiar Estado de la  Obligacion?",
            "No Tiene Detalle, Por Favor Verifique",
            "error"
          );
        } else {
          this.cambiarEstadoObligacion(element);
        }
      });
  }

  onDisabledBtnActivated(stsoblig) {
    if (stsoblig == "VAL") return false;
    else return true;
  }

  onDisabledBtnCancel(stsacre) {
    if (stsacre == "ACT") return false;
    else return true;
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource?.data.length;
    return numSelected === numRows;
  }

  /** Selects all rows if they are not all selected; otherwise clear selection. */
  masterToggle() {
    if (this.isAllSelected()) {
      this.selection.clear();
      return;
    }

    this.selection.select(...this.dataSource.data);
  }

  /** The label for the checkbox on the passed row */
  checkboxLabel(row?: any): string {
    if (!row) {
      return `${this.isAllSelected() ? "deselect" : "select"} all`;
    }
    return `${this.selection.isSelected(row) ? "deselect" : "select"} row ${
      row.position + 1
    }`;
  }
}
