<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <div class="table-responsive" perfectScrollbar>
          <table class="table-detalle">
            <tr>
              <td>No. Cotización: {{ this.vwCotizacion.idcotizacion || 0 }}</td>
              <td>Estado: {{ this.vwCotizacion.estado }}</td>
            </tr>
            <tr>
              <td>Póliza: {{ this.vwCotizacion.Poliza }}</td>
              <td>
                Fecha Cotización: {{ this.vwCotizacion.fecha_cotizacion }}
              </td>
            </tr>
            <tr>
              <td>Marca: {{ this.vwCotizacion.descripcion_marca }}</td>
              <td>Modelo: {{ this.vwCotizacion.descripcion_modelo }}</td>
            </tr>
            <tr>
              <td>Cliente: {{ this.vwCotizacion.nombre_cliente }}</td>
              <td>Tipo Vehículo: {{ this.vwCotizacion.tipovehiculo }}</td>
            </tr>
            <tr>
              <td>CC: {{ this.vwCotizacion.cc }}</td>
              <td>
                Cantidad puertas: {{ this.vwCotizacion.cantidad_puertas }}
              </td>
            </tr>
            <tr>
              <td>Transmisión: {{ this.vwCotizacion.transmision }}</td>
              <td>Año: {{ this.vwCotizacion["año"] }}</td>
            </tr>
            <tr>
              <td>No. Reclamo: {{ this.vwCotizacion.no_reclamo }}</td>
              <td>Usuario: {{ this.vwCotizacion.usuario }}</td>
            </tr>
          </table>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-back">
            <button-back></button-back>
          </div>

          <div class="btn-back">
            <button
              mat-flat-button
              color="accent"
              (click)="openDialogoRepuesto()"
            >
              Ingresar varios<mat-icon>add</mat-icon>
            </button>
          </div>
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div class="table-responsive" perfectScrollbar>
          <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="mat-elevation-z8"
          >
            <!-- Id Column -->
            <ng-container matColumnDef="iddetalle">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Id</th>
              <td mat-cell *matCellDef="let element">
                {{ element.iddetalle }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <!-- CODREPUESTO Column -->
            <ng-container matColumnDef="idrepuesto">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Repuesto
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.idrepuesto }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <!-- DESCREPUESTO Column -->
            <ng-container matColumnDef="descripcion_repuesto">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Descripcion
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.descripcion_repuesto }}
              </td>
              <td mat-footer-cell *matFooterCellDef>TOTAL</td>
            </ng-container>

            <!-- TIPOREPUESTO Column -->
            <ng-container matColumnDef="precio_autorizado">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Precio Autorizado
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.precio_autorizado }}
              </td>
              <td mat-footer-cell *matFooterCellDef>
                {{ getTotalMoneda() | currency : " " : "code" }}
              </td>
            </ng-container>

            <ng-container matColumnDef="oferta_menor">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Oferta Menor
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.oferta_menor }}
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <!-- DESCRIPCION_CLASE Column -->
            <ng-container matColumnDef="estado">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
              <td mat-cell *matCellDef="let element">{{ element.estado }}</td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
              <td mat-cell *matCellDef="let element">
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onEdit(element)"
                  matTooltip="Editar Detalle Cotizacion"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onDelete(element)"
                  matTooltip="Eliminar Detalle Cotizacion"
                  matTooltipClass="tooltip-red"
                >
                  Eliminar<mat-icon>delete</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onOfertas(element)"
                  matTooltip="Ver Ofertantes"
                  matTooltipClass="tooltip-red"
                >
                  Ver ofertas <mat-icon>local_offer</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onAceptar(element)"
                  matTooltip="Aceptar"
                  matTooltipClass="tooltip-red"
                  [disabled]="onDisabledBtnStatus(element)"
                >
                  Aceptar <mat-icon>check_circle</mat-icon>
                </button>
              </td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
            <tr
              mat-footer-row
              *matFooterRowDef="displayedColumns; sticky: true"
            ></tr>
          </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
