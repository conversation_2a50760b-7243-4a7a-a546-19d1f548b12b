<form [formGroup]="form">
  <div class="page_title">
    <h1>{{page_title}}</h1>
  </div>
  <hr/>

  <mat-grid-list cols="2" rowHeight="150px" gutterSize="1px">
  
    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Numero de Deposito" name="numdep" formControlName="numdep">
        <!-- <input style="visibility: hidden; display: none;" name = "numdep" formControlName="numdep" > -->
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Numero de Boleta" name="numero_boleta" formControlName="numero_boleta">
        <!-- <input style="visibility: hidden; display: none;" name = "numero_boleta" formControlName="numero_boleta" > -->
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select name="stsdep" placeholder="Estatus" formControlName="stsdep">
          <mat-option *ngFor="let Estado of estados" [value]="Estado.id">
            {{Estado.name}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select name="identfinan" placeholder="Entidad Financiero" formControlName="identfinan">
          <mat-option *ngFor="let Entidad of entidadesfin" [value]="Entidad.identfinan"  (click) = "getCuentaFinanciera(Entidad.identfinan)">
            {{Entidad.descentfinan}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <mat-select name="idcuentabancaria" placeholder="Cuenta de Banco" formControlName="idcuentabancaria">
          <mat-option *ngFor="let Cuentas of cuentabancaria" [value]="Cuentas.idcuentabancaria">
            {{Cuentas.numero_cuenta}}
          </mat-option>
        </mat-select>
      </mat-form-field>
    </mat-grid-tile>

    <mat-grid-tile>
      <mat-form-field>
        <input matInput placeholder="Fecha Ingreso" name="fecsts" formControlName="fecsts" readonly> 
      </mat-form-field>
    </mat-grid-tile>
    
    <mat-grid-tile>
        <mat-form-field>
      <mat-select name="codmoneda" placeholder="Moneda" formControlName="codmoneda">
        <mat-option *ngFor="let monedas of monedas" [value]="monedas.id">
          {{monedas.name}}
        </mat-option>
       </mat-select>
       </mat-form-field>
      </mat-grid-tile> 

      <mat-grid-tile>
        <mat-form-field>
          <input matInput placeholder="Descripción" name="textmotvanul" formControlName="textmotvanul">
        </mat-form-field>
      </mat-grid-tile>

      <mat-grid-tile>
        <mat-form-field>
          <input matInput placeholder="Monto" name="mtodeplocal" formControlName="mtodeplocal" readonly>
        </mat-form-field>
      </mat-grid-tile>
      
       <!-- <mat-grid-tile>
        <mat-dialog-actions>
          <button mat-flat-button color="primary" (click)="close()">
            <mat-icon>highlight_off</mat-icon>Cerrar
          </button>
        </mat-dialog-actions>
      </mat-grid-tile> -->

      <mat-grid-tile>
        <mat-dialog-actions>
          <button mat-flat-button  color="primary"(click)="save(form.value)" [disabled]="!form.valid">
            <mat-icon>save</mat-icon>Guardar
          </button>
        </mat-dialog-actions>
      </mat-grid-tile>
  </mat-grid-list>
</form>