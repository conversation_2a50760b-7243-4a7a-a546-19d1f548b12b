<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8 p-2">
        <div class="button-container">
          <button-back></button-back>
          <button mat-raised-button (click)="openModal()" matTooltip="Descargar reporte de comisiones netas"
            class="mb-2 download-button">
            <mat-icon>cloud_download</mat-icon>
            Descargar Reporte
          </button>
        </div>

        <h4 class="text-center">Comisiones Netas</h4>
        <h4 class="text-center">
          {{ nombre }}
        </h4>
        <h5 class="text-center">
          Total {{ totalComisionOrigenUSD | currency: "USD: " }}
          Total {{ totalComisionOrigenQ | currency: "QTZ: " }}
        </h5>

        <icore-table *ngIf="data" [columns]="columns" [data]="data" [length]="data.length" [filter]="true">
          'No hay datos para mostrar'
        </icore-table>

        <ngx-skeleton-loader *ngIf="!data" [count]="10"></ngx-skeleton-loader>
      </div>
    </div>
  </div>
</div>