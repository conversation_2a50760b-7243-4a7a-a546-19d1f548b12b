import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { global } from './../../_services/global';

@Injectable({
  providedIn: 'root'
})
export class CargaMasivaService {

  public url: string;

  constructor(private _http: HttpClient) {
    this.url = global.url;
  }

  getVwCargaMasiva(): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.get(this.url+'carga-masiva', {headers: headers});
  }

  getCargaMasivaById(id: number): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.post(this.url+`carga-masiva/${id}`, null, {headers: headers});
  }

  getParametro(codigo): Observable<any>{
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.post(this.url+`lval/catalogo/${codigo}`, null, {headers: headers});
  }
  
  postProcessCargaMasiva(data: any): Observable<any>{
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.post(this.url+`carga-masiva/data/create`, data, {headers: headers});
  }

  postUploadCargaMasiva(id: number, path: any): Observable<any>{
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.post(this.url+`carga-masiva/data/carga/${id}`, {path: path}, {headers: headers});
  }

  getDetalleCargaMasivaById(id: number): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.get(this.url+`detalle-carga-masiva/data/${id}`, {headers: headers});
  }

  postDetalleCargaMasivaDelete(id: number): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.post(this.url+`detalle-carga-masiva/${id}`, null, {headers: headers});
  }

  putDetalleCargaMasivaDone(id: number): Observable<any>{   
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.put(this.url+`detalle-carga-masiva/${id}`, null, {headers: headers});
  }

}
