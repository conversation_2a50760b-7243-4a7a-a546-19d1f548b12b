<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-grid-list cols="3" rowHeight="65px" gutterSize="1px">
          <mat-grid-tile>
            <mat-form-field>
              <mat-select
                name="configuracion"
                placeholder="Tipo Configuración"
                (selectionChange)="onChange($event.value)"
                required
              >
                <mat-option
                  *ngFor="let tipoc of tipoConfiguracion"
                  [value]="tipoc.id_configuracion_morosidad"
                >
                  {{ tipoc.id_configuracion_morosidad }} - {{ tipoc.TipoRangoMorosidad }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </mat-grid-tile>
        </mat-grid-list>
        <hr />
        <table class="table-detalle">
            <tr>
              <td>Tipo Rango Morosidad: {{ tipoData.TipoRangoMorosidad }}</td>
              <td>Tipo Cobrador: {{ tipoData.tipocobrador }}</td>
            </tr>
            <tr>
              <td>Rango Inicial: {{ tipoData.RangoInicial }}</td>
              <td>Rango Final: {{ tipoData.RangoFinal }}</td>
            </tr>
            <tr>
              <td>Ind Cancela: {{ tipoData.indcancelar }}</td>
              <td>Ind Agente: {{ tipoData.indicadoragente }}</td>
            </tr>
            <tr>
              <td>Ind Asegurado: {{ tipoData.indAsegurado }}</td>
              <td>Tipo Riesgo: {{ tipoData.tiporiesgo }}</td>
            </tr>
            <tr>
              <td>Tipo Agente: {{ tipoData.tipoagente }}</td>
              <td>Medio de Pago: {{ tipoData.mediopago }}</td>
            </tr>
        </table>
        <div class="container-btn-new">
          <div class="btn-back">
            <button mat-flat-button color="primary" (click)="onBack()">
              Regresar<mat-icon>back</mat-icon>
            </button>
          </div>
          <div class="btn-back">
            <button mat-flat-button [disabled] = "idTipo == 0" (click)="onPreview()">
            Vista Previa<mat-icon>preview</mat-icon>
            </button>
        </div>
        <div class="btn-new">
            <button mat-flat-button [disabled] = "idTipo == 0" color="accent" (click)="onProcess()">
            Procesar<mat-icon>save</mat-icon>
            </button>
        </div>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="mat-elevation-z8"
        >
          <!-- morosidad Column -->
          <ng-container matColumnDef="morosidad">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Morosidad</th>
            <td mat-cell *matCellDef="let element">{{ element.Morosidad }}</td>
          </ng-container>

          <!-- poliza Column -->
          <ng-container matColumnDef="poliza">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Póliza</th>
            <td mat-cell *matCellDef="let element">{{ element.poliza_aseguradora }}</td>
          </ng-container>

          <!-- id_intermediario Column -->
          <ng-container matColumnDef="id_intermediario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Intermediario
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.id_intermediario }}
            </td>
          </ng-container>

          <!-- email_corredor Column -->
          <ng-container matColumnDef="email_corredor">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Email Agente
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.emailagente }}
            </td>
          </ng-container>

          <!-- cobrador Column -->
          <ng-container matColumnDef="cobrador">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Cobrador</th>
            <td mat-cell *matCellDef="let element">
              {{ element.cobrador }}
            </td>
          </ng-container>

          <!-- operacion Column -->
          <ng-container matColumnDef="operacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Operación</th>
            <td mat-cell *matCellDef="let element">{{ element.operacion }}</td>
          </ng-container>

          <!-- dias_vence Column -->
          <ng-container matColumnDef="dias_vence">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Días Vence
            </th>
            <td mat-cell *matCellDef="let element">{{ element.dias_vence }}</td>
          </ng-container>

          <!-- fecvenc Column -->
          <ng-container matColumnDef="fecvenc">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Fecha Vence
            </th>
            <td mat-cell *matCellDef="let element">{{ element.fecvenc }}</td>
          </ng-container>

          <!-- nopago Column -->
          <ng-container matColumnDef="nopago">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>No. Pago</th>
            <td mat-cell *matCellDef="let element">{{ element.no_pago }}</td>
          </ng-container>

          <!-- email_asegurado Column -->
          <ng-container matColumnDef="email_asegurado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Email Asegurado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.email }}
            </td>
          </ng-container>

          <!-- tel1 Column -->
          <ng-container matColumnDef="tel1">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tel 1</th>
            <td mat-cell *matCellDef="let element">
              {{ element.tel1 }}
            </td>
          </ng-container>

          <!-- tel2 Column -->
          <ng-container matColumnDef="tel2">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Tel 2</th>
            <td mat-cell *matCellDef="let element">
              {{ element.tel2 }}
            </td>
          </ng-container>

          <!-- nombreaseurado Column -->
          <ng-container matColumnDef="nombreaseurado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Nombre Asegurado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.nombre_asegurado }}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
