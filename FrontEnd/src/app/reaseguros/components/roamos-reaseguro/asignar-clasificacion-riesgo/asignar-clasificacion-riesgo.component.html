<h1>Asignar clasificaciones de riesgo</h1>
<div class="p-2" *ngIf="data$ | async as data; else loading">
  <icore-table
    [columns]="displayedColumns"
    [data]="data"
    [length]="data.length"
    [filter]="true"
  >
    <h1>No hay clasificaciones de riesgo</h1>
  </icore-table>
</div>

<ng-template #loading>
  <div class="mx-2">
    <ngx-skeleton-loader
      [count]="displayedColumns.length"
      appearance="line"
    ></ngx-skeleton-loader></div
></ng-template>
