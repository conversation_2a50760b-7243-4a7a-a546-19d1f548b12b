<h2 mat-dialog-title>No.Cotización: {{ idcotizacion }}</h2>

<mat-dialog-content class="mat-typography">
  <div>
    <div *ngFor="let pago of detalle">
      <div>
        <mat-selection-list #pagos [multiple]="false">
          <mat-list-option
            (click)="onPagos(pago.rubros, pago.infoPlanFracc.plan)"
          >
            {{ pago.infoPlanFracc.plan }} - {{ moneda }}
            {{ pago.pagos | number : "1.2-2" }}
          </mat-list-option>
        </mat-selection-list>
        <mat-divider inset></mat-divider>
      </div>
    </div>
  </div>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button [mat-dialog-close]="true" cdkFocusInitial>Cerrar</button>
</mat-dialog-actions>
