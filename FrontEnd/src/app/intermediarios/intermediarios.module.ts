import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { ReactiveFormsModule } from "@angular/forms";
import { RouterModule } from "@angular/router";
import { PerfectScrollbarModule } from "ngx-perfect-scrollbar";

import { CardModule } from "../content/partials/general/card/card.module";

import { MAT_MOMENT_DATE_ADAPTER_OPTIONS } from "@angular/material-moment-adapter";
import { MAT_DATE_FORMATS, MAT_DATE_LOCALE } from "@angular/material/core";
import { MTX_DATETIME_FORMATS } from "@ng-matero/extensions/core";
import { AgGridModule } from "ag-grid-angular";
import { NgxSkeletonLoaderModule } from "ngx-skeleton-loader";
import { ToastrModule, ToastrService } from "ngx-toastr";
import { CobradoresModule } from "../cobradores/cobradores.module";
import { ComisionEspecialComponent } from "../comisiones/comision-especial/comision-especial.component";
import { ComponentsModule } from "../components/components.module";
import { MaterialModule } from "../material.module";
import { PersonasModule } from "../personas/personas.module";
import { SharedModule } from "../shared/shared.module";
import { FormularioCanalesComponent } from "./formulario-canales/formulario-canales.component";
import { FormularioCobradorComponent } from "./formulario-cobrador/formulario-cobrador.component";
import { ConversionComisionesNetasComponent } from './pages/conversion-comisiones-netas/conversion-comisiones-netas.component';
import { FormularioCuentaIntermediarioComponent } from "./formulario-cuenta-intermediario/formulario-cuenta-intermediario.component";
import { FormularioComponent } from "./formulario/formulario.component";
import { IntermediariosRoutingModule } from "./intermediarios-routing.module";
import { IntermediariosComponent } from "./intermediarios.component";
import { ListOperinterComponent } from "./list-operinter/list-operinter.component";
import { CanalesDeNegocioComponent } from "./pages/canales-de-negocio/canales-de-negocio.component";
import { ConsultasFacturasClienteComponent } from "./pages/consultas-facturas-cliente/consultas-facturas-cliente.component";
import { ConsultasFacturasComponent } from "./pages/consultas-facturas/consultas-facturas.component";
import { DetalleIntermediarioComponent } from "./pages/detalle-intermediario/detalle-intermediario.component";
import { PasoDosIntermediarioComponent } from "./pages/detalle-intermediario/paso-dos/paso-dos.component";
import { PasoTresIntermediarioComponent } from "./pages/detalle-intermediario/paso-tres/paso-tres.component";
import { PasoUnoIntermediarioComponent } from "./pages/detalle-intermediario/paso-uno/paso-uno.component";
import { ProyeccionesComisionComponent } from "./pages/proyecciones-comision/proyecciones-comision.component";
import { TemplateRendererComponent } from "./templateRender.component";
import { ReporteComisionesNetasComponent } from './pages/conversion-comisiones-netas/reporte-comisiones-netas/reporte-comisiones-netas/reporte-comisiones-netas.component';
import { AnticipoComisionComponent } from "./pages/anticipo-comision/anticipo-comision.component";
import { DetalleOperacionComponent } from './detalle-operaciones/detalle-operacion.component';
import { DetalleObligacionComponent } from './detalle-obligaciones/detalle-obligacion.component';
import { ReprogramarPagosComponent } from "./reprogramar-pagos-anticipos/reprogramar-pago-anticipo-comision.component";

export const MY_DATE_FORMATS = {
  parse: {
    dateInput: "DD/MM/YYYY",
  },
  display: {
    dateInput: "DD/MM/YYYY",
    monthYearLabel: "MMMM YYYY",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "MMMM YYYY",
  },
};

@NgModule({
  declarations: [
    FormularioComponent,
    IntermediariosComponent,
    ComisionEspecialComponent,
    ConsultasFacturasComponent,
    ConsultasFacturasClienteComponent,
    ListOperinterComponent,
    CanalesDeNegocioComponent,
    FormularioCanalesComponent,
    FormularioCobradorComponent,
    FormularioCuentaIntermediarioComponent,
    TemplateRendererComponent,
    DetalleIntermediarioComponent,
    PasoUnoIntermediarioComponent,
    PasoDosIntermediarioComponent,
    PasoTresIntermediarioComponent,
    ProyeccionesComisionComponent,
    ConversionComisionesNetasComponent,
    ReporteComisionesNetasComponent,
    AnticipoComisionComponent,
    DetalleOperacionComponent,
    DetalleObligacionComponent,
    ReprogramarPagosComponent,
  ],
  imports: [
    CommonModule,
    CardModule,
    ComponentsModule,
    MaterialModule,
    NgxSkeletonLoaderModule,
    PerfectScrollbarModule,
    ReactiveFormsModule,
    IntermediariosRoutingModule,
    RouterModule,
    ToastrModule.forRoot(),
    SharedModule,
    CobradoresModule,
    AgGridModule,
    PersonasModule,
  ],
  providers: [
    {
      provide: ToastrService,
    },
    {
      provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS,
      useValue: { useUtc: true },
    },
    {
      provide: MTX_DATETIME_FORMATS,
      useValue: {
        parse: {
          dateInput: "YYYY-MM-DD",
          monthInput: "MMMM",
          yearInput: "YYYY",
          timeInput: "HH:mm",
          datetimeInput: "YYYY-MM-DD HH:mm",
        },
        display: {
          dateInput: "YYYY-MM-DD",
          monthInput: "MMMM",
          yearInput: "YYYY",
          timeInput: "HH:mm",
          datetimeInput: "YYYY-MM-DD HH:mm",
          monthYearLabel: "YYYY MMMM",
          dateA11yLabel: "LL",
          monthYearA11yLabel: "MMMM YYYY",
          popupHeaderDateLabel: "MMM DD, ddd",
        },
      },
    },
    {
      provide: MAT_DATE_LOCALE,
      useValue: "es-GT",
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: MY_DATE_FORMATS,
    },
  ],
})
export class IntermediariosModule {}
