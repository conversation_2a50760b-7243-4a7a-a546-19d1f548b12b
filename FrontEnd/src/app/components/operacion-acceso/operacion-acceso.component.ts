import { element } from "protractor";
import { OperUsuarioService } from "./operUsuario.service";
import { Usuario } from "../list-usuario/usuario";
//import { CajeroComponent } from './../cajero.component';
import { Component, OnInit, Inject } from "@angular/core";
import {
  FormGroup,
  FormControl,
  FormBuilder,
  Validators,
} from "@angular/forms";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { UserService } from "../../_services/user.service";
/*
import { Cajero } from '../cajero';
import { Caja } from '../../caja/caja';
import { Cliente } from '../../list-cliente/cliente';
*/
import { global } from "../../_services/global";
//ALERTS
import swal from "sweetalert2";
//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
// ABRIR DIALOGO DESDE UNA PANTALLA DE TIPO DIALOGO
import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { ListUsuarioComponent } from "../list-usuario/list-usuario.component";

@Component({
  selector: "app-operacion-acceso",
  templateUrl: "./operacion-acceso.component.html",
  styleUrls: ["./operacion-acceso.component.css"],
  providers: [UserService, OperUsuarioService],
})
export class OperacionAccesoComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public hide = true;
  public bTienePermiso: boolean = false;

  //public cajero: Cajero;
  //public cliente:  Cliente;

  //public cajas;

  public acreencias;

  constructor(
    public _operUsuarioAccesoService: OperUsuarioService,
    private _router: Router,
    private _route: ActivatedRoute,
    private _userService: UserService,
    private formBuilder: FormBuilder,
    private _dialogRef: MatDialogRef<OperacionAccesoComponent>, //DIALOGO
    private dialog: MatDialog, // ABRIR DIALOGO DESDE UNA PANTALLA DE TIPO DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = "Operación de Acceso";
    this.identity = this._userService.getIdentity();
    this.token = this._userService.getToken();
    this.url = global.url;
  }

  public form = this.formBuilder.group(
    {
      codusr: ["", Validators.required],
      usuario: [""],
      password: ["", Validators.required],
      monto: [""],
      operacion: [""],
    },
    { updateOn: "change" }
  );

  ngOnInit(): void {
    this.initValuesForm();
  }

  doValidaOperacionAcceso(form, event) {
    console.log("doValidaOperacionAcceso");
    event.preventDefault();
    //this._cajaService.getCajaUsuario(this.identity).subscribe(
    this._operUsuarioAccesoService.doValidaOperacionAcceso(form).subscribe(
      (response) => {
        //console.log('response', response);

        if (response.status == "success") {
          //this.cajas = response.cajas;
          this.bTienePermiso = true;
          this.close();
        } else {
          this.status = 'error';
          this.bTienePermiso = false;
          swal.fire(`Nombre de usuario ó Password escritos incorrectamente`);
        }
      },
      (error) => {
        //console.log(<any>error);
        this.bTienePermiso = false;
        swal.fire(`Nombre de usuario ó Password escritos incorrectamente`);
        this.status = 'error';
      }
    );
  }

  close(): void {
    this._dialogRef.close(<boolean>this.bTienePermiso);
  }

  private initValuesForm(): void {
    //console.log('data.element', this.data.element);
    if (this.data.operacion) {
      this.form.patchValue({
        codusr: this.data.codusr,
        usuario: this.data.codusr,
        monto: Math.abs(this.data.monto),
        operacion: this.data.operacion,
      });
    }
  }

  openDialogoUsuario(): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {};
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = false;
    dialogConfig.height = "550px";
    dialogConfig.width = "1500px";

    const dialogRef = this.dialog.open(ListUsuarioComponent, dialogConfig);

    dialogRef.afterClosed().subscribe((result: Usuario) => {
      //let cliente = {idcliente: result.idcliente, nombre: result.nombre};
      //console.log('cliente', cliente);
      if (result) {
        this.form.patchValue({
          codusr: result.codusr,
          usuario: result.nombre,
        });
      }
    });
  }
}
