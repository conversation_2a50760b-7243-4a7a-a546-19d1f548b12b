<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />

        <form [formGroup]="form">
          <div class="container">
            <h3 class="text-center">Datos Generales</h3>

            <div class="row">
              <div class="col">
                <p>ID: {{ form.value.idplanilla }}</p>
              </div>
            </div>

            <div class="row">
              <div class="col">
                <p>Estado: {{ form.value.stsplani }}</p>
              </div>
            </div>

            <div class="row">
              <p class="ml-1">Fecha: {{ form.value.fecstsplani }}</p>
            </div>

            <div class="row">
              <div class="col">
                <p>Usuario: {{ form.value.usuario }}</p>
              </div>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Moneda</mat-label>
                <mat-select
                  name="codmoneda"
                  placeholder="Moneda"
                  formControlName="codmoneda"
                >
                  <mat-option
                    *ngFor="let moneda of listCodigosMoneda"
                    [value]="moneda.valor"
                  >
                    {{ moneda.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Fecha Ingreso</mat-label>
                <input
                  matInput
                  [matDatepicker]="fecingplani"
                  placeholder="Fecha Ingreso"
                  name="fecingplani"
                  formControlName="fecingplani"
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="fecingplani"
                ></mat-datepicker-toggle>
                <mat-datepicker #fecingplani></mat-datepicker>
              </mat-form-field>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Tipo</mat-label>
                <mat-select
                  name="tipoplani"
                  placeholder="Tipo"
                  formControlName="tipoplani"
                >
                  <mat-option
                    *ngFor="let tipo of listTiposPlani"
                    [value]="tipo.valor"
                  >
                    {{ tipo.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Código</mat-label>
                <mat-select
                  name="codigo"
                  placeholder="Código"
                  formControlName="codigo"
                >
                  <mat-option
                    *ngFor="let codigo of listCodigosPlani"
                    [value]="codigo.valor"
                  >
                    {{ codigo.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Cliente</mat-label>
                <input
                  matInput
                  placeholder="PRESIONE EL ICONO"
                  name="cliente"
                  formControlName="cliente"
                  readonly
                />
                <button
                  mat-icon-button
                  color="accent"
                  matSuffix
                  (click)="openDialogoCliente()"
                >
                  <mat-icon>search</mat-icon>
                </button>
              </mat-form-field>
            </div>

            <div class="row">
              <mat-form-field class="col">
                <mat-label>Gestor de Cobro</mat-label>
                <mat-select
                  placeholder="Gestor de Cobro"
                  formControlName="gestor_cobro"
                >
                  <mat-option
                    *ngFor="let ejecutivo of ejecutivos$ | async"
                    [value]="ejecutivo.persona_id"
                  >
                    {{ ejecutivo.nombres }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            <div class="row">
              <mat-form-field class="col">
                <mat-label>Intermediario</mat-label>
                <mat-select
                  placeholder="Intermediario"
                  formControlName="intermediario"
                >
                  <mat-option
                    *ngFor="let intermediario of intermediarios$ | async"
                    [value]="intermediario.codigo"
                  >
                    {{intermediario.codigo}} | {{ intermediario.nombre }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
          </div>
          <div class="ml-2">
            <button mat-flat-button color="primary" matTooltip="Limpiar filtros" (click)="clearFiltros()">
              <mat-icon>clear_all</mat-icon>
            </button>
          </div>
        </form>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-botones">
          <button
            mat-flat-button
            class="btn-back"
            color="primary"
            (click)="onBack()"
          >
            Regresar <mat-icon>arrow_back</mat-icon>
          </button>

          <button
            mat-raised-button
            color="accent"
            (click)="openDialogoFacturas()"
            [disabled]="this.form.get('tipoplani')?.value == 'DNA'"
          >
            Sel. Manual de Facturas
          </button>

          <button
            mat-raised-button
            [disabled]="this.form.get('tipoplani')?.value != 'DNA'"
            color="basic"
            (click)="f_input.click()"
          >
            Cargar Cobro <mat-icon>upload</mat-icon>
            <input
              type="file"
              hidden
              accept=".txt"
              #f_input
              (change)="handleFileInputChange(f_input.files)"
            />
          </button>

          <button
            mat-flat-button
            color="primary"
            (click)="onSaveForm(form.value)"
            [disabled]="this.form.get('idplanilla')?.value != 0"
          >
            <!-- [disabled]="form.invalid">-->
            Grabar <mat-icon>save</mat-icon>
          </button>

          <button
            mat-flat-button
            color="accent"
            [disabled]="idPlanilla <= 0"
            (click)="onGenerate()"
          >
            Generar Txt <mat-icon>text_snippet</mat-icon>
          </button>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="mat-elevation-z8"
        >
          <!-- idplanilla Column -->
          <ng-container matColumnDef="idplanilla">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              ID Planilla
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.idplanilla }}
            </td>
          </ng-container>

          <!-- idpolplanilla Column -->
          <ng-container matColumnDef="idpolplanilla">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              ID Aviso Detalle
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.idpolplanilla }}
            </td>
          </ng-container>

          <!-- numpolplani Column -->
          <ng-container matColumnDef="numpolplani">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Póliza</th>
            <td mat-cell *matCellDef="let element">
              {{ element.numpolplani }}
            </td>
          </ng-container>

          <!-- numcert Column -->
          <ng-container matColumnDef="numcert">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              No. Cetificado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.numcert }}
            </td>
          </ng-container>

          <!-- nonmbreasegurado Column -->
          <ng-container matColumnDef="nonmbreasegurado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Cliente</th>
            <td mat-cell *matCellDef="let element">
              {{ element.nonmbreasegurado }}
            </td>
          </ng-container>

          <!-- no_requerimiento Column -->
          <ng-container matColumnDef="no_requerimiento">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              No. Requerimiento
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.idrequerimiento }}
            </td>
          </ng-container>

          <!-- no_pago Column -->
          <ng-container matColumnDef="no_pago">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>No. Pago</th>
            <td mat-cell *matCellDef="let element">
              {{ element.numcatalogo }}
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-flat-button
                color="primary"
                (click)="delete(element)"
                [disabled]="form.value.stsplani === 'COB'"
                matTooltip="Eliminar "
                class="mr-1"
                matTooltipClass="tooltip-red"
              >
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
