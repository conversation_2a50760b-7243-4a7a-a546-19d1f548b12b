<div class="row" [formGroup]="formulario">
  <mat-form-field class="col" appearance="fill">
    <mat-select formControlName="banco_id" placeholder="Banco">
      <mat-option *ngFor="let banco of dataBanco" [value]="banco.idbanco">
        {{ banco.nombre }}
      </mat-option>
    </mat-select>
  </mat-form-field>
  <mat-form-field class="col" appearance="fill">
    <mat-label>Tipo de cuenta</mat-label>
    <input matInput formControlName="tipo_cuenta" sanitizeUppercase />
  </mat-form-field>
  <mat-form-field class="col" appearance="fill">
    <mat-label>Numero de cuenta</mat-label>
    <input matInput formControlName="numero_cuenta" type="number" />
  </mat-form-field>
</div>
