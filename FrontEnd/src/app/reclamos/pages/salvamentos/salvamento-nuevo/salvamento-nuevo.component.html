<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body" style="background: #fff">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />

        <form [formGroup]="form">
          <div
            class="alert alert-success col-md-5 mt-3"
            *ngIf="status == 'success'"
          >
            El registro se ha guardado correctamente.
          </div>

          <div class="alert alert-danger col-md-5" *ngIf="status == 'error'">
            El registro no se ha completado correctamente, vuelve a intentarlo.
          </div>

          <div class="row-grid">
            <div class="col-grid">
              <h2>DATOS GENERALES</h2>
            </div>
          </div>

          <div class="row-grid">
            <mat-form-field class="col-grid">
              <mat-label>Caso</mat-label>
              <input
                matInput
                placeholder="Caso"
                name="caso"
                formControlName="caso"
                readonly
              />
            </mat-form-field>

            <mat-form-field class="col-grid">
              <mat-select name="tipo" placeholder="Tipo" formControlName="tipo">
                <mat-option *ngFor="let item of tipos" [value]="item.valor">
                  {{ item.descripcion }}
                </mat-option>
              </mat-select>
            </mat-form-field>

            <mat-form-field class="col-grid">
              <mat-label>Reclamo</mat-label>
              <input
                matInput
                placeholder="PRESIONE EL ICONO"
                name="idreclamo"
                formControlName="idreclamo"
                readonly
              />
              <button
                mat-icon-button
                color="accent"
                matSuffix
                (click)="openDialogoReclamos()"
              >
                <mat-icon>search</mat-icon>
              </button>
            </mat-form-field>

            <mat-form-field class="col-grid">
              <mat-label>Fecha Salvamento</mat-label>
              <input matInput [matDatepicker]="fecing" placeholder="Fecha Salvamento" name="fecha_ingreso"
                formControlName="fecha_ingreso" />
              <mat-datepicker-toggle matSuffix [for]="fecing"></mat-datepicker-toggle>
              <mat-datepicker #fecing></mat-datepicker>
            </mat-form-field>

              <mat-form-field class="col-grid">
                <mat-select
                  name="Propiedad"
                  placeholder="Propiedad"
                  formControlName="Propiedad"
                >
                  <mat-option
                    *ngFor="let item of propiedadBienes" 
                    [value]="item.valor"
                  >
                    {{ item.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field class="col-grid">
                <mat-select
                  name="Ubicacion"
                  placeholder="Ubicacion"
                  formControlName="Ubicacion"
                >
                  <mat-option
                    *ngFor="let item of ubicaciones"
                    [value]="item.valor"
                  >
                    {{ item.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <mat-form-field class="col-grid">
                <mat-select
                  name="finalidad"
                  placeholder="Finalidad"
                  formControlName="finalidad"
                >
                  <mat-option
                    *ngFor="let item of finalidades"
                    [value]="item.valor"
                  >
                    {{ item.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              
            <mat-form-field class="col-grid">
              <mat-select name="Proceso" placeholder="Proceso" formControlName="Proceso">
                <mat-option *ngFor="let item of procesos" [value]="item.valor">
                  {{ item.descripcion }}
                </mat-option>
              </mat-select>
            </mat-form-field>

              <mat-form-field class="col-grid">
                <mat-select name="Estado Interno" placeholder="Estado Interno" formControlName="estado_interno">
                  <mat-option *ngFor="let item of estado_interno" [value]="item.valor">
                    {{ item.descripcion }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

             <mat-form-field class="col-grid">
                <mat-label>Fecha Escritura</mat-label>
                <input matInput [matDatepicker]="fecesc" placeholder="Fecha Escritura" name="fechaescritura"
                  formControlName="fechaescritura" />
                <mat-datepicker-toggle matSuffix [for]="fecesc"></mat-datepicker-toggle>
                <mat-datepicker #fecesc></mat-datepicker>
              </mat-form-field>

              <mat-form-field class="col-grid">
                <mat-label>Escritura</mat-label>
                <textarea matInput placeholder="Escritura" name="escritura" formControlName="escritura">
                              </textarea>
              </mat-form-field>


               <mat-form-field class="col-grid">
                <mat-label>Observaciones</mat-label>
                <textarea
                  matInput
                  placeholder="Observaciones"
                  name="observaciones"
                  formControlName="observaciones"
                >
                </textarea>
              </mat-form-field>


  
              <mat-form-field class="col-grid">
                <mat-label>Fecha Traspaso</mat-label>
                <input
                  matInput
                  [matDatepicker]="feccon"
                  placeholder="Fecha Traspaso"
                  name="fecha_traspaso"
                  formControlName="fecha_traspaso"
                />
                <mat-datepicker-toggle
                  matSuffix
                  [for]="feccon"
                ></mat-datepicker-toggle>
                <mat-datepicker #feccon></mat-datepicker>
              </mat-form-field>
  
              <mat-form-field class="col-grid">
                <mat-label>Precio Base</mat-label>
                <input
                  type="number"
                  matInput 
                  placeholder="Precio Base"
                  name="precio_base"
                  formControlName="precio_base"
                 
                />
              </mat-form-field>
  
              <mat-form-field class="col-grid">
                <mat-label>Precio Venta</mat-label>
                <input
                  type="number"
                  matInput
                  placeholder="Precio Venta"
                  name="precio_venta"
                  formControlName="precio_venta"
                />
              </mat-form-field>
  
              <mat-form-field class="col-grid">
                <mat-label>Cliente</mat-label>
                <input
                  matInput
                  placeholder="PRESIONE EL ICONO"
                  name="cliente"
                  formControlName="cliente"
                  readonly
                />
                <button
                  mat-icon-button
                  color="accent"
                  matSuffix
                  (click)="openDialogoCliente()"
                >
                  <mat-icon>search</mat-icon>
                </button>
              </mat-form-field>
          </div>

          <div class="control-actions">
            <button mat-flat-button color="primary" (click)="close()">
              <mat-icon>highlight_off</mat-icon>Cancelar
            </button>
            <button
              mat-flat-button
              color="accent"
              (click)="onSaveForm(form.value)"
              [disabled]="!form.valid"
            >
              <mat-icon>save</mat-icon>{{ textButton }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
