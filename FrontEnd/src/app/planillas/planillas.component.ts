import { Component, OnInit } from "@angular/core";
import { ModalService } from "../_services/modal.service";
import { IActions } from "../shared/interfaces/actions.interface";
import { FormularioAsignarCodigoPlanillaClienteComponent } from "./components/formularioAsignarCodigoPlanillaCliente/formularioAsignarCodigoPlanillaCliente.component";
import { FormularioPlanillaComponent } from "./components/formularioPlanilla/formularioPlanilla.component";
import { ListarClientesPlanillaComponent } from "./components/listar-clientes-planilla/listar-clientes-planilla.component";
import { ICodigoPlanilla } from "./interfaces/planillas.interface";
import { CodigoPlanillaClienteService } from "./services/codigo-planilla-cliente.service";
import { PlanillaService } from "./services/planilla.service";

@Component({
  selector: "app-planillas",
  templateUrl: "./planillas.component.html",
  styleUrls: ["./planillas.component.css"],
})
export class PlanillasComponent implements OnInit {
  pageTitle = "Codigo de Planillas";
  rechargeFunction: Function;

  actions: IActions[] = [
    {
      color: "accent",
      icon: "edit",
      tooltipText: "Editar",
      onClick: ({ row }) => {
        this.edit(row);
      },
    },
    {
      tooltipText: "Asignar Cliente",
      color: "primary",
      icon: "person_add",
      onClick: ({ row }) => {
        this.assignClient(row);
      },
    },
    {
      tooltipText: "Ver clientes asignados",
      color: "accent",
      icon: "people",
      onClick: ({ row }) => {
        this.getClientesByPlanilla(row.id);
      },
    },
  ];

  constructor(
    public planillaService: PlanillaService,
    public codigoPlanillaClienteService: CodigoPlanillaClienteService,
    private modalService: ModalService,
  ) {}

  ngOnInit() {}

  onNew() {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };

    this.modalService.openDialog<FormularioPlanillaComponent>({
      component: FormularioPlanillaComponent,
      title: "Nuevo Codigo de Planilla",
      width: "500px",
      element: { data: null },
      actionClose: onClose,
    });
  }

  edit(row: ICodigoPlanilla) {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };

    this.modalService.openDialog<FormularioPlanillaComponent>({
      component: FormularioPlanillaComponent,
      title: "Editar Codigo de Planilla",
      width: "500px",
      element: { data: row },
      actionClose: onClose,
    });
  }

  assignClient(row: ICodigoPlanilla) {
    this.modalService.openDialog<FormularioAsignarCodigoPlanillaClienteComponent>(
      {
        component: FormularioAsignarCodigoPlanillaClienteComponent,
        title: "Asignar Cliente",
        width: "500px",
        element: { data: row },
      },
    );
  }

  async getClientesByPlanilla(id: string) {
    const datos = await this.codigoPlanillaClienteService
      .getAsignacionesByCodigoPlanillaId(id)
      .toPromise();

    this.modalService.openDialog<ListarClientesPlanillaComponent>({
      component: ListarClientesPlanillaComponent,
      title: "Clientes Asignados",
      //width: "500px",
      element: { data: datos },
    });
  }
}
