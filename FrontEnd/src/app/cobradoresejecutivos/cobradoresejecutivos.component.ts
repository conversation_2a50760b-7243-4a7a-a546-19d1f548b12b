import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from "@angular/core";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { Subscription } from "rxjs";

import { ActivatedRoute, Router } from "@angular/router";
import { filter } from "rxjs/operators";
import Swal from "sweetalert2";
import { ListenService } from "../_services/listen.service";
import { ModalService } from "../_services/modal.service";
import { IEjecutivo } from "../interface";
import { FormularioEjecutivoComponent } from "./formulario-ejecutivo/formulario-ejecutivo.component";
import { FormularioCobradorEjecutivoComponent } from "./formulario/formulario.component";
import { ModalDesactivarComponent } from "./modal-desactivar/modal-desactivar.component";
import { CobradoresejecutivosService } from "./services/cobradoresejecutivos.service";

@Component({
  selector: "app-cobradoresejecutivos",
  templateUrl: "./cobradoresejecutivos.component.html",
  styleUrls: ["./cobradoresejecutivos.component.css"],
})
export class CobradoresejecutivosComponent implements OnInit, OnDestroy {
  public pageTitle: string = "";
  displayedColumns: string[] = [
    "id",
    "codigo_cobrador",
    "nombre",
    "apellido",
    "estado",
    "actions", // more columns,
  ];
  dataSource!: MatTableDataSource<IEjecutivo>;
  loading: boolean = false;
  message: string = "";
  subscription!: Subscription;

  constructor(
    private cobradoresejecutivosService: CobradoresejecutivosService,
    private modalService: ModalService,
    private listenService: ListenService,
    private route: ActivatedRoute,
    private router: Router,
  ) {
    this.pageTitle = "Listado de Ejecutivos";
  }
  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort!: MatSort;

  ngOnInit(): void {
    this.getCobradoresEjecutivos();
    this.subscription = this.listenService.ok$.subscribe((v) =>
      v ? this.getCobradoresEjecutivos() : "",
    );

    this.route.queryParams
      .pipe(
        filter((params) => {
          return params.add === "cobradornotfound";
        }),
      )
      .subscribe(() => {
        this.onNew();
      });
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  getCobradoresEjecutivos() {
    this.cobradoresejecutivosService.getAllCobradoresEjecutivos().subscribe({
      next: (value) => {
        this.dataSource = new MatTableDataSource(value);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.loading = false;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onNew() {
    this.modalService.openDialog<FormularioCobradorEjecutivoComponent>({
      component: FormularioCobradorEjecutivoComponent,
      title: "Nuevo ejecutivo",
      element: null,
    });
  }

  asignarEjecutivo(idcobrador: number) {
    this.modalService.openDialog<FormularioEjecutivoComponent>({
      title: "Asignar intermediario",
      component: FormularioEjecutivoComponent,
      element: { idcobrador },
    });
  }

  desactivarActivarCobradorEjecutivo(id: number, estado: string) {
    if (estado === "ACT") {
      this.desactivarCobradorEjecutivo(id);
    } else {
      this.activarCobradorEjecutivo(id);
    }
  }

  desactivarCobradorEjecutivo(id: number) {
    this.cobradoresejecutivosService.desactivarCobradorEjecutivo(id).subscribe((data) => {
      Swal.fire({
        title: " Ejecutivo desactivado",
        text: "El  Ejecutivo ha sido desactivado",
        icon: "success",
        confirmButtonText: "Aceptar",
      });
      if (data.length > 0) {
        this.listarIntermediarios(data);
      }
      this.getCobradoresEjecutivos();
    });
  }

  listarIntermediarios(data) {
    this.modalService.openDialog<ModalDesactivarComponent>({
      title: "Listado de Intermediarios",
      component: ModalDesactivarComponent,
      element: data,
      disableAutoClose: true,
    });
  }


  activarCobradorEjecutivo(id: number) {
    this.cobradoresejecutivosService.activarCobradorEjecutivo(id).subscribe(() => {
      this.getCobradoresEjecutivos();
    });
  }

  onEdit(element: IEjecutivo) {
    this.modalService.openDialog<FormularioCobradorEjecutivoComponent>({
      component: FormularioCobradorEjecutivoComponent,
      title: "Editar ejecutivo",
      element,
      width: "500px",
    });
  }

  datosGenerales(id: number) {
    this.router.navigate(["/cobradorejecutivo/detalle", id]);
  }
}
