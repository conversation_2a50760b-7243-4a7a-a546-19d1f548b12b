import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { PolizaEstadoService } from './poliza-estado.service';
import { VwPolizaEstado } from './vw-poliza-estado';

import { global } from "../_services/global";

@Component({
  selector: 'app-list-poliza-estado',
  templateUrl: './list-poliza-estado.component.html',
  styleUrls: ['./list-poliza-estado.component.css'],
  providers: [PolizaEstadoService]
})
export class ListPolizaEstadoComponent implements OnInit {

  public page_title: string;
  public status: string;  
  public url;    
  //public acreencia: EntidadAcreedora; 
  public dataSource = null; 

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _polizaEstadoService: PolizaEstadoService,        
    //
    private _dialogRef: MatDialogRef<ListPolizaEstadoComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) { 
    this.page_title = "Listado de Pólizas";    
    this.url = global.url;
  }

  displayedColumns: string[] = ['idpoliza',/*'codigo_producto','poliza_aseguradora', */'producto_poliza','idcliente', 'cliente_nombre', 'estado_poliza', 'actions'];  
  
  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, {static: true}) sort: MatSort;
  ngOnInit() {
    this.getPolizaActivas();        
  }  

  getPolizaActivas(){
    
    this._polizaEstadoService.getPolizas().subscribe(
      response => {        
        if (response.status == 'success') {          
          this.dataSource = new MatTableDataSource<VwPolizaEstado>(response.polizaProducto);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;          
        }else{
          //this.status = 'error';          
        }        
      },
      error => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }
  
  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput.split(',').map(str => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  } 

 onSelected(element){
  console.log('onSelected'); 
  //this._dialogRef.close(element.idcliente);
  this._dialogRef.close(<VwPolizaEstado>element);
 }

}
