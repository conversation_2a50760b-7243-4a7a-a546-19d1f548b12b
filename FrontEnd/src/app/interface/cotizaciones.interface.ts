export interface ICotizador {
  idcotizacion: number;
  idproducto: string;
  nombre: string;
  segundonombre: null;
  apellido: null;
  fecinivig: Date;
  fecfinvig: Date;
  telefono: string;
  correo: null;
  feccotizacion: Date;
  usuario: string;
  estado: string;
  nit: null;
  idpoliza: null;
  numcert: number;
  id_intermediario: string;
  idtipo_moneda: null;
  inspector_asignado: null;
  horainivig: null;
  horafinvig: null;
  calculo: null;
  tipo_cliente: null;
  idplanpagos: null;
  tipo: string;
  idplantilla?: string;
}

export type Cotizacion = {
  idcotizacion: number;
  numcert: number;
  idplantilla: string;
};

/*export interface CalculosResponse {
  detalleOperacion: DetalleOperacionCalculos[];
}*/

export interface CalculosResponse {
  [key: string]: DetallePlan[];
}

export interface DetallePlan {
  sumaAsegurada: number;
  tasa: string;
  prima: number;
  rubros: Rubros;
  primaTotal: number;
  pagos: number;
  infoPlanFracc: InfoPlanFracc;
}

export interface InfoPlanFracc {
  plan: string;
  numpago: number;
  idplanfracc: number;
}

export interface Rubros {
  primaNeta: Asistencia;
  gastosEmision: Asistencia;
  asistencia: Asistencia;
  recargoFracc?: Asistencia;
  iva: Asistencia;
}

export interface Asistencia {
  valor: number;
  idrubro: number;
}

export interface DetalleOperacionCalculos {
  descripcion: string;
  valor: number;
  idplanfracc: string;
  numpagos: string;
  nombre: string;
}

export interface PayloadRecargosDescuentos {
  porcentaje: string;
  indmarcar: boolean;
}
export interface IRecargosDescuentos {
  isSelected: boolean;
  idrecadcto: string;
  porcentaje: string;
  indmarcar: boolean;
  tipo: string;
  idcotizacion: string;
  numcert: string;
  idplantilla: string;
  descripcion: string;
  isEdit: boolean;
}

export interface IDetalleCotizacion {
  generales: Generales;
  caracteristicas: Caracteristica[];
}

export interface Caracteristica {
  valor: string;
  etiqueta: string;
}

export interface Generales {
  idcotizacion: number;
  numcert: string;
  nombre: string;
  segundonombre: null;
  apellido: string;
  fecinivig: Date;
  fecfinvig: Date;
  telefono: string;
  correo: string;
  feccotizacion: Date;
  nit: string;
  inspector_asignado: null;
  horainivig: null;
  horafinvig: null;
  idplanpagos: null;
  estado: string;
  tipo: string;
  nombreintermediario: string;
  simbolo: string;
}

export interface BienesCotizacion {
  bienes: { [key: string]: Bien[] };
}

export interface Bien {
  idbien: string;
  descripcion: string;
  codigo: string;
  clase: string;
  suma?: number;
  tasa?: number;
  prima?: number;
}

export type BienPaylaod = Omit<Bien, "descripcion" | "codigo" | "clase">;

export interface ResponseCargaMasivaCotizacion {
  [key: string] : CalculosResponse[];
}