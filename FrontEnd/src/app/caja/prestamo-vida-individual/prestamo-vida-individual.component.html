<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{page_title}}</h1>
        </div>
        <hr />

        <form [formGroup]="form">
          <mat-grid-list cols="1" rowHeight="70px" gutterSize="1px">

            <mat-grid-tile>
              <mat-form-field>
                <mat-select name="producto" placeholder="Producto" formControlName="producto">
                  <mat-option *ngFor="let producto of listProductos" [value]="producto.idproducto">
                    {{producto.codigo}} - {{producto.descripcion}}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </mat-grid-tile>

            <mat-grid-tile>
              <mat-form-field>
                <mat-label>Fecha Vencimiento</mat-label>
                <input matInput [matDatepicker]="fecha" placeholder="Fecha" name="fecha" formControlName="fecha">
                <mat-datepicker-toggle matSuffix [for]="fecha"></mat-datepicker-toggle>
                <mat-datepicker #fecha></mat-datepicker>
              </mat-form-field>
            </mat-grid-tile>
          </mat-grid-list>

          <mat-grid-list cols="2" rowHeight="50px" gutterSize="1px">
            <mat-grid-tile>
              <mat-dialog-actions>
                <button mat-flat-button color="primary" (click)="getPrestamos(form.value)" [disabled]="form.invalid" >
                  <mat-icon>highlight_off</mat-icon>Ejecutar Consulta
                </button>
              </mat-dialog-actions>
            </mat-grid-tile>
            <mat-grid-tile>
              <mat-dialog-actions>
                <button mat-flat-button color="accent" (click)="openDialogoOperUsuarioAcceso(form.value)">
                  <mat-icon>save</mat-icon>Ejecutar Proceso
                </button>
              </mat-dialog-actions>
            </mat-grid-tile>
          </mat-grid-list>
        </form>

        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">

          <!-- IdCliente Column -->
          <ng-container matColumnDef="producto">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Producto </th>
            <td mat-cell *matCellDef="let element"> {{element.producto}} </td>
          </ng-container>

          <!-- Nombre Column -->
          <ng-container matColumnDef="poliza_aseguradora">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Póliza </th>
            <td mat-cell *matCellDef="let element"> {{element.poliza_aseguradora}} </td>
          </ng-container>

          <!-- NumDoc Column -->
          <ng-container matColumnDef="nombre_contratante">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Contratante </th>
            <td mat-cell *matCellDef="let element"> {{element.nombre_contratante}} </td>
          </ng-container>

          <!-- Nit Column -->
          <ng-container matColumnDef="operacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Operación </th>
            <td mat-cell *matCellDef="let element"> {{element.operacion}} </td>
          </ng-container>

          <!-- Fecha Nacimiento Column -->
          <ng-container matColumnDef="tipo_operacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Tipo </th>
            <td mat-cell *matCellDef="let element"> {{element.tipo_operacion}} </td>
          </ng-container>

          <!-- Telefono Column -->
          <ng-container matColumnDef="talonario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Talonario </th>
            <td mat-cell *matCellDef="let element"> {{element.talonario}} </td>
          </ng-container>

          <!-- Estado Column -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
            <td mat-cell *matCellDef="let element"> {{ element.estado }} </td>
          </ng-container>

          <!-- Correo Column -->
          <ng-container matColumnDef="no_pago">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> No. Pago </th>
            <td mat-cell *matCellDef="let element"> {{element.no_pago }} </td>
          </ng-container>

          <!-- Direccion Column -->
          <ng-container matColumnDef="factura">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Requerimiento </th>
            <td mat-cell *matCellDef="let element"> {{element.factura}} </td>
          </ng-container>


          <!-- Direccion Column -->
          <ng-container matColumnDef="totalfactura">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto </th>
            <td mat-cell *matCellDef="let element"> {{element.totalfactura}} </td>
          </ng-container>

          <!-- Action Column 
    <ng-container matColumnDef="actions">
      <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
      <td mat-cell *matCellDef="let element">
        <button mat-flat-button color="primary" (click)="onSelected(element)" matTooltip="Seleccionar"
          matTooltipClass="tooltip-red">
          Seleccionar<mat-icon>touch_app</mat-icon>
        </button>
    </ng-container>
    -->

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        </table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>
      </div>
    </div>
  </div>
</div>