import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { ModalService } from "src/app/_services/modal.service";
import { IActions } from "src/app/shared/interfaces/actions.interface";
import Swal from "sweetalert2";
import { FormularioPlanReaseguroComponent } from "../../components/plan-reaseguro/formulario-plan-reaseguro/formulario-plan-reaseguro.component";
import { IPlanReaseguro } from "../../interfaces/plan-reaseguro.interface";
import { PlanReaseguroService } from "../../services/plan-reaseguro.service";

@Component({
  selector: "app-plan-reaseguro",
  templateUrl: "./plan-reaseguro.component.html",
  styleUrls: ["./plan-reaseguro.component.css"],
})
export class PlanReaseguroComponent implements OnInit {
  rechargeFunction: Function;

  actions: IActions[] = [
    {
      color: "accent",
      icon: "edit",
      tooltipText: "Editar",
      onClick: ({ row }) => {
        this.edit(row);
      },
    },
  ];

  query: string = "";

  constructor(
    public planReaseguroService: PlanReaseguroService,
    private modalService: ModalService,
    private router: Router,
  ) {
    this.query = this.router.url.split("?")[1];
  }

  ngOnInit() {}

  onNew() {
    if (!this.rechargeFunction) {
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioPlanReaseguroComponent>({
      component: FormularioPlanReaseguroComponent,
      title: "Nuevo Tipo de Contrato",
      width: "720px",
      element: { data: null },
      actionClose: onClose,
    });
  }

  edit(element: IPlanReaseguro) {
    if (!this.rechargeFunction) {
      console.error("La función de recarga aún no se ha asignado");
      return;
    }
    const onClose = {
      next: (value: boolean) => {
        console.log("cerrado");
        this.rechargeFunction();
      },
    };
    this.modalService.openDialog<FormularioPlanReaseguroComponent>({
      component: FormularioPlanReaseguroComponent,
      title: "Editar Tipo de Contrato",
      width: "720px",
      element: { data: element },
      actionClose: onClose,
    });
  }

  delete(element: IPlanReaseguro) {
    if (!this.rechargeFunction) {
      console.error("La función de recarga aún no se ha asignado");
      return;
    }
    Swal.fire({
      title: "Eliminar",
      text: "Esta seguro de realizar el cambio?",
      icon: "warning",
      showDenyButton: true,
      confirmButtonText: "Si",
      denyButtonText: "No",
    }).then((result) => {
      if (result.isConfirmed) {
        this.planReaseguroService
          .deletePlanReaseguro(element.id)
          .subscribe((res) => {
            this.rechargeFunction();
          });
      }
    });
  }

  dblClick({ row }) {
    this.router.navigate(["reaseguros/contrato"], {
      queryParams: { planReaseguro: row.id },
    });
  }
}
