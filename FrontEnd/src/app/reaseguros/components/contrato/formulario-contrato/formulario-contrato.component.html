<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8 py-2">
        <button-back class="ml-2"></button-back>
        <div class="page_title text-center">
          <h1>{{ id === "new" ? "Nuevo" : "Editar" }} contrato</h1>
        </div>
        <form [formGroup]="formularioContrato" class="m-4">
          <div class="row">
            <mat-form-field class="col" appearance="outline">
              <mat-label>Tipo de Contrato</mat-label>
              <mat-select
                formControlName="tipo_contrato_id"
                name="tipo_contrato_id"
              >
                <mat-option
                  *ngFor="let tipoContrato of tipoContrato$ | async"
                  [value]="tipoContrato.id"
                  >{{ tipoContrato.nombre }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Plan de reaseguro</mat-label>
              <mat-select
                formControlName="plan_reaseguro_id"
                name="plan_reaseguro_id"
              >
                <mat-option
                  *ngFor="let planReaseguro of planReaseguro$ | async"
                  [value]="planReaseguro.id"
                  >{{ planReaseguro.nombre }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Ramo de reaseguro</mat-label>
              <mat-select
                formControlName="ramo_reaseguro_id"
                name="ramo_reaseguro_id"
              >
                <mat-option
                  *ngFor="let ramoReaseguro of ramoReaseguro$ | async"
                  [value]="ramoReaseguro.id"
                  >{{ ramoReaseguro.codigo }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Clasificacion de riesgo</mat-label>
              <mat-select
                formControlName="clasificacion_riesgo_id"
                name="clasificacion_riesgo_id"
              >
                <mat-option
                  *ngFor="
                    let clasificacionRiesgo of clasificacionRiesgo$ | async
                  "
                  [value]="clasificacionRiesgo.id"
                  >{{ clasificacionRiesgo.codigo }} -
                  {{ clasificacionRiesgo.nombre }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Reasegurador</mat-label>
              <mat-select
                formControlName="reasegurador_id"
                name="reasegurador_id"
              >
                <mat-option
                  *ngFor="let reasegurador of reasegurador$ | async"
                  [value]="reasegurador.id"
                  >{{ reasegurador.contacto }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Broker</mat-label>
              <mat-select formControlName="broker_id" name="broker_id">
                <mat-option
                  *ngFor="let broker of broker$ | async"
                  [value]="broker.id"
                  >{{ broker.contacto }}</mat-option
                >
              </mat-select>
            </mat-form-field>
            <mat-form-field class="col" appearance="outline">
              <mat-label>Observaciones</mat-label>
              <input
                matInput
                formControlName="observaciones"
                name="observaciones"
                sanitizeUppercase
              />
            </mat-form-field>
          </div>
          <div class="d-flex justify-content-end m-2">
            <button
              mat-flat-button
              color="accent"
              (click)="saveForm()"
              [disabled]="!formularioContrato.valid"
            >
              <mat-icon>save</mat-icon>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
