import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ModalService } from "src/app/_services/modal.service";
import Swal from "sweetalert2";
import { FormularioCobradorEjecutivoComponent } from "../formulario/formulario.component";
import { CobradoresejecutivosService } from "../services/cobradoresejecutivos.service";

@Component({
  selector: "app-modal-desactivar",
  templateUrl: "./modal-desactivar.component.html",
  styleUrls: ["./modal-desactivar.component.css"],
})
export class ModalDesactivarComponent implements OnInit {
  intermediarios: any[] = [];
  cobradoresejecutivos: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: any },
    private modalService: ModalService,
    private cobradoresejecutivosService: CobradoresejecutivosService,
    private dialogRef: MatDialogRef<ModalDesactivarComponent>,
  ) {
    this.intermediarios = data.element;
  }

  ngOnInit() {
    this.getCobradoresEjecutivos();
  }

  getCobradoresEjecutivos() {
    this.cobradoresejecutivosService.getAllCobradoresEjecutivos(true).subscribe({
      next: (value) => {
        this.cobradoresejecutivos = value;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  close() {
    //recorremos intermediarios y validamos si todos los isOk son true para cerrar el modal
    let isOk = this.intermediarios.every((intermediario) => intermediario.isOk);
    if (isOk) this.dialogRef.close();
    else {
      Swal.fire({
        title: "Error",
        text: "Hay intermediarios sin cobrador asignado",
        icon: "error",
        confirmButtonText: "Ok",
      });
    }
  }

  nuevoCobradorEjecutivo() {
    const actionClose = {
      next: (value: boolean) => {
        this.getCobradoresEjecutivos();
      },
    };
    this.modalService.openDialog<FormularioCobradorEjecutivoComponent>({
      component: FormularioCobradorEjecutivoComponent,
      title: "Nuevo cobrador ejecutivo",
      element: null,
      actionClose,
    });
  }

  saveNewCobradorEjecutivo(intermediario: any) {
    if (intermediario.isOk) {
      Swal.fire({
        title: "Error",
        text: "El intermediario ya tiene un cobrador asignado, continue con el siguiente intermediario",
        icon: "error",
        confirmButtonText: "Ok",
      });
      return;
    }
    if (
      intermediario.idcobradorTmp &&
      intermediario.idintermediario &&
      intermediario.idcobrador
    ) {
      this.cobradoresejecutivosService
        .saveCobradorIntermediario(
          intermediario.idcobradorTmp,
          intermediario.idintermediario,
          intermediario.idcobrador,
        )
        .subscribe({
          next: (value) => {
            //this.close();
            intermediario.isOk = true;
          },
          error: (e) => {
            console.error(e);
          },
        });
    } else {
      Swal.fire({
        title: "Error",
        text: "Debe seleccionar un cobrador",
        icon: "error",
        confirmButtonText: "Ok",
      });
    }
  }
}
