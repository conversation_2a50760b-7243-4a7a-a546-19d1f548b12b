<h2 mat-dialog-title>Direccion</h2>
<div mat-dialgo-content>
  <form [formGroup]="formulario">
    <div class="row">
      <mat-form-field
        class="col"
        appearance="outline"
        *ngIf="!data.element.idcreacion"
      >
        <mat-label>Contacto</mat-label>
        <mat-select
          formControlName="contacto_id"
          name="contacto_id"
          class="col"
        >
          <mat-option
            *ngFor="let contacto of contacto$ | async"
            [value]="contacto.id"
          >
            {{ contacto.nombre }}
          </mat-option>
        </mat-select>
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Direccion</mat-label>
        <input
          matInput
          formControlName="direccion"
          type="text"
          sanitizeUppercase
        />
      </mat-form-field>
      <mat-checkbox formControlName="principal" name="principal" class="mt-2">
        Direccion Principal
      </mat-checkbox>
    </div>
    <div class="d-flex justify-content-end m-2">
      <button
        mat-flat-button
        color="accent"
        (click)="save()"
        [disabled]="!formulario.valid"
      >
        <mat-icon>save</mat-icon>
      </button>
    </div>
  </form>
</div>
