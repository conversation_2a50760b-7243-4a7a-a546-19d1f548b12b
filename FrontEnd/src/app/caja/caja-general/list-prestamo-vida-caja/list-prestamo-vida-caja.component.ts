import { Component, OnInit, ViewChild, Input, Inject } from "@angular/core";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { PrestamoService } from "./prestamo.service";
import { PrestamoVida } from "./prestamo-vida";

import { global } from "../../../_services/global";
//DIALOG
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
//SELECTION
import { SelectionModel } from "@angular/cdk/collections";

@Component({
  selector: 'app-list-prestamo-vida-caja',
  templateUrl: './list-prestamo-vida-caja.component.html',
  styleUrls: ['./list-prestamo-vida-caja.component.css'],
  providers: [PrestamoService]
})
export class ListPrestamoVidaCajaComponent implements OnInit {

  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public dataSource = null;
  public nNumRelingTemp: any;
  /*private idcliente: string;
  private nombre: string;*/

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  // Checkbox
  selection = new SelectionModel<PrestamoVida>(true, []);

  constructor(
    private _prestamoService: PrestamoService,
    private _dialogRef: MatDialogRef<ListPrestamoVidaCajaComponent>, //DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.page_title = this.data.title;
    this.nNumRelingTemp = this.data.numReling;
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "idprestamovida",
    "producto",
    "poliza",
    "numcert",
    "estado",
    "vigencia_inicio",
    "vigencia_fin",
    "stsprestamo",
    "tipoprestamo",
    "montoprestamo",
    "fecha_generacion",
    "factura",
    "select"
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getPrestamo(this.data.idPagador);
  }

  getPrestamo($id) {
    console.log("getPrestamo");
    this._prestamoService.findPrestamoByIdPagador($id, this.nNumRelingTemp).subscribe(
      (response) => {
        if (response.status == "success") {
          this.dataSource = new MatTableDataSource<PrestamoVida>(
            response.prestamo
          );
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  /** Si el número de elementos seleccionados coincide con el número total de filas. */
  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = !!this.dataSource && this.dataSource.data.length;
    return numSelected === numRows;
  }

  /** Selecciona todas las filas si no están todas seleccionadas; de lo contrario, deselecciona. */
  masterToggle() {
    this.isAllSelected()
      ? this.selection.clear()
      : this.dataSource.data.forEach((r) => this.selection.select(r));
  }

  /** Etiqueta de la casilla de verificación del registro anterior */
  checkboxLabel(row: PrestamoVida): string {
    if (!row) {
      return `${this.isAllSelected() ? "select" : "deselect"} all`;
    }
    return `${this.selection.isSelected(row) ? "deselect" : "select"} row ${
      row.id + 1
    }`;
  }

  /** Selecciona Datos */
  selectedData() {
    const numSelected = this.selection.selected;
    const numRelIng = this.nNumRelingTemp;

    if (numSelected.length > 0) {
      /*console.log('numSelected', numSelected);
      const [acreencia] = numSelected;
      console.log('idcliente', acreencia.idcliente);
      console.log('nombre', acreencia.nombre);
      this.idcliente = acreencia.idcliente;
      this.nombre =  acreencia.nombre;*/
      
      this._prestamoService.onSaveForm(numSelected, numRelIng).subscribe(
        (response) => {
          if (response.status == "success") {
            this.close();
          } else {
            //this.status = 'error';
          }
        },
        (error) => {
          console.log(<any>error);
          //this.status = 'error';
        }
      );
    } else {
      alert("Selecciona Alguna Factura");
    }
  }

  close(): void {
    this._dialogRef.close();
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }
}
