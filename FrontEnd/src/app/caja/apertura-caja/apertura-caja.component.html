<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">

      <div class="col-md-12 mt-3">
        <h1>{{page_title}}</h1>
        <p *ngIf="status == 'success'">
          Control de Aperturas y Cierres de la Caja General
        </p>

        <div class="alert alert-success col-md-5 mt-3" *ngIf="status == 'success'">
          Proceso Ejecutado Correctamente.
        </div>

        <div class="alert alert-danger col-md-5" *ngIf="status == 'error'">
          El registro no se ha guardado correctamente, vuelve a intentarlo.
        </div>

        <form class="col-md-12 ml-0 pl-0" #aperturaForm="ngForm" (ngSubmit)="getValidaEstadoCaja(aperturaForm)">

          <div class="row ">
            <div class="col-md-6" style="width: 50%" >
              <div class="form-group">
                <label for="id" class="control-label" >No. Caja</label>
                <input type="text" class="form-control" style="width:100%;"id="id" name="id" placeholder="No. de Caja" #id="ngModel"
                  [(ngModel)]="aperturacaja.id" required disabled>
                <small *ngIf="!id.valid && id.touched" class="invalid-feedback d-block">
                  Correlativo de Apertura Invalido
                </small>
              </div>
            </div>
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <label for="stsapeciecaja" class="control-label">Estado</label>
                <select class="form-control" style="width:100%;" name="stsapeciecaja" id="stsapeciecaja" #stsapeciecaja="ngModel"
                  disabled="true" [(ngModel)]="aperturacaja.stsapeciecaja" required>
                  <option value="ACT" selected>Activa</option>
                  <option value="CER">Cerrada</option>
                  <option value="VAL">Valida</option>
                </select>
                <small *ngIf="!stsapeciecaja.valid && stsapeciecaja.touched" class="invalid-feedback d-block">
                  Estado de la Caja Invalido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <label for="idcaja" class="control-label">Caja</label>
                <select class="form-control" style="width:100%;" name="idcaja" id="idcaja" #idcaja="ngModel"
                  [(ngModel)]="aperturacaja.idcaja"  required >
                  <option *ngFor="let caja of cajas" value="{{caja.id}}" selected>
                    {{caja.desccaja}}
                  </option>
                </select>
                <small *ngIf="!idcaja.valid && idcaja.touched" class="invalid-feedback d-block">
                  El valor de la caja no es valido
                </small>
              </div>
            </div>
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <label for="idcajero" class="control-label">Cajero</label>
                <select class="form-control" id="idcajero" style="width:100%;" name="idcajero" #idcajero="ngModel"
                  [(ngModel)]="aperturacaja.idcajero" required>
                  <option *ngFor="let cajero of cajeros" value="{{cajero.id}}">
                    {{cajero.codcajero}}
                  </option>
                </select>
                <small *ngIf="!idcajero.valid && idcajero.touched" class="invalid-feedback d-block">
                  El valor del cajero no es valido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <!-- Full Name -->
                <label for="fecapertura" class="control-label">Fecha Apertura</label>
                <input type="datetime" class="form-control" style="width:100%;" id="fecapertura" name="fecapertura"
                  placeholder="Fecha Apertura" #fecapertura="ngModel" [(ngModel)]="aperturacaja.fecapertura" disabled>
                <small *ngIf="!fecapertura.valid && fecapertura.touched" class="invalid-feedback d-block">
                  Fecha Invalida
                </small>
              </div>
            </div>
            <div class="col-md-6 col-sm-6 col-xs-6">
              <div class="form-group">
                <!-- Street 1 -->
                <label for="mtosalapertura" class="control-label">Monto Apertura</label>
                <input type="number" style="width:100%;" value="0.00" step="0.00" min="0.00" max="100000.00" class="form-control"
                  id="mtosalapertura" name="mtosalapertura" placeholder="Monto Apertura" #mtosalapertura="ngModel"
                  [(ngModel)]="aperturacaja.mtosalapertura" required>
                <small *ngIf="!mtosalapertura.valid && mtosalapertura.touched" class="invalid-feedback d-block">
                  Monto de apertura no valido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div  class="col-md-6" style="width: 50%">
              <div class="form-group">

                <label for="feccierre" class="control-label">Fecha Cierre</label>
                <input type="datetime" class="form-control" style="width:100%;" id="feccierre" name="feccierre" placeholder="Fecha Cierre"
                  #feccierre="ngModel" [(ngModel)]="aperturacaja.feccierre" disabled>
                <small *ngIf="!feccierre.valid && feccierre.touched" class="invalid-feedback d-block">
                  La fecha no es valida
                </small>
              </div>
            </div>
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <label for="mtosalcierre" class="control-label">Monto Cierre</label>
                <input type="number" style="width:100%;" step="0.00" min="0.00" max="100000.00" class="form-control" id="mtosalcierre"
                  name="mtosalcierre" placeholder="Monto Saldo Cierre" #mtosalcierre="ngModel"
                  [(ngModel)]="aperturacaja.mtosalcierre">
                <small *ngIf="!mtosalcierre.valid && mtosalcierre.touched" class="invalid-feedback d-block">
                  El monto de cierre ingresado no es valido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <!-- City-->
                <label for="mtomov" class="control-label">Monto Movimiento</label>
                <input type="number" style="width:100%;" step="0.00" min="0.00" max="100000.00" class="form-control" id="mtomov"
                  name="mtomov" placeholder="Monto Movimiento" #mtomov="ngModel" [(ngModel)]="aperturacaja.mtomov">
                <small *ngIf="!mtomov.valid && mtomov.touched" class="invalid-feedback d-block">
                  El monto del movimiento ingresado no es valido
                </small>
              </div>
            </div>
            <div class="col-md-6" style="width: 50%">
              <div class="form-group">
                <!-- State Button -->
                <label for="mtomovdol" class="control-label">Monto Movimiento U$</label>
                <input type="number" style="width:100%;" step="0.00" min="0.00" max="100000.00" class="form-control" id="mtomovdol"
                  name="mtomovdol" placeholder="Monto Movimiento U$" #mtomovdol="ngModel"
                  [(ngModel)]="aperturacaja.mtomovdol">
                <small *ngIf="!mtomovdol.valid && mtomovdol.touched" class="invalid-feedback d-block">
                  El monto del movimiento ingresado no es valido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div  class="col-md-6" style="width: 50%">
              <div class="form-group">
                <!-- City-->
                <label for="mtosalaperturadol" class="control-label">Monto Apertura U$</label>
                <input type="number" style="width:100%;" step="0.00" min="0.00" max="100000.00" class="form-control" id="mtosalaperturadol"
                  name="mtosalaperturadol" placeholder="Monto Apertura U$" #mtosalaperturadol="ngModel"
                  [(ngModel)]="aperturacaja.mtosalaperturadol" required value="0.00">
                <small *ngIf="!mtosalaperturadol.valid && mtosalaperturadol.touched" class="invalid-feedback d-block">
                  El monto de aperutra ingresado no es valido
                </small>
              </div>
            </div>
            <div  class="col-md-6" style="width: 50%">
              <div class="form-group">
                <!-- City-->
                <label for="mtosalcierredol" class="control-label">Monto Cierre U$</label>
                <input type="number"  style="width:100%;" step="0.00" min="0.00" max="100000.00" class="form-control" id="mtosalcierredol"
                  name="mtosalcierredol" placeholder="Monto Cierre U$" #mtosalcierredol="ngModel"
                  [(ngModel)]="aperturacaja.mtosalcierredol">
                <small *ngIf="!mtosalcierredol.valid && mtosalcierredol.touched" class="invalid-feedback d-block">
                  El monto de aperutra ingresado no es valido
                </small>
              </div>
            </div>
          </div>

          <div class="row ">
            <div class="col-md-6 col-sm-6 col-xs-6">
              <div class="form-group">
                <label for="mtosalcierredol" class="control-label">Observaciones</label>
                <textarea class="form-control"id="observacion" name="observacion" placeholder="Observaciones"
                  #observacion="ngModel" [(ngModel)]="aperturacaja.observacion">
                        </textarea>
              </div>
            </div>
            <div class="class-button">
              <div class="col-md-6 col-sm-6 col-xs-6">
                <br/><br/>
                <button mat-raised-button color="primary" matTooltip="Aperturar / Cierre de Caja"
                  matTooltipClass="tooltip-red" aria-label="Aperturar / Cierre de Caja" [disabled]="aperturaForm.invalid">
                  Aperturar/Cierre Caja
                  <mat-icon>attach_money</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>