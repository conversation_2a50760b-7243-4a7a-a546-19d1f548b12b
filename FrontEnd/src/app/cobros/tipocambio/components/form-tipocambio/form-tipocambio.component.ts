import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { CotizacionesService } from "src/app/cotizaciones/services/cotizaciones.service";
import { TipocambioService } from "../../services/tipocambio.service";
import { MatSnackBar } from "@angular/material/snack-bar";
import Swal from "sweetalert2";

@Component({
  selector: "app-form-tipocambio",
  templateUrl: "./form-tipocambio.component.html",
  styles: [],
})
export class FormTipocambioComponent implements OnInit {
  monedas$ = this.cotizacionesService.getMonedas();
  tipoCambioForm: FormGroup = new FormGroup({
    idmoneda: new FormControl(),
    tasa: new FormControl(),
    tasa_caja: new FormControl(),
  });
  idcambio: number = 0;

  constructor(
    private dialogRef: MatDialogRef<FormTipocambioComponent>,
    private cotizacionesService: CotizacionesService,
    private tipoCambioService: TipocambioService,
    private snakbar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}

  ngOnInit(): void {
    const data = this.data?.element;
    if (data?.idcambio) {
      this.idcambio = data.idcambio;
      this.tipoCambioForm.setValue({
        idmoneda: data.idmoneda,
        tasa: data.tasa,
        tasa_caja: data.tasa_caja,
      });
      this.tipoCambioForm.get("idmoneda")?.disable();
    }
  }

  close() {
    this.dialogRef.close(false);
  }

  save() {
    const dataForm: any = this.tipoCambioForm.getRawValue();
    const payload = {
      idmoneda: Number(dataForm.idmoneda),
      tasa: Number(dataForm.tasa),
      tasa_caja: Number(dataForm.tasa_caja),
    };

    const methodService = this.idcambio
      ? this.tipoCambioService.actualizarTipoCambio(this.idcambio, payload)
      : this.tipoCambioService.createTipoCambio(payload);

    methodService.subscribe({
      next: () => {
        this.dialogRef.close(true);
      },
      error: (e) => {
        const msjError =
          e?.error?.message ||
          "Ocurrió un error interno al realizar la operación";
        Swal.fire({
          customClass: {
            confirmButton: "btn btn-danger",
          },
          buttonsStyling: true,
          title: "Error",
          icon: "error",
          text: msjError,
        });
      },
    });
  }
}
