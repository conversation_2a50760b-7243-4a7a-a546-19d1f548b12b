import { NgModule } from "@angular/core";
import { RouterModule, Routes } from "@angular/router";
import { AuthGuard } from "../_guards/auth.guard";
import { CobradoresComponent } from "./cobradores.component";
import { DatosCobradorComponent } from "./datos-cobrador/datos-cobrador.component";

const routes: Routes = [
  {
    path: "",
    component: CobradoresComponent,
    canActivate: [AuthGuard],
  },
  {
    path: "detalle/:id",
    component: DatosCobradorComponent,
    canActivate: [AuthGuard],
  },
];

@NgModule({
  exports: [RouterModule],
  imports: [RouterModule.forChild(routes)],
})
export class CobradorRoutingModule {}
