import { Component, Inject, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { takeUntil } from "rxjs/operators";
import { EmitFilter } from "src/app/components/selects/selects.component";
import { default as Swal, default as swal } from "sweetalert2";
import { ListenService } from "../../_services/listen.service";
import { IPersona, ITipoCobrador, ITipoISR } from "../../interface";
import { PersonasService } from "../../personas/services";
import { CobradoresService } from "../services/cobradores.service";

@Component({
  selector: "app-formulario",
  templateUrl: "./formulario.component.html",
  styleUrls: ["./formulario.component.css"],
})
export class FormularioCobradorComponent implements OnInit {
  public loading: boolean = false;
  public pageTitle: string = "";
  public indexTab: number = 0;
  public personas: IPersona[];
  public tipoCobradores: ITipoCobrador[];
  public tipoISR: ITipoISR[];
  public cobradoresForm!: FormGroup;
  currentPage = 1;
  total = 1;
  query;

  constructor(
    private _dialogRef: MatDialogRef<FormularioCobradorComponent>, // DIALOGO
    @Inject(MAT_DIALOG_DATA) public data: { element: { idcobrador: number } },
    private listenService: ListenService,
    private personasService: PersonasService,
    private cobradoresService: CobradoresService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.buildForm();
    this.getPersonas();
    this.getTipoCobrador();
    this.getTipoISR();
    if (this.data.element?.idcobrador) this.asignarDatos(this.data.element);
    this.listenCodigo();
  }

  waitForFilterResponse(item: EmitFilter<IPersona>) {
    if (item == null) {
      //this.snack.dismiss();
      this.total = -1;
      this.query = null;
      this.getPersonas();
    }
    if (item?.value) {
      this.query = {
        param: ["nombre", "nit"],
        value: item.value,
      };
      this.currentPage = 1;
      this.getPersonas();
    }

    /*item?.data.subscribe((v) => {
      if (v.length === 0) {
        this.snack
          .open("El registro no existe", "Crear")
          .onAction()
          .subscribe(() => {
            this.router.navigate(["/personas/nuevo"], {
              queryParams: { nombre: item.value },
            });
            setTimeout(() => {
              this.snack.dismiss();
            }, 1000);
          });
      } else {
        this.snack.dismiss();
      }
    });*/
  }

  infinityScroll() {
    this.currentPage++;
    this.getPersonas();
  }

  getPersonas() {
    if (this.total === this.personas?.length) return;
    this.personasService
      .getPersonas(
        this.query,
        { currentPage: this.currentPage, pageSize: 10 },
        {
          param: "cobrador",
          value: "true",
        },
      )
      .subscribe({
        next: (value) => {
          //this.personas = value.data;
          if (!this.query && this.total > 0) {
            this.personas = [...this.personas, ...value.data];
          } else {
            this.personas = value.data;
          }
          this.total = value.total;
        },
        error: (e) => {
          console.error(e);
        },
      });
  }

  getTipoCobrador() {
    this.cobradoresService.getAllTipoCobrador().subscribe({
      next: (value) => {
        this.tipoCobradores = value;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  getTipoISR() {
    this.cobradoresService.getAllTipoIsr().subscribe({
      next: (value) => {
        this.tipoISR = value;
      },
      error: (e) => {
        console.error(e);
      },
    });
  }

  buildForm() {
    this.cobradoresForm = this.fb.group({
      idpersona: new FormControl("", Validators.required),
      codigo_cobrador: new FormControl("", Validators.required),
      idtipo_cobrador: new FormControl("", Validators.required),
      idtipo_isr: new FormControl("", Validators.required),
    });
  }

  close(): void {
    this._dialogRef.close();
  }

  onNewTab() {
    this.indexTab++;
  }

  onSaveForm() {
    if (this.data.element?.idcobrador) {
      this.cobradoresService
        .updateCobrador(this.data.element.idcobrador, this.cobradoresForm.value)
        .subscribe({
          next: () => {
            swal.fire("Registro actualizado", "", "success").then(() => {
              this.close();
              this.listenService.reloadComponent(true);
            });
          },
          error: (e) => {
            swal.fire(
              "Error actualizando el registro",
              "Comunicarse con un administrador",
              "error",
            );
            this.close();
          },
        });
    } else {
      this.cobradoresService
        .createCobrador(this.cobradoresForm.value)
        .subscribe({
          next: () => {
            swal.fire("Registro guardado", "", "success").then(() => {
              this.close();
              this.listenService.reloadComponent(true);
            });
          },
          error: (e) => {
            swal.fire(
              "Error guardando el registro",
              "Comunicarse con un administrador",
              "error",
            );
            this.close();
          },
        });
    }
  }

  listenCodigo() {
    this.cobradoresForm.get("codigo_cobrador").setErrors({ exist: true });
    this.cobradoresForm
      .get("codigo_cobrador")
      .valueChanges.subscribe((value) => {
        if (value) {
          this.cobradoresService
            .validateCodigo(value, this.data.element?.idcobrador)
            .pipe(
              takeUntil(
                this.cobradoresForm.get("codigo_cobrador").valueChanges,
              ),
            )
            .subscribe({
              next: (data) => {
                this.cobradoresForm.get("codigo_cobrador").setErrors(null);
              },
              error: (e) => {
                this.cobradoresForm
                  .get("codigo_cobrador")
                  .setErrors({ exist: true });
                Swal.fire({
                  title: "Error",
                  text: "El código ya existe",
                  icon: "error",
                  confirmButtonText: "Aceptar",
                });
              },
            });
        }
      });
  }

  asignarDatos(data: any) {
    data.idpersona = data.persona_id;
    this.cobradoresForm.patchValue(data);
    this.indexTab = data.idcobrador ? 1 : 0;
  }
}
