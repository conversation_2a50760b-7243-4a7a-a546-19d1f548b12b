import { Component, EventEmitter, Input, OnInit, Output } from "@angular/core";

@Component({
  selector: "app-paso-dos-intermediario",
  templateUrl: "./paso-dos.component.html",
  //styleUrls: ["../detalle-intermediario.component.css"],
})
export class PasoDosIntermediarioComponent implements OnInit {
  @Input() datos;
  @Input() loading;
  @Output() nextStep = new EventEmitter<void>();
  @Output() prevStep = new EventEmitter<void>();
  @Output() reloadData = new EventEmitter<void>();

  constructor() {}

  ngOnInit() {}

  reload() {
    this.reloadData.emit();
  }

  next() {
    this.nextStep.emit();
  }

  previous() {
    this.prevStep.emit();
  }

  isBoolean(value) {
    return value == "1" || value == 1 || value == true;
  }
}
