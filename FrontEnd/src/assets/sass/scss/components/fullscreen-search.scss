/*=========================================================================================
	File Name: fullscreen-search.scss
	Description: Full screen search with type anywhere functionality.
	----------------------------------------------------------------------------------------
    Item Name: Item Name: Modern Admin -Angular 11+ Bootstrap 5 Admin Dashboard Template
	Version: 3.0
	Author: PIXINVENT
	Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/


%extend-fullscreen-search-close {
	content: '';
	position: absolute;
	width: 2px;
	height: 100%;
	top: 0;
	left: 50%;
	border-radius: 3px;
	opacity: 0.2;
	background: $black;
}
body{
	&.search-open{
		overflow-y:hidden;
	}
}
.fullscreen-search-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: $fullscreen-search-overlay-bg;
	opacity: 0;
	pointer-events: none;
	transition: opacity 0.5s;
	transition-timing-function: cubic-bezier(0.7,0,0.3,1);
}

.fullscreen-search {
	width: 100px;
	min-height: 40px;
	z-index: 10000;
	background: $fullscreen-search-bg;
	position: absolute;
	top: -50px;
	right: 0px;
	opacity:0;
	overflow-y: scroll;
	transform-origin: 100% 0;
	transition-property: min-height, width, top, right;
	transition-duration: 0.5s;
	transition-timing-function: cubic-bezier(0.7,0,0.3,1);
	&.open {
		width: 100%;
		min-height: 100%;
		opacity: 1;
		top: 0px;
		overflow: hidden;
		.fullscreen-search-form {
			width: 90%;
			height: 160px;
			transform: translate3d(0,3em,0);
		}
		.fullscreen-search-input {
			font-size: 7em;
		}
		.fullscreen-search-submit {
			pointer-events: auto;
			opacity: 1;
			transform: translate3d(-30px,-50%,0) scale3d(1,1,1);
			transition: opacity 0.3s, transform 0.3s;
			transition-delay: 0.5s;
		}
		.fullscreen-search-close {
			opacity: 1;
			pointer-events: auto;
			transform: scale3d(1,1,1);
			transition: opacity 0.3s, transform 0.3s;
			transition-delay: 0.5s;
		}
		.fullscreen-search-content {
			opacity: 1;
			height: auto;
			overflow: visible;
			pointer-events: auto;
			transition: opacity 0.3s 0.5s;
			.fullscreen-search-result{
				.media-object{
					width:3rem;
				}
			}
		}
		~ .fullscreen-search-overlay {
			opacity: 1;
		}
	}
	&.hideInput .fullscreen-search-input {
		color: transparent;
		transition: color 0.3s;
	}
}
.fullscreen-search-form {
	width: 100%;
	height: 40px;
	margin: 0 auto;
	position: relative;
	transition-property: width, height, transform;
	transition-duration: 0.5s;
	transition-timing-function: cubic-bezier(0.7,0,0.3,1);
}
.fullscreen-search-input {
	width: 100%;
	height: 100%;
	padding: 0 10% 0 10px;
	font-weight: 700;
	font-family:$headings-font-family;
	border:none;
	background: transparent;
	color: $fullscreen-search-color;
	transition: font-size 0.5s cubic-bezier(0.7,0,0.3,1);
	&:-moz-placeholder {
		color: $fullscreen-search-color;
	}
	&:-ms-input-placeholder {
		color: $fullscreen-search-color;
	}
	&:focus {
		outline: none;
	}
}
.fullscreen-search-input::-ms-clear {
	display: none;
}
.fullscreen-search-input::-webkit-input-placeholder {
	color: $fullscreen-search-color;
}
.fullscreen-search-input::-moz-placeholder {
	color: $fullscreen-search-color;
}
input[type="search"] {
	box-sizing: border-box;
}
.fullscreen-search-submit {
	position: absolute;
	width: 80px;
	height: 80px;
	text-indent: 100px;
	overflow: hidden;
	right: 0;
	top: 50%;
	background: transparent $search-magnifier no-repeat center center;
	background-size: 100%;
	border: none;
	pointer-events: none;
	transform-origin: 50% 50%;
	opacity: 0;
	transform: translate3d(-30px,-50%,0) scale3d(0,0,1);
	&:focus {
		outline: none;
	}
}
.fullscreen-search-close {
	width: 36px;
	height: 36px;
	position: absolute;
	right: 1em;
	top: 1em;
	overflow: hidden;
	text-indent: 100%;
	cursor: pointer;
	pointer-events: none;
	opacity: 0;
	transform: scale3d(0,0,1);
	&:hover {
		&.fullscreen-search-close::before {
			opacity: 1;
		}
		&.fullscreen-search-close::after {
			opacity: 1;
		}
	}
}
.fullscreen-search-close::before {
	@extend %extend-fullscreen-search-close;
	transform: rotate(45deg);
}
.fullscreen-search-close::after {
	@extend %extend-fullscreen-search-close;
	transform: rotate(-45deg);
}
.fullscreen-search-content {
	color: $gray;
	margin-top: 4.5em;
	width: 98%;
	height: 0;
	overflow: hidden;
	padding: 0 5.5%;
	// background: $fullscreen-search-bg;
	position: absolute;
	pointer-events: none;
	opacity: 0;
}

@include media-breakpoint-down(md) {
	.fullscreen-search-input {
		padding: 0 25% 0 10px;
	}
	.fullscreen-search.open {
		.fullscreen-search-input {
			font-size: 2em;
		}
		.fullscreen-search-submit {
			transform: translate3d(0,-50%,0) scale3d(0.5,0.5,1);
		}
	}
}
@include media-breakpoint-down(lg) {
	.fullscreen-search {
		width: 80%;
		/*top: 10%;
		right: 10%;*/
	}
	.fullscreen-search.open {
		.fullscreen-search-input {
			font-size: 3em;
		}
	}
}
.gn-search:focus::-webkit-input-placeholder {
	color: transparent;
}
