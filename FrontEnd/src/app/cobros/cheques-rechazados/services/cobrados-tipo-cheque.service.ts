import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { global } from './../../../_services/global';

@Injectable({
  providedIn: 'root'
})
export class CobradosTipoChequeService {

  public url: string;
  constructor(
      private _http: HttpClient
    ) {
        this.url = global.url;
    }

  getVwChequesReqCobradosTipoCheque(): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.get(this.url+'vw-req-cobrados-tipo-cheque', {headers: headers});
  }

  getChequeRechazado(id: any): Observable<any>{     
    let headers = new HttpHeaders().set('Content-Type', 'application/json');
    return this._http.get(this.url+`vw-req-cobrados-tipo-cheque/${id}`, {headers: headers});
  }

}
