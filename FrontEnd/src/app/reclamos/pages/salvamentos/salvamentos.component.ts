import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { Router } from "@angular/router";

import { UserService } from "src/app/_services/user.service";
import { SalvamentosService } from "../../services/salvamentos.service";

import { NgxSpinnerService } from "ngx-spinner";
import Swal from "sweetalert2";

@Component({
  selector: "app-salvamentos",
  templateUrl: "./salvamentos.component.html",
  styleUrls: ["./salvamentos.component.css"],
})
export class SalvamentosComponent implements OnInit {
  public identity: string;
  public page_title: string;
  public status: string;
  public dataSource: any;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  displayedColumns: string[] = [
    "idsalvamento",
    "estado",
    "fecha_ingreso",
    "tipo",
    "reclamo",
    "caso",
    "recuperador",
    "comprador",
    "datos_vehiculo",
    "actions",
  ];

  constructor(
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private salvamentosService: SalvamentosService,
    private _userService: UserService
  ) {
    this.page_title = "Mantenimiento de Salvamentos";
    this.identity = this._userService.getIdentity();
  }

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  ngOnInit(): void {
    this.getDataAll();
  }

  getDataAll() {
    this.ngxSpinnerService.show();
    this.salvamentosService.getSalvamentos().subscribe(
      (response) => {
        this.dataSource = new MatTableDataSource<any>(response.data ?? []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onActivate(element: any) {
    this.ngxSpinnerService.show();
    this.salvamentosService
      .postSalvamentoActivar(element.IdSalvamento)
      .subscribe(
        (response) => {
          Swal.fire(
            "Activación",
            "El salvamento se ha activado correctamente.",
            "success"
          );
          this.ngxSpinnerService.hide();
          this.getDataAll();
        },
        (error) => {
          console.log(<any>error);
          Swal.fire(
            "Error Activación",
            "El salvamento no pudo activarse.",
            "error"
          );
          this.ngxSpinnerService.hide();
        }
      );
  }

  onCerrar(element: any) {
    this.ngxSpinnerService.show();
    this.salvamentosService
      .postSalvamentoCerrar(element.IdSalvamento)
      .subscribe(
        (response) => {
          Swal.fire(
            "Procesado",
            "El salvamento se ha cerrado correctamente.",
            "success"
          );
          this.ngxSpinnerService.hide();
          this.getDataAll();
        },
        (error) => {
          console.log(<any>error);
          Swal.fire(
            "Error al Procesar",
            "El salvamento no pudo cerrarse.",
            "error"
          );
          this.ngxSpinnerService.hide();
        }
      );
  }

  onNew(): void {
    this.router.navigate(["reclamos/salvamentos/new"]);
  }

  onEdit(row: any): void {
    this.router.navigate(["reclamos/salvamentos", row.IdSalvamento]);
  }

  onDetail(row: any): void {
    this.router.navigate(["reclamos/salvamentos/detalle", row.IdSalvamento]);
  }
}
