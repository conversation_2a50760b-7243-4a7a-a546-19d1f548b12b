import { Component, OnInit, ViewChild } from "@angular/core";
import { UserService } from "../../_services/user.service";
import { CajaService } from "./caja.service";
import { Caja } from "./caja";

import { global } from "../../_services/global";
import swal from "sweetalert2";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { FormularioComponent } from "./formulario/formulario.component";

@Component({
  selector: "app-caja",
  templateUrl: "./caja.component.html",
  styleUrls: ["./caja.component.css"],
  providers: [UserService, CajaService],
})
export class CajaComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public caja: Caja;
  public dataSource = null;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _userService: UserService,
    private _cajaService: CajaService,
    private dialog: MatDialog
  ) {
    this.page_title = "Listado de Cajas";
    this.identity = this._userService.getIdentity();
    this.token = this._userService.getToken();
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "codcaja",
    "desccaja",
    "fecing",
    "stscaja",
    "actions" /*, 'new'*/,
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getCajas();
  }

  getCajas() {
    this._cajaService.getCajas().subscribe(
      (response) => {
        if (response.status == "success") {
          this.dataSource = new MatTableDataSource<Caja>(response.cajas);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
          //console.log(this.cajas);
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  onDelete(id) {
    swal
      .fire({
        title: "¿Estas seguro?",
        text: "Una vez borrado no podrás recuperarlo!",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete) {
          this._cajaService.delete(id).subscribe(
            (response) => {
              swal.fire({ text: "La caja fue borrada!", icon: "success" });
              this.getCajas();
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }

  onEdit(element) {
    //console.log('Editar');
    this.openDialogo(element);
  }

  onNew() {
    this.openDialogo();
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  openDialogo(element: Caja = null): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = { title: "Cajeros", element: element };
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = true;
    dialogConfig.height = "550px";
    dialogConfig.width = "500px";

    const dialogRef = this.dialog.open(FormularioComponent, dialogConfig);
    dialogRef.afterClosed().subscribe((result) => {
      this.getCajas();
    });
  }
}
