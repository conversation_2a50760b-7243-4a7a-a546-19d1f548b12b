<div class="container">
  <h1>{{ pageTitle }}</h1>
</div>
<mat-divider></mat-divider>

<div class="table-responsive" perfectScrollbar>
  <table mat-table [dataSource]="dataSource" matSort>
    <ng-container matColumnDef="idcotizacion">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Cotización</th>
      <td mat-cell *matCellDef="let element">
        {{ element.idcotizacion }}
      </td>
    </ng-container>
    <ng-container matColumnDef="numcert">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Certificado</th>
      <td
        mat-cell
        *matCellDef="let element"
        (click)="close()"
        matTooltip="Modificar Certificado"
      >
        {{ element.numcert }}
      </td>
    </ng-container>
    <ng-container matColumnDef="descripcion">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>Descripcion</th>
      <td mat-cell *matCellDef="let element">
        {{ element.descripcion || "Sin descripcion"}}
      </td>
    </ng-container>

    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr
      mat-row
      *matRowDef="let row; columns: displayedColumns"
      (click)="selectCert(row)"
    ></tr>
  </table>
</div>
<mat-paginator
  [pageSizeOptions]="[5, 10, 20]"
  showFirstLastButtons
></mat-paginator>
<mat-dialog-actions>
  <button
    mat-flat-button
    class="bg-danger"
    (click)="close()"
    matTooltip="Cerrar"
  >
    <mat-icon>cancel</mat-icon>
  </button>
</mat-dialog-actions>
