<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        
        <div class="page_title">
          <h1>{{page_title}}</h1>
        </div>          
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8">         

          <!-- Numero Relacion Ingreso Column -->
          <ng-container matColumnDef="id">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> RelIng </th>
            <td mat-cell *matCellDef="let element"> {{element.id}} </td>
          </ng-container>

          <!-- Estado Column -->
          <ng-container matColumnDef="stsreling">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Estado </th>
            <td mat-cell *matCellDef="let element"> {{element.stsreling}} </td>
          </ng-container>   
          
           <!-- Tipo Obligación Column -->
           <ng-container matColumnDef="tiporeling">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Tipo </th>
            <td mat-cell *matCellDef="let element"> {{element.tiporeling | uppercase}} </td>
          </ng-container> 

          <!-- Moneda Column -->
          <ng-container matColumnDef="codusr">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Cajero </th>
            <td mat-cell *matCellDef="let element"> {{element.codusr}} </td>
          </ng-container>

          <!-- Monto Column -->
          <ng-container matColumnDef="nMtoPagoLocal">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Monto </th>
            <td mat-cell *matCellDef="let element"> {{element.nMtoPagoMoneda | currency :' ':'code':'.2-2'}} </td>
          </ng-container>

          <!-- Fecha Emisión Column -->
          <ng-container matColumnDef="fecstsreling">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> Fecha </th>
            <td mat-cell *matCellDef="let element"> {{element.fecstsreling | date:'dd/MM/yyyy'}} </td>
          </ng-container>

           <!-- Nombre Cliente Column -->
         <ng-container matColumnDef="nombre">
           <th mat-header-cell *matHeaderCellDef mat-sort-header> Nombre Cliente</th>
           <td mat-cell *matCellDef="let element"> {{element.nombre}} </td>
         </ng-container>

          <!-- Action Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> </th>
            <td mat-cell *matCellDef="let element">
              <button mat-raised-button color="basic" 
              (click)="onDetail(element)"
                      matTooltip="Ver Detalle de la Relación de Ingreso"
                      matTooltipClass="tooltip-red">
                Detalle<mat-icon>subject</mat-icon>
              </button>
              &nbsp;
              <button mat-flat-button color="accent" (click)="onCancel(element)" [disabled]="onDisabledBtnCancel(element.stsreling)"
                      matTooltip="Anular Relación de Ingreso"
                      matTooltipClass="tooltip-red"
              >
                Anular<mat-icon>fact_check</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

        </table>
        <mat-paginator [pageSizeOptions]="[5, 10, 20]" showFirstLastButtons></mat-paginator>

      </div>
    </div>
  </div>
</div>
