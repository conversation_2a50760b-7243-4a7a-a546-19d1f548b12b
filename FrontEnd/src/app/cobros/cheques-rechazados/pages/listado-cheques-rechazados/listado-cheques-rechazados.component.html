<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field class="form-search">
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
          <div class="btn-new">
            <button
              mat-raised-button
              (click)="showFilter = true"
              *ngIf="!showFilter"
              matTooltip="Búsqueda Avanzada"
              matTooltipClass="tooltip-red"
              class="btn-min-text btn-sup-action"
            >
              <mat-icon>search</mat-icon> Avanzada
            </button>
          </div>
        </div>

        <div class="filtros" *ngIf="showFilter">
          <mat-form-field
            *ngFor="let filter of filterSelectObj"
            class="input-formulario"
          >
            <mat-label>Filtro {{ filter.name }}</mat-label>
            <mat-select
              name="{{ filter.columnProp }}"
              [(ngModel)]="filter.modelValue"
              (selectionChange)="filterChange(filter, $event)"
            >
              <mat-option [value]="item" *ngFor="let item of filter.options">{{
                item
              }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div
          class="filtros-buttons"
          *ngIf="showFilter"
          style="margin-bottom: 15px; text-align: right"
        >
          <button
            mat-raised-button
            class="btn-min-text"
            (click)="showFilter = false; resetFilters()"
          >
            Cancelar
          </button>
          <button
            style="display: inline-block"
            class="btn-min-text"
            mat-raised-button
            color="accent"
            (click)="resetFilters()"
          >
            Limpiar
          </button>
        </div>

        <div class="table-responsive" perfectScrollbar>
          <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="mat-elevation-z8"
          >
            <!-- Correlativo Column -->
            <ng-container matColumnDef="correlativo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Correlativo
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.correlativo }}
              </td>
            </ng-container>

            <!-- Póliza Column -->
            <ng-container matColumnDef="poliza">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Póliza</th>
              <td mat-cell *matCellDef="let element">{{ element.poliza }}</td>
            </ng-container>

            <!-- Requerimiento Column -->
            <ng-container matColumnDef="idrequerimiento">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Requerimiento
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.idrequerimiento }}
              </td>
            </ng-container>

            <!-- numacre Column -->
            <ng-container matColumnDef="numacre">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                No. Acreencia
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.identidad_acreedora }}
              </td>
            </ng-container>

            <!-- Requerimiento Column -->
            <ng-container matColumnDef="no_cheque">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                No. Cheque
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.no_cheque }}
              </td>
            </ng-container>

            <!-- Requerimiento Column -->
            <ng-container matColumnDef="fecha_rechazo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                F. Rechazo
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.fecha_rechazo }}
              </td>
            </ng-container>

            <!-- Requerimiento Column -->
            <ng-container matColumnDef="monto_cheque">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Monto Ch.
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.monto_cheque }}
              </td>
            </ng-container>

            <!-- Gastos Admon Column -->
            <ng-container matColumnDef="gastos_admin">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Monto Acreencia
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.gastos_admin }}
              </td>
            </ng-container>

            
            <!-- estado Column -->
            <ng-container matColumnDef="estado">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
              <td mat-cell *matCellDef="let element">{{ element.estado }}</td>
            </ng-container>

             
                  <!-- DiasVencidos Column -->
                  <ng-container matColumnDef="DiasVencidos">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Dias Vencidos</th>
                    <td mat-cell *matCellDef="let element">{{ element.DiasVencidos }}</td>
                  </ng-container>

                        <!-- numerodocumentofel Column -->
                        <ng-container matColumnDef="numerodocumentofel">
                          <th mat-header-cell *matHeaderCellDef mat-sort-header>Fel Factura</th>
                          <td mat-cell *matCellDef="let element">{{ element.numerodocumentofel }}</td>
                        </ng-container>

                            <!-- numerodocumentofel Column -->
                            <ng-container matColumnDef="felnc">
                              <th mat-header-cell *matHeaderCellDef mat-sort-header>Fel NC</th>
                              <td mat-cell *matCellDef="let element">{{ element.felnc }}</td>
                            </ng-container>

                              <!-- numerodocumentofel Column -->
                              <ng-container matColumnDef="fecha_reling">
                                <th mat-header-cell *matHeaderCellDef mat-sort-header>Fecha Reling</th>
                                <td mat-cell *matCellDef="let element">{{ element.fecha_reling }}</td>
                              </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
              <td mat-cell *matCellDef="let element">
                <button
                  mat-raised-button
                  color="basic"
                  (click)="onDetail(element)"
                  matTooltip="Detalle Cheque"
                  matTooltipClass="tooltip-red"
                >
                   <mat-icon>subject</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onEdit(element)"
                  matTooltip="Editar Registro"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onActivate(element)"
                  [disabled]="onDisabledBtnStatus(element.estado)"
                  matTooltip="Activar"
                  matTooltipClass="tooltip-red"
                >
                   <mat-icon>check_circle</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-raised-button
                  color="basic"
                  (click)="onExonerarGastos(element)"
                  matTooltip="Exonerar Gastos Administrativos"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>money_off</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onDelete(element)"
                  [disabled]="onDisabledBtnStatusAnular(element.estado)"
                  matTooltip="Anular Registro"
                  matTooltipClass="tooltip-red"
                >
                   <mat-icon>delete</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onRevert(element)"
                  [disabled]="onDisabledBtnStatusAnular(element.estado)"
                  matTooltip="Recuperar Registro"
                  matTooltipClass="tooltip-red"
                >
                   <mat-icon>restart_alt</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-raised-button
                  color="basic"
                  (click)="onGenerate(element)"
                  [disabled]="
                    onDisabledBtnGenerate(element.identidad_acreedora)
                  "
                  matTooltip="Generar Acreencia"
                  matTooltipClass="tooltip-red"
                >
                 <mat-icon>bookmark_added</mat-icon>
                </button>
                &nbsp;


                <button
                  mat-flat-button
                  class="bt-1"
                  color="accent"
                  (click)="onNotaCredito(element)"
                  [disabled]="!onDisabledBtnApply(element) "
                  matTooltip="Nota Credito"
                  matTooltipClass="tooltip-red"
                >
                 Generar NC <mat-icon>task</mat-icon>
                </button>

                
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
