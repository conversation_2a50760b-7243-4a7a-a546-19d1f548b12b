import { Component, Inject, OnInit } from "@angular/core";
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ComisionEspecialIntermediarioService } from "../../services/comision-especial-intermediario.service";

@Component({
  selector: "app-comision-especial-intermediario",
  templateUrl: "./comision-especial-intermediario.component.html",
  styleUrls: ["./comision-especial-intermediario.component.css"],
})
export class ComisionEspecialIntermediarioComponent implements OnInit {
  formulario = new FormGroup({
    comision: new FormControl(""),
    sinComision: new FormControl(true),
  });

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { idpoliza: number } },
    private fb: FormBuilder,
    private comisionEspecialIntermediarioService: ComisionEspecialIntermediarioService,
    private dialogRef: MatDialogRef<ComisionEspecialIntermediarioComponent>,
  ) {}

  ngOnInit() {
    this.listen();
  }

  listen() {
    this.formulario.get("sinComision").valueChanges.subscribe((value) => {
      if (value) {
        this.formulario.get("comision").disable();
        this.formulario.get("comision").clearValidators();
      } else {
        this.formulario.get("comision").enable();
        this.formulario.get("comision").setValidators(Validators.required);
      }
      this.formulario.get("comision").updateValueAndValidity();
    });
    this.formulario.get("comision").valueChanges.subscribe((value) => {
      if (value && this.formulario.get("sinComision").value) {
        this.formulario.get("sinComision").setValue(false);
      }
    });
  }

  guardar() {
    console.log(this.formulario.value);
    if (this.formulario.value.sinComision) {
      this.formulario.value.comision = 0;
      this.dialogRef.close(true);
      return;
    }
    const data = {
      ...this.formulario.value,
      idpoliza: this.data.element.idpoliza,
    };
    this.comisionEspecialIntermediarioService.saveComision(data).subscribe({
      next: (data) => {
        console.log("success", data);
      },
      error: (error) => {
        console.log("error", error);
      },
    });
    this.dialogRef.close(this.formulario.value);
  }
}
