import { Component, OnInit } from "@angular/core";
import { Router } from "@angular/router";
import { IActions } from "src/app/shared/interfaces/actions.interface";
import { FichaContactosService } from "../../services/ficha-contactos.service";

@Component({
  selector: "app-ficha-contacto",
  templateUrl: "./ficha-contacto.component.html",
  styleUrls: ["./ficha-contacto.component.css"],
})
export class FichaContactoComponent implements OnInit {
  actions: IActions[] = [
    {
      color: "accent",
      icon: "info",
      tooltipText: "Ver",
      onClick: ({ row }) => {
        this.router.navigate(["/reaseguros/ficha-contacto", row.id]);
      },
    },
  ];

  constructor(
    private router: Router,
    public fichaContactosService: FichaContactosService,
  ) {}

  ngOnInit() {}

  onNew() {
    this.router.navigate(["/reaseguros/ficha-contacto/new"]);
  }
}
