import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";
import { IContacto_Direccion } from "../interfaces/ficha-contacto.interface";

@Injectable({
  providedIn: "root",
})
export class ContactoDireccionService {
  private readonly urlBase = environment.apiUrlv1 + "/reaseguro";

  constructor(private readonly http: HttpClient) {}

  getDirecciones() {
    return this.http.get(`${this.urlBase}/contacto-direccion`).pipe(
      map((res: { data: IContacto_Direccion[] }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getDireccion(id: number) {
    return this.http.get(`${this.urlBase}/contacto-direccion/${id}`).pipe(
      map((res: { data: IContacto_Direccion[] }) => {
        if (res.data) return res.data;
      }),
    );
  }

  getDireccionByContacto(id: number) {
    return this.http
      .get(`${this.urlBase}/contacto-direccion/find-by-contacto/${id}`)
      .pipe(
        map((res: { data: IContacto_Direccion[] }) => {
          if (res.data) return res.data;
        }),
      );
  }

  createDireccion(direccion: IContacto_Direccion) {
    return this.http.post(`${this.urlBase}/contacto-direccion`, direccion).pipe(
      map((res: { data: IContacto_Direccion }) => {
        if (res.data) return res.data;
      }),
    );
  }

  updateDireccion(id: number, direccion: IContacto_Direccion) {
    return this.http
      .put(`${this.urlBase}/contacto-direccion/${id}`, direccion)
      .pipe(
        map((res: { data: IContacto_Direccion }) => {
          if (res.data) return res.data;
        }),
      );
  }

  deleteDireccion(id: number) {
    return this.http.delete(`${this.urlBase}/contacto-direccion/${id}`).pipe(
      map((res: { data: IContacto_Direccion }) => {
        if (res.data) return res.data;
      }),
    );
  }
}
