<div *ngIf="dataSource.data.length > 0">
  <table mat-table [dataSource]="dataSource" style="width: 100%">
    <ng-container [matColumnDef]="col.key" *ngFor="let col of columnsSchema">
      <th mat-header-cell *matHeaderCellDef [ngSwitch]="col.key">
        <span *ngSwitchCase="'indmarcar'">
          <mat-checkbox
            (change)="selectAll($event)"
            [checked]="isAllSelected()"
            [indeterminate]="!isAllSelected() && isAnySelected()"
          ></mat-checkbox>
        </span>
        <span *ngSwitchDefault>{{ col.label }}</span>
      </th>
      <td mat-cell *matCellDef="let element">
        <div [ngSwitch]="col.type" *ngIf="!element.isEdit">
          <ng-container *ngSwitchCase="'indmarcar'">
            <mat-checkbox
              (change)="element.indmarcar = $event.checked; editRow(element)"
              [checked]="element.indmarcar"
            ></mat-checkbox>
          </ng-container>
          <div class="btn-edit" *ngSwitchCase="'isEdit'">
            <button mat-button (click)="element.isEdit = !element.isEdit">
              <mat-icon>mode_edit</mat-icon>
            </button>
          </div>
          <span *ngSwitchDefault>
            {{ element[col.key] }}
          </span>
        </div>
        <div [ngSwitch]="col.type" *ngIf="element.isEdit">
          <div *ngSwitchCase="'indmarcar'"></div>
          <div *ngSwitchCase="'isEdit'">
            <button
              mat-mini-fab
              color="primary"
              (click)="editRow(element)"
              [disabled]="disableSubmit(element.id)"
            >
              <mat-icon>done</mat-icon>
            </button>
          </div>
          <mat-form-field class="form-input" *ngSwitchDefault>
            <input
              matInput
              [required]="col.required"
              [pattern]="col.pattern"
              [type]="col.type"
              [readonly]="col.readonly"
              [(ngModel)]="element[col.key]"
              (change)="inputHandler($event, element.id, col.key)"
            />
          </mat-form-field>
        </div>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
</div>
