import { Component, OnInit, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { Router } from "@angular/router";

import { UserService } from "src/app/_services/user.service";
import { ConsultasFacturasService } from "../../services/consultas-facturas.service";

import { NgxSpinnerService } from "ngx-spinner";

@Component({
  selector: "app-consultas-facturas",
  templateUrl: "./consultas-facturas.component.html",
  styleUrls: ["./consultas-facturas.component.css"],
})
export class ConsultasFacturasComponent implements OnInit {
  public identity: string;
  public page_title: string;
  public status: string;
  public dataSource: any;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  displayedColumns: string[] = [
    "poliza",
    "nit",
    "contratante",
    "estado",
    "requerimiento",
    "serie_fel",
    "factura_fel",
    /* "compromiso",
    "nota", */
    "monto",
    "actions",
  ];

  constructor(
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService,
    private consultasFacturasService: ConsultasFacturasService,
    private _userService: UserService
  ) {
    this.page_title = "Consultas y Movimientos";
    this.identity = this._userService.getIdentity();
  }

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  ngOnInit(): void {
    this.getDataAll();
  }

  getDataAll() {
    this.ngxSpinnerService.show();
    this.consultasFacturasService.getConsultaFacturas(this.identity).subscribe(
      (response) => {
        this.dataSource = new MatTableDataSource<any>(response.data ?? []);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  getPreviewFactura(element: any) {
    return element.ruta_fel || "";
  }

  onDisablePreviewFactura(element: any) {
    return (element.ruta_fel || "") == "" ? true : false;
  }
}
