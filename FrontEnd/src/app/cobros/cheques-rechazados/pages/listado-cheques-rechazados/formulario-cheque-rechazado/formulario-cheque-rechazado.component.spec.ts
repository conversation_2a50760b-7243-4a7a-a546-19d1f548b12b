import { ComponentFixture, TestBed, waitForAsync as  } from '@angular/core/testing';

import { FormularioChequeRechazadoComponent } from './formulario-cheque-rechazado.component';

describe('FormularioChequeRechazadoComponent', () => {
  let component: FormularioChequeRechazadoComponent;
  let fixture: ComponentFixture<FormularioChequeRechazadoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ FormularioChequeRechazadoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FormularioChequeRechazadoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
