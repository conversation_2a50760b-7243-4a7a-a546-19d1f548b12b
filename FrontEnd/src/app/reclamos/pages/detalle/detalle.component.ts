import { Location } from "@angular/common";
import { Component, OnInit, ViewChild } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator } from "@angular/material/paginator";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ActivatedRoute } from "@angular/router";
import { BehaviorSubject, Observable } from "rxjs";
import { switchMap } from "rxjs/operators";
import { TableColumn } from "src/app/components/icore-table/table";
import { IDetalleReclamo, IReclamoBitacoraPayload } from "src/app/interface";
import { UserService } from "src/app/_services/user.service";
import { ReclamosService } from "../../services/reclamos.service";

//ALERTS
import { SmsComponent } from "src/app/gestion-cartera/sms/sms.component";
import { EmailComponent } from "src/app/components/email/email.component";
import { ModalService } from "src/app/_services/modal.service";


@Component({
  selector: "app-detalle",
  templateUrl: "./detalle.component.html",
  styleUrls: ["./detalle.component.css"],
})
export class DetalleComponent implements OnInit {
  pageTitle = "Detalle reclamo";
  idreclamo: string = "";
  numcert: string = "";
  showBitacora: boolean = false;
  edit: boolean = false;
  idbitacora_manual: string = "";
  tel: string = "";
  textosms: string = "";
  email: string = "";
  textoemail: string = "";

  formBitacora = new FormGroup({
    observaciones: new FormControl("", Validators.required),
  });

  displayedColumnsBitacora: TableColumn[] = [
    {
      columnDef: "fechabitacora",
      header: "Fecha",
      cell: (element: Record<string, any>) => `${element["fechabitacora"]}`,
    },
    {
      columnDef: "descripcion",
      header: "Descripcion",
      cell: (element: Record<string, any>) => `${element["descripcion"]}`,
    },
    {
      columnDef: "actions",
      header: "",
      actions: [
        {
          color: "primary",
          icon: "edit",
          tooltipText: "Editar",
          onClick: ({ row }) => this.onEdit(row.idbitacora_manual),
        },
      ],
    },
  ];

  displayedColumnsPersonal: TableColumn[] = [
    {
      columnDef: "etiqueta",
      header: "Etiqueta",
      cell: (element: Record<string, any>) => `${element["etiqueta"]}`,
    },
    {
      columnDef: "valor",
      header: "Valor",
      cell: (element: Record<string, any>) =>
        `${element["valor"] || "sin valor"}`,
    },
  ];

  refreshData$ = new BehaviorSubject<boolean>(true);
  bitacoraSource$: Observable<any>;
  dataDetalle$!: Observable<IDetalleReclamo>;

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  constructor(
    private dialog: MatDialog,
    private modalService: ModalService,
    private reclamosService: ReclamosService,
    private route: ActivatedRoute,
    private userService: UserService,
    private location: Location,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.idreclamo = this.route.snapshot.paramMap.get("id");
    this.numcert = this.route.snapshot.paramMap.get("certificado");
    this.dataDetalle$ = this.reclamosService.getReclamoDetalle(
      this.idreclamo,
      this.numcert
    );
    this.bitacoraSource$ = this.refreshData$.pipe(
      switchMap((_) => this.reclamosService.getBitacoraReclamo(this.idreclamo))
    );
  }

  onShowBitacora() {
    this.showBitacora = !this.showBitacora;
  }

  submitBitacora() {
    this.saveBitacora();
  }

  openDialogoSMS() {
      const actionClose = {
        next: (result: any) => {
          this.tel = result.telefono;
          this.textosms = result.texto;

          const payload: IReclamoBitacoraPayload = {
            observaciones: `Mensaje SMS enviado a ${this.tel} texto: ${this.textosms}`,
            usuario: this.userService.getIdentity(),
            idreclamo: this.idreclamo,
          };
          this.reclamosService.createBitacora(payload).subscribe({
            next: () => {
              this.snackBar.open("Guardado", "", {
                duration: 5 * 1000,
                horizontalPosition: "start",
              });
              this.formBitacora.reset();
              this.refreshData$.next(true);
            },
    });
        },
        error: (e) => {
          console.error(e);
        },
      };

     this.modalService.openDialog<SmsComponent>({
       component: SmsComponent,
       title: "Enviar SMS",
       width: "400px",
       element: {
         tipo_operacion: "165",
       },
       actionClose,
     });
    
   

  }

  openDialogoEmail() {

      const actionClose = {
        next: (result: any) => {
        
          this.email = result.email;
          this.textoemail = result.mensaje;

          const payload: IReclamoBitacoraPayload = {
            observaciones: `Email  enviado a ${this.email} texto: ${this.textoemail}`,
            usuario: this.userService.getIdentity(),
            idreclamo: this.idreclamo,
          };
          this.reclamosService.createBitacora(payload).subscribe({
            next: () => {
              this.snackBar.open("Guardado", "", {
                duration: 5 * 1000,
                horizontalPosition: "start",
              });
              this.formBitacora.reset();
              this.refreshData$.next(true);
            },
          });
        },
        error: (e) => {
          console.error(e);
        },
      };

    this.modalService.openDialog<EmailComponent>({
      title: "Enviar Correo",
      component: EmailComponent,
      width: "400px",
      element: {
        idtipogestion: "2",
      },actionClose,
    });
  }

  saveBitacora() {
    const payload: IReclamoBitacoraPayload = {
      ...this.formBitacora.value,
      usuario: this.userService.getIdentity(),
      idreclamo: this.idreclamo,
    };
    this.reclamosService.createBitacora(payload).subscribe({
      next: () => {
        this.snackBar.open("Guardado", "", {
          duration: 5 * 1000,
          horizontalPosition: "start",
        });
        this.formBitacora.reset();
        this.refreshData$.next(true);
      },
    });
  }

  onEdit(idbitacora_manual: string) {
    this.idbitacora_manual = idbitacora_manual;
    this.reclamosService.getBitacoraById(idbitacora_manual).subscribe({
      next: (value) => {
        this.formBitacora.controls["observaciones"].setValue(value.descripcion);
        this.edit = true;
      },
    });
  }

  saveEdit(idbitacora_manual: string) {
    this.reclamosService
      .updateBitacora({ ...this.formBitacora.value }, idbitacora_manual)
      .subscribe({
        next: () => {
          this.snackBar.open("Registro bitacora actualizado", "", {
            duration: 5 * 1000,
            horizontalPosition: "start",
          });
          this.formBitacora.reset();
          this.edit = false;
          this.refreshData$.next(true);
        },
      });
  }

  onBack() {
    this.location.back();
  }
}
