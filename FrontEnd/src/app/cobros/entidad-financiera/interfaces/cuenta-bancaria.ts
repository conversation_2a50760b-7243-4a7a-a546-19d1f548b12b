export interface CuentaBancaria {
  idcuentabancaria: number;
  identfinan: number;
  tipo_cuenta: string;
  numero_cuenta: string;
  descripcion: string;
  codmoneda: string;
  stscta: string;
  fecsts: Date;
  saldolocal: number;
  saldomoneda: string;
  fecultconcil: Date;
  numctaaux: null;
  idecta: null;
  saldoconcilentfinanlocal: string;
  saldoconcilentfinanmoneda: string;
  CUENTA_CONTABLE: null;
  saldolocal_cadena: string;
  saldomoneda_cadena: string;
}

export interface PayloadCuentaBancaria {
  identfinan: number;
  tipo_cuenta: string;
  numero_cuenta: string;
  descripcion: string;
  codmoneda: string;
  fecsts: Date;
  saldolocal: string;
  saldomoneda: string;
  fecultconcil: Date;
  saldoconcilentfinanlocal: string;
  saldoconcilentfinanmoneda: string;
  saldolocal_cadena: string;
  saldomoneda_cadena: string;
}
