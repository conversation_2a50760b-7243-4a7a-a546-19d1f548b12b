<div class="app-content content">
  <div class="content-wrapper">
    <button-back-reclamos></button-back-reclamos>
    <div class="content-body">
      <div class="page_title text-center">
        <h1>{{ updating ? 'Editar' : 'Nueva' }} Factura Externa</h1>
      </div>
      <hr />
      
      <form [formGroup]="formFacturaExterna" (ngSubmit)="submitFacturaExterna()">
        <div class="container">
          <div class="row">
            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Fecha</mat-label>
              <input
                matInput
                [matDatepicker]="picker"
                formControlName="fecha"
                placeholder="Seleccionar fecha"
                readonly
              />
              <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
              <mat-error *ngIf="formFacturaExterna.get('fecha')?.hasError('required')">
                La fecha es requerida
              </mat-error>
            </mat-form-field>

            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Persona</mat-label>
              <input
                (click)="searchCliente()"
                type="text"
                formControlName="persona"
                matInput
                placeholder="Buscar Cliente"
                readonly
              />
              <mat-error *ngIf="formFacturaExterna.get('persona')?.hasError('required')">
                La persona es requerida
              </mat-error>
            </mat-form-field>
          </div>

          <div class="row">
            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Factura No.</mat-label>
              <input
                type="text"
                formControlName="facturano"
                matInput
                placeholder="Número de factura"
              />
              <mat-error *ngIf="formFacturaExterna.get('facturano')?.hasError('required')">
                El número de factura es requerido
              </mat-error>
            </mat-form-field>

            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Tipo</mat-label>
              <mat-select formControlName="tipo">
                <mat-option 
                  *ngFor="let tipo of tiposFactura$ | async" 
                  [value]="tipo.codigo"
                >
                  {{ tipo.descripcion }}
                </mat-option>
              </mat-select>
              <mat-error *ngIf="formFacturaExterna.get('tipo')?.hasError('required')">
                El tipo es requerido
              </mat-error>
            </mat-form-field>
          </div>

          <div class="row">
            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Cantidad</mat-label>
              <input
                type="number"
                formControlName="cantidad"
                matInput
                placeholder="Cantidad"
                min="0"
                step="0.01"
                (blur)="calcularIvaYNeto()"
              />
              <mat-error *ngIf="formFacturaExterna.get('cantidad')?.hasError('required')">
                La cantidad es requerida
              </mat-error>
              <mat-error *ngIf="formFacturaExterna.get('cantidad')?.hasError('min')">
                La cantidad debe ser mayor a 0
              </mat-error>
            </mat-form-field>

            <mat-form-field class="col-md-6" appearance="outline">
              <mat-label>Total Factura</mat-label>
              <input
                type="number"
                formControlName="totalfactura"
                matInput
                placeholder="Total de la factura"
                min="0"
                step="0.01"
                (blur)="calcularIvaYNeto()"
              />
              <mat-error *ngIf="formFacturaExterna.get('totalfactura')?.hasError('required')">
                El total de factura es requerido
              </mat-error>
              <mat-error *ngIf="formFacturaExterna.get('totalfactura')?.hasError('min')">
                El total debe ser mayor a 0
              </mat-error>
            </mat-form-field>
          </div>

          <div class="row mt-4">
            <div class="col-12 d-flex justify-content-end">
              <button
                type="button"
                mat-stroked-button
                color="warn"
                class="mr-3"
                (click)="cancelar()"
              >
                Cancelar
              </button>
              <button
                type="submit"
                mat-flat-button
                color="primary"
                [disabled]="formFacturaExterna.invalid"
                [matTooltip]="textTooltip"
              >
                <mat-icon>save</mat-icon>
                {{ textTooltip }}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
