<div class="app-content content">
  <div class="content-wrapper">
    <h2 class="text-center">Detalle Cliente</h2>
    <div class="content-header row mb-1"></div>
    <div class="content-body">
      <button-back *ngIf="stepper.selectIndex === 3"></button-back>
      <mat-stepper linear #stepper [selectedIndex]="stepper.selectedIndex">
        <mat-step [stepControl]="form">
          <ng-template matStepLabel>General</ng-template>
          <app-paso-uno-cliente
            *ngIf="stepper.selectedIndex === 0"
            (nextStep)="next()"
          ></app-paso-uno-cliente>
        </mat-step>

        <mat-step>
          <ng-template matStepLabel>Detalle</ng-template>

          <app-paso-dos-cliente
            (previousStep)="previous()"
            *ngIf="stepper.selectedIndex === 1"
          ></app-paso-dos-cliente>
        </mat-step>
      </mat-stepper>
    </div>
  </div>
</div>
