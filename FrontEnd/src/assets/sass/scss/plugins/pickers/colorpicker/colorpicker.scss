// MiniColors Picker
.minicolors-theme-bootstrap{
    .minicolors-swatch{
        top: 6px !important;
        left: 6px !important;
        height: 22px;
        width: 23px;
    }
    .minicolors-input{
        padding-left: 43px !important;
    }
}

// Spectrum
.sp-button-container{
    width: 100%;
    .sp-cancel,
    .sp-choose,
    .sp-palette-toggle {
        border:0;
        border-radius: 2px;
        padding: 6px 0px;
        float: left;
        width: 48%;
        text-align: center;
        outline: 0;
    }
    .sp-cancel, .sp-palette-toggle, .sp-choose {
        border: 1px solid transparent;
    }
    .sp-cancel{
        color: #393939 !important;
        background-color: #fcfcfc;
        margin-right: 2%;
        border-color: #ddd;
        padding-top: 3px;
        &:hover{
            color: #393939 !important;
            text-decoration: none;
        }
    }
    .sp-choose {
        color: #fff;
        background-color: #50CAED;
    }
}
.sp-container{
    button{
        background-image:none !important;
        text-shadow: none !important;
        &:hover{
            border:1px solid transparent !important;
            color: #fff;
            background-color: #50CAED !important;
        }
        &:active{
            border: 1px solid transparent !important;
            color: #fff;
            background-color: #50CAED !important;
            box-shadow: none !important;
        }
    }
}
