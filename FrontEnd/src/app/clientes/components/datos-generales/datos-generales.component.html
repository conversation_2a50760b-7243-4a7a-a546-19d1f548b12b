<form class="form form-horizontal" [formGroup]="clienteForm">
  <div class="form-body">
    <h4 class="form-section">
      <i class="feather ft-user"></i> Persona Datos generales
    </h4>
    <div class="form-group row mx-auto">
      <mat-form-field class="col" appearance="outline">
        <mat-label>Persona</mat-label>
        <input
          type="text"
          matInput
          formControlName="idpersona"
          [matAutocomplete]="autoPersona"
        />
        <mat-autocomplete
          #autoPersona="matAutocomplete"
          [displayWith]="displayPersonaFn(personas)"
        >
          <mat-option
            *ngFor="let persona of filteredPersonas | async"
            [value]="persona.id"
          >
            {{ getNombrePersona(persona) }}
          </mat-option>
        </mat-autocomplete>
        <mat-error *ngIf="!isPersonValid">Persona no valida</mat-error>
      </mat-form-field>
      <div *ngIf="!isPersonValid">
        <button
          type="button"
          class="btn btn-success"
          [routerLink]="[
            '/personas/detalle/',
            clienteForm.controls.idpersona.value
          ]"
        >
          Verificar
        </button>
      </div>
    </div>
  </div>
</form>
