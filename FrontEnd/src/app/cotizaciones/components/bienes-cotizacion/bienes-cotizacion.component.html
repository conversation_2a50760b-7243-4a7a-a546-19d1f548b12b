<button class="btn btn-danger my-2" mat-mini-fab (click)="close()">
  <mat-icon>close</mat-icon>
</button>

<mat-accordion>
  <mat-expansion-panel *ngFor="let formulario of formularios | keyvalue">
    <mat-expansion-panel-header>
      <mat-panel-title>
        {{ formulario.key }}
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div>
      <form
        *ngFor="let value of formulario.value"
        (submit)="save(formulario.key)"
      >
        <strong class="mx-1">{{ value.descripcion }}: </strong>
        <mat-form-field class="mr-2">
          <input
            matInput
            placeholder="Suma"
            [(ngModel)]="value.suma"
            (ngModelChange)="calcularPrima(formulario.key)"
            name="suma"
          />
        </mat-form-field>
        <mat-form-field class="mr-2">
          <input
            matInput
            placeholder="Tasa %"
            [(ngModel)]="value.tasa"
            (ngModelChange)="calcularPrima(formulario.key)"
            name="tasa"
          />
        </mat-form-field>
        <mat-form-field class="mr-2">
          <input
            matInput
            placeholder="Prima"
            readonly
            [ngModel]="value.prima"
            name="prima"
          />
        </mat-form-field>
        <button mat-mini-fab color="primary">
          <mat-icon>save</mat-icon>
        </button>
      </form>
    </div>
  </mat-expansion-panel>
</mat-accordion>
