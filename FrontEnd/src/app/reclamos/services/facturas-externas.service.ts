import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";
import {
  IResponseData2,
  FacturaExterna,
  FacturaExternaPayload,
  TipoFactura,
} from "src/app/interface/facturas-externas.interface";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class FacturasExternasService {
  private readonly urlBase: string = environment.apiUrl;

  constructor(private http: HttpClient) {}

  createFacturaExterna(body: FacturaExternaPayload) {
    return this.http.post(`${this.urlBase}/reclamos/facturas-externas`, body);
  }

  getFacturasExternas(): Observable<FacturaExterna[]> {
    return this.http
      .get(`${this.urlBase}/reclamos/facturas-externas`)
      .pipe(map((res: Partial<IResponseData2<FacturaExterna[]>>) => res.data));
  }

  getFacturaExternaById(idfacturaexterna: number): Observable<FacturaExterna> {
    return this.http.get<FacturaExterna>(
      `${this.urlBase}/reclamos/facturas-externas/${idfacturaexterna}`
    );
  }

  updateFacturaExterna(idfacturaexterna: number, body: FacturaExternaPayload) {
    return this.http.put(
      `${this.urlBase}/reclamos/facturas-externas/${idfacturaexterna}`,
      body
    );
  }

  activarFacturaExterna(idfacturaexterna: number) {
    return this.http.patch(
      `${this.urlBase}/reclamos/facturas-externas/activar/${idfacturaexterna}`,
      {}
    );
  }

  eliminarFacturaExterna(idfacturaexterna: number) {
    return this.http.patch(
      `${this.urlBase}/reclamos/facturas-externas/eliminar/${idfacturaexterna}`,
      {}
    );
  }

  getTiposFactura(): Observable<TipoFactura[]> {
    return this.http
      .post(`${this.urlBase}/lval/catalogo/TIPOFACTEXT`, {})
      .pipe(map((res: any) => res.lval));
  }
}
