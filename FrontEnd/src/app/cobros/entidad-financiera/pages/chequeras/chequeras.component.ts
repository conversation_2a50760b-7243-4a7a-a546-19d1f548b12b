import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { BehaviorSubject } from "rxjs";
import { switchMap } from "rxjs/operators";
import { ChequeraService } from "../../services/chequera.service";
import { TableColumn } from "src/app/components/icore-table/table";
import { Chequera } from "../../interfaces/chequera";
import { ModalService } from "src/app/_services/modal.service";
import { FormularioChequerasComponent } from "../formulario-chequeras/formulario-chequeras.component";

@Component({
  selector: "app-chequeras",
  templateUrl: "./chequeras.component.html",
  styles: [
    `
      .container-btn-new {
        display: flex;
        justify-content: space-between;
      }
    `,
  ],
})
export class ChequerasComponent implements OnInit {
  pageTitle = "Chequeras";
  refreshData$ = new BehaviorSubject<void>(null);
  data$ = this.getCuentaChequera();
  idCuentaBancaria: string;
  displayedColumns: TableColumn<Chequera>[] = [
    {
      columnDef: "identfinan",
      header: "#",
      cell: (element: Record<string, any>) => `${element["identfinan"]}`,
    },
    {
      columnDef: "idcuentabancaria",
      header: "# Cuenta bancaria",
      cell: (element: Record<string, any>) => `${element["idcuentabancaria"]}`,
    },
    {
      columnDef: "numchequera",
      header: "# Chequera",
      cell: (element: Record<string, any>) => `${element["numchequera"] ?? ""}`,
    },
    {
      columnDef: "numchqini",
      header: "# Cheque Inicial",
      cell: (element: Record<string, any>) => `${element["numchqini"] ?? ""}`,
    },
    {
      columnDef: "numchqfin",
      header: "# Cheque Final",
      cell: (element: Record<string, any>) => `${element["numchqfin"] ?? ""}`,
    },
    {
      columnDef: "tipochequera",
      header: "Tipo chequera",
      cell: (element: Record<string, any>) =>
        `${element["tipochequera"] ?? ""}`,
    },
    {
      columnDef: "numctrlchq",
      header: "# Control",
      cell: (element: Record<string, any>) => `${element["numctrlchq"] ?? ""}`,
    },
    {
      columnDef: "stschequera",
      header: "Estado",
      cell: (element: Record<string, any>) => `${element["stschequera"] ?? ""}`,
      chip: [
        {
          color: "none",
          validationProperty: "ANU",
        },
        {
          color: "primary",
          validationProperty: "ACT",
        },
      ],
    },
    {
      columnDef: "fecsts",
      header: "Fecha modificacion",
      cell: (element: Record<string, any>) => `${element["fecsts"] ?? ""}`,
      pipes: {
        token: "date",
        format: "dd/MM/YYYY",
      },
    },
    {
      columnDef: "actions",
      header: "",
      actions: [
        {
          color: "primary",
          icon: "add_card",
          tooltipText: "Cheques",
          onClick: ({ row }) =>
            this.router.navigate(["/cobros/chequera", row.idcuentabancaria]),
        },
      ],
    },
  ];

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private chequeraService: ChequeraService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((param) => (this.idCuentaBancaria = param?.id));
  }

  newChequera() {
    const actionClose = {
      next: () => {
        this.refreshData$.next();
      },
    };
    this.modalService.openDialog<FormularioChequerasComponent>({
      component: FormularioChequerasComponent,
      title: "Crear chequera",
      element: {
        idcuentabancaria: this.idCuentaBancaria,
      },
      actionClose,
    });
  }

  private getCuentaChequera() {
    return this.refreshData$.pipe(
      switchMap((_) => this.chequeraService.getChequeras(this.idCuentaBancaria))
    );
  }
}
