import { Component, OnInit, Input, Inject } from "@angular/core";
import { MAT_DIALOG_DATA } from "@angular/material/dialog";
import { ModalService } from "src/app/_services/modal.service";
import { BordereauxModalComponent } from "../bordereaux-modal/bordereaux-modal.component";
import { DistribucionMensualModalComponent } from "../distribucion-mensual-modal/reaseguro-cedido-modal.component";

@Component({
  selector: "app-card-container",
  templateUrl: "./report-options.component.html",
  styleUrls: ["./report-options.component.css"],
})
export class ReportsOptionsComponent implements OnInit {
  @Input() cards: { title: string; description: string; method: string }[] = [];
  rechargeFunction: Function;
  selectedValue: string;

  constructor(
    private modalService: ModalService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {}
  ngOnInit() {}

  switchMethod(method: string) {
    console.log("Método seleccionado:", method);
    switch (method) {
      case "distribucionMensual":
        this.openDistribucionMensualModal();
        break;
      case "bordereaux":
        this.openBordereauxModal();
        break;
      default:
        console.warn("Método no reconocido:", method);
        break;
    }
  }

  openDistribucionMensualModal() {
    const onClose = {
      next: (result: any) => {},
    };

    this.modalService.openDialog({
      component: DistribucionMensualModalComponent,
      title: "Distribución Mensual",
      element: {},
      actionClose: onClose,
    });
  }

  openBordereauxModal() {
    const onClose = {
      next: (result: any) => {},
    };

    this.modalService.openDialog({
      component: BordereauxModalComponent,
      title: "Distribución Manual (Bordereaux)",
      element: {},
      actionClose: onClose,
    });
  }
}
