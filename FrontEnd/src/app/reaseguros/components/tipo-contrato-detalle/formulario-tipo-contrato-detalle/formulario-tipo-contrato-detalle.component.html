<!--<button-back class="ml-2"></button-back>-->
<div class="page_title text-center">
  <h1>Configuracion de tipo de contrato</h1>
  <mat-radio-group
    [(ngModel)]="groupRadioButtons"
    (change)="selectionChange()"
    [disabled]="disableSelection"
  >
    <mat-radio-button class="p-2" value="Retencion">
      Retencion
    </mat-radio-button>
    <mat-radio-button class="p-2" value="Facultativo">
      Facultativo
    </mat-radio-button>
    <mat-radio-button class="p-2" value="Tent Plan">
      Tent Plan
    </mat-radio-button>
  </mat-radio-group>
</div>
<div *ngIf="groupRadioButtons === 'Retencion'">
  <ng-container *ngTemplateOutlet="retencion"></ng-container>
</div>
<div *ngIf="groupRadioButtons === 'Facultativo'">
  <ng-container *ngTemplateOutlet="facultativo"></ng-container>
</div>
<div *ngIf="groupRadioButtons === 'Tent Plan'">
  <ng-container *ngTemplateOutlet="tentPlan"></ng-container>
</div>
<div *ngIf="!groupRadioButtons">
  <h1 class="text-center">Seleccione un tipo de contrato</h1>
</div>

<ng-template #retencion>
  <form [formGroup]="formularioTipoContratoDetalle" class="m-4">
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>tipo de contrato</mat-label>
        <mat-select
          formControlName="tipo_contrato_id"
          name="tipo_contrato_id"
          disabled
        >
          <mat-option
            *ngFor="let tipoContrato of tipoContrato$ | async"
            [value]="tipoContrato.id"
            >{{ tipoContrato.nombre }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Capacidad</mat-label>
        <input matInput formControlName="capacidad" type="number" />
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Porcentaje retencion</mat-label>
        <input matInput formControlName="porcentaje_retencion" type="number" />
      </mat-form-field>
    </div>
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>Observaciones</mat-label>
        <textarea
          matInput
          formControlName="observaciones"
          type="text"
          sanitizeUppercase
        ></textarea>
      </mat-form-field>
    </div>
    <div class="d-flex justify-content-end m-2">
      <button
        mat-flat-button
        color="primary"
        (click)="save()"
        [disabled]="!formularioTipoContratoDetalle.valid"
      >
        <mat-icon>save</mat-icon>
      </button>
    </div>
  </form>
</ng-template>

<ng-template #facultativo>
  <form [formGroup]="formularioTipoContratoDetalle" class="m-4">
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>tipo de contrato</mat-label>
        <mat-select
          formControlName="tipo_contrato_id"
          name="tipo_contrato_id"
          disabled
        >
          <mat-option
            *ngFor="let tipoContrato of tipoContrato$ | async"
            [value]="tipoContrato.id"
            >{{ tipoContrato.nombre }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Capacidad</mat-label>
        <input matInput formControlName="capacidad" type="number" />
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Tasa de reaseguro</mat-label>
        <input matInput formControlName="tasa_reaseguro" type="number" />
      </mat-form-field>
    </div>
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>Dias de cobertura</mat-label>
        <input matInput formControlName="dias_cobertura" type="number" />
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Dias de garantia</mat-label>
        <input matInput formControlName="dias_garantia" type="number" />
      </mat-form-field>
    </div>
    <div class="d-flex justify-content-end m-2">
      <button
        mat-flat-button
        color="primary"
        (click)="save()"
        [disabled]="!formularioTipoContratoDetalle.valid"
      >
        <mat-icon>save</mat-icon>
      </button>
    </div>
  </form>
</ng-template>

<ng-template #tentPlan>
  <form [formGroup]="formularioTipoContratoDetalle" class="m-4">
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>tipo de contrato</mat-label>
        <mat-select
          formControlName="tipo_contrato_id"
          name="tipo_contrato_id"
          disabled
        >
          <mat-option
            *ngFor="let tipoContrato of tipoContrato$ | async"
            [value]="tipoContrato.id"
            >{{ tipoContrato.nombre }}</mat-option
          >
        </mat-select>
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Capacidad</mat-label>
        <input matInput formControlName="capacidad" type="number" />
      </mat-form-field>
      <mat-form-field class="col" appearance="outline">
        <mat-label>Prioridad</mat-label>
        <input matInput formControlName="prioridad" type="number" />
      </mat-form-field>
    </div>
    <div class="row">
      <mat-form-field class="col" appearance="outline">
        <mat-label>Observaciones</mat-label>
        <textarea
          matInput
          formControlName="observaciones"
          type="text"
          sanitizeUppercase
        ></textarea>
      </mat-form-field>
    </div>
    <div class="d-flex justify-content-end m-2">
      <button
        mat-flat-button
        color="primary"
        (click)="save()"
        [disabled]="!formularioTipoContratoDetalle.valid"
      >
        <mat-icon>save</mat-icon>
      </button>
    </div>
  </form>
</ng-template>
