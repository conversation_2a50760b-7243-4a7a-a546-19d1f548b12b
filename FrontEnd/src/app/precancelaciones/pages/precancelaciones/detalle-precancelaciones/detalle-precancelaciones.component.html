<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <table class="table-detalle">
            <tr>
              <td>ID Aviso: {{ this.vwConfiguracionMorosidad.id_aviso }}</td>
              <td>Tipo Rango Morosidad: {{ this.vwConfiguracionMorosidad.tiporangomorosidad }}</td>
            </tr>
            <tr>
              <td>Rango Inicial: {{ this.vwConfiguracionMorosidad.rangoinicial }}</td>
              <td>Rango Final: {{ this.vwConfiguracionMorosidad.rangofinal }}</td>
            </tr>
            <tr>
              <td>Ind Cancela: {{ this.vwConfiguracionMorosidad.indcancelar }}</td>
              <td>Ind Agente: {{ this.vwConfiguracionMorosidad.indicadoragente }}</td>
            </tr>
            <tr>
              <td>Ind Asegurado: {{ this.vwConfiguracionMorosidad.indasegurado }}</td>
              <td>Tipo Riesgo: {{ this.vwConfiguracionMorosidad.tiporiesgo }}</td>
            </tr>
            <tr>
              <td>Tipo Agente: {{ this.vwConfiguracionMorosidad.tipoagente }}</td>
              <td>Medio de Pago: {{ this.vwConfiguracionMorosidad.MediosPago }}</td>
            </tr>
            <tr>
              <td>Tipo Cobrador: {{ this.vwConfiguracionMorosidad.Cobradores }}</td>
              <td></td>
            </tr>
        </table>     
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
            <div class="btn-back">
                <button mat-flat-button color="primary" (click)="onBack()">
                  Regresar<mat-icon>back</mat-icon>
                </button>
            </div>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="table-responsive mat-elevation-z8"
          perfectScrollbar
        >
          <!-- id_detalle Column -->
          <ng-container matColumnDef="id_detalle">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              ID Detalle
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.id_aviso_detalle }}
            </td>
          </ng-container>

          <!-- morosidad Column -->
          <ng-container matColumnDef="morosidad">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Morosidad</th>
            <td mat-cell *matCellDef="let element">{{ element.morosidad }}</td>
          </ng-container>

          <!-- idrequerimiento Column -->
          <ng-container matColumnDef="idrequerimiento">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Requerimiento
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.idrequerimiento }}
            </td>
          </ng-container>

          <!-- poliza Column -->
          <ng-container matColumnDef="poliza">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Póliza</th>
            <td mat-cell *matCellDef="let element">{{ element.poliza }}</td>
          </ng-container>

          <!-- id_intermediario Column -->
          <ng-container matColumnDef="id_intermediario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Intermediario
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.id_intermediario }}
            </td>
          </ng-container>

          <!-- email_corredor Column -->
          <ng-container matColumnDef="email_corredor">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Email Agente
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.email_corredor }}
            </td>
          </ng-container>

          <!-- cobrador Column -->
          <ng-container matColumnDef="cobrador">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Cobrador</th>
            <td mat-cell *matCellDef="let element">
              {{ element.cobrador }}
            </td>
          </ng-container>

          <!-- operacion Column -->
          <ng-container matColumnDef="operacion">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Operación</th>
            <td mat-cell *matCellDef="let element">{{ element.operacion }}</td>
          </ng-container>

          <!-- dias_vence Column -->
          <ng-container matColumnDef="dias_vence">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Días Vence
            </th>
            <td mat-cell *matCellDef="let element">{{ element.dias_vence }}</td>
          </ng-container>

          <!-- fecvenc Column -->
          <ng-container matColumnDef="fecvenc">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Fecha Vence
            </th>
            <td mat-cell *matCellDef="let element">{{ element.fecvenc }}</td>
          </ng-container>

          <!-- nopago Column -->
          <ng-container matColumnDef="nopago">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>No. Pago</th>
            <td mat-cell *matCellDef="let element">{{ element.nopago }}</td>
          </ng-container>

          <!-- email_asegurado Column -->
          <ng-container matColumnDef="email_asegurado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Email Asegurado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.email_asegurado }}
            </td>
          </ng-container>

          <!-- tel1 Column -->
          <ng-container matColumnDef="tel1">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tel 1
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.tel1 }}
            </td>
          </ng-container>

          <!-- tel2 Column -->
          <ng-container matColumnDef="tel2">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tel 2
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.tel2 }}
            </td>
          </ng-container>

          <!-- nombreaseurado Column -->
          <ng-container matColumnDef="nombreaseurado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Nombre Asegurado
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.nombreaseurado }}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
