import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ActivatedRoute } from "@angular/router";
import { BehaviorSubject } from "rxjs";
import { map, switchMap } from "rxjs/operators";
import { TableColumn } from "src/app/components/icore-table/table";
import {
  IReclamosRequisitos,
  IReclamosRequisitosPayload,
} from "src/app/interface";
import { formatDate } from "src/app/_helpers";
import Swal from "sweetalert2";
import { ReclamoFacade } from "../../+state/reclamos.facade";
import { ReclamosService } from "../../services/reclamos.service";

@Component({
  selector: "app-requisitos",
  templateUrl: "./requisitos.component.html",
  styleUrls: ["./requisitos.component.css"],
})
export class RequisitosComponent implements OnInit {
  pageTitle = "Requisitos reclamos";
  title$ = this.facade.loadTitle$.pipe(
    map((entity) =>
      entity.filter((e) => e.idreclamo.toString() === this.idreclamo)
    )
  );

  displayedColumns: TableColumn<IReclamosRequisitos>[] = [
    {
      columnDef: "id",
      header: "#",
      cell: (element: Record<string, any>) =>
        `${element["idreclamorequisito"]}`,
    },
    {
      columnDef: "codigo",
      header: "Codigo",
      cell: (element: Record<string, any>) => `${element["codigo"]}`,
    },
    {
      columnDef: "descreq",
      header: "Descripcion",
      cell: (element: Record<string, any>) => `${element["descripcion"]}`,
    },
    {
      columnDef: "observaciones",
      header: "Observaciones",
      cell: (element: Record<string, any>) => `${element["observaciones"]}`,
    },
    {
      columnDef: "fechasolicitud",
      header: "Fecha solicitud",
      cell: (element: Record<string, any>) => `${element["fechasolicitud"]}`,
    },
    {
      columnDef: "fecharecepcion",
      header: "Fecha recepcion",
      cell: (element: Record<string, any>) => `${element["fecharecepcion"]}`,
    },
    {
      columnDef: "actions",
      header: "",
      actions: [
        {
          color: "primary",
          icon: "edit",
          tooltipText: "Editar",
          onClick: ({ row }) =>
            this.onUpdate(row.idreclamorequisito.toString()),
        },
        {
          color: "accent",
          icon: "delete",
          tooltipText: "Eliminar",
          onClick: ({ row }) =>
            this.deleteRequisito(row.idreclamorequisito.toString()),
        },
      ],
    },
  ];

  idreclamo: string = "";
  idreclamoRequisito: string = "";
  textTooltip: string = "Crear";
  updating: boolean = false;

  formRequisito = new FormGroup({
    codigo: new FormControl(""),
    descripcion: new FormControl(""),
    fechasolicitud: new FormControl(""),
    fecharecepcion: new FormControl(""),
  });

  codigosRequisitos$ = this.reclamosService.getCodigoRequisitos();
  refreshData$ = new BehaviorSubject<void>(null);
  data$ = this._requisitosReclamos();

  constructor(
    private reclamosService: ReclamosService,
    private route: ActivatedRoute,
    private facade: ReclamoFacade,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe(
      (param) => (this.idreclamo = param.get("id"))
    );
    this.refreshData$.next();
  }

  submitRequisito() {
    if (!this.updating) {
      this.saveRequisito();
    } else {
      this.editRequisito();
    }
  }

  saveRequisito() {
    const payload: IReclamosRequisitosPayload = {
      ...this.formRequisito.value,
      fechasolicitud: formatDate(
        this.formRequisito.controls["fechasolicitud"].value
      ),
      fecharecepcion: formatDate(
        this.formRequisito.controls["fecharecepcion"].value
      ),
      idreclamo: this.idreclamo,
    };

    this.reclamosService.createRequisito(payload).subscribe({
      next: () => {
        this.snackBar.open("Guardado", "", {
          duration: 5 * 1000,
          horizontalPosition: "start",
        });
        this.formRequisito.reset();
        this.refreshData$.next();
      },
      error: (err) => {
        Swal.fire("Error", "error guardando el requisito", "error");
      },
    });
  }

  deleteRequisito(idreclamorequisito: string) {
    Swal.fire({
      title: "¿Desea eliminar el requisito?",
      text: "Esta accion no se puede revertir.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Si, eliminar",
      cancelButtonText: "Cancelar",
    }).then((result) => {
      if (result.isConfirmed) {
        this.reclamosService.deleteRequisito(idreclamorequisito).subscribe({
          next: () => {
            this.snackBar.open("Eliminado", "", {
              duration: 3 * 1000,
              horizontalPosition: "start",
            });
            this.refreshData$.next();
          },
          error: (err) => {
            console.error(err);
            Swal.fire("Error", "error al eliminar", "error");
          },
        });
      }
    });
  }

  onUpdate(idreclamorequisito: string) {
    this.idreclamoRequisito = idreclamorequisito;
    this.reclamosService.getRequisitoById(idreclamorequisito).subscribe({
      next: (requisito) => {
        this.formRequisito.setValue({
          codigo: requisito.codigo,
          descripcion: requisito.observaciones,
          fechasolicitud: requisito.fechasolicitud,
          fecharecepcion: requisito.fecharecepcion,
        });
        this.updating = true;
        this.textTooltip = "Editar";
      },
    });
  }

  editRequisito() {
    const payload = {
      ...this.formRequisito.value,
      idreclamo: this.idreclamo,
    };
    this.reclamosService
      .updateRequisito(payload, this.idreclamoRequisito)
      .subscribe({
        next: () => {
          this.snackBar.open("Editado", "", {
            duration: 5 * 1000,
            horizontalPosition: "start",
          });
          this.formRequisito.reset();
          this.refreshData$.next();
          this.updating = false;
          this.textTooltip = "Crear";
        },
        error: () => {
          Swal.fire("Error", "error al actualizar", "error");
        },
      });
  }

  private _requisitosReclamos() {
    return this.refreshData$.pipe(
      switchMap((_) =>
        this.reclamosService.getRequisitosReclamos(this.idreclamo)
      )
    );
  }
}
