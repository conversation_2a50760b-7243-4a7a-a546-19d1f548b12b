import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { map } from "rxjs/operators";
import { environment } from "src/environments/environment";

@Injectable({
  providedIn: "root",
})
export class DistribucionManualPolizaService {
  private readonly urlBase = environment.apiUrlv1 + "/reaseguro";
  private readonly urlBase2 = environment.apiUrl;

  constructor(private readonly http: HttpClient) {}

  getData = () => this.getPolizas();

  getPolizas() {
    return this.http
      .get(`${this.urlBase}/tipo-contrato-detalle-poliza/dist-manual/polizas`)
      .pipe(
        map((res: { data: any }) => {
          if (res.data) return res.data;
        }),
      );
  }

  slipDistribucion(poliza_id: string, certificado: string, seccion: string) {
    return this.http
      .get(
        `${this.urlBase2}/reaseguro/dist-manual/slip/${poliza_id}/${certificado}/${seccion}`,
        {
          headers: { "Content-Type": "application/pdf" },
        },
      )
      .pipe(map((res: any) => res.encrypt));
  }
}
