<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title pt-2">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>
        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()" matTooltip="Nuevo">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          class="mat-elevation-z8"
        >
          <!-- idplanilla Column -->
          <ng-container matColumnDef="idplanilla">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>ID Planilla</th>
            <td mat-cell *matCellDef="let element">
              {{ element.idplanilla }}
            </td>
          </ng-container>

          <!-- estado Column -->
          <ng-container matColumnDef="estado">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Estado</th>
            <td mat-cell *matCellDef="let element">{{ element.stsplani }}</td>
          </ng-container>

          <!-- fecha Column -->
          <ng-container matColumnDef="fecha">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Fecha</th>
            <td mat-cell *matCellDef="let element">
              {{ element.fecstsplani }}
            </td>
          </ng-container>

          <!-- fecha_ingreso Column -->
          <ng-container matColumnDef="fecha_ingreso">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Fecha Ingreso
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.fecingplani }}
            </td>
          </ng-container>

          <!-- moneda Column -->
          <ng-container matColumnDef="moneda">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Moneda
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.codmoneda }}
            </td>
          </ng-container>

          <!-- tipo Column -->
          <ng-container matColumnDef="tipo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Tipo
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.tipoplani }}
            </td>
          </ng-container>

          <!-- codigo Column -->
          <ng-container matColumnDef="codigo">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Código
            </th>
            <td mat-cell *matCellDef="let element">
              {{ element.codigo }}
            </td>
          </ng-container>

          <!-- modo_pago Column -->
          <ng-container matColumnDef="modo_pago">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Modo Pago
            </th>
            <td mat-cell *matCellDef="let element">{{ element.modpagplani }}</td>
          </ng-container>

          <!-- usuario Column -->
          <ng-container matColumnDef="usuario">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Usuario
            </th>
            <td mat-cell *matCellDef="let element">{{ element.usuario }}</td>
          </ng-container>

          <!-- Action Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
            <td mat-cell *matCellDef="let element">
              <button
                mat-raised-button
                color="accent"
                (click)="onDetail(element)"
                matTooltip="Detalle"
                matTooltipClass="tooltip-red"
              >
                <mat-icon>subject</mat-icon>
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="primary"
                (click)="onProcess(element)"
                  [disabled]="element.stsplani === 'COB' || element.stsplani==='CER'"
                matTooltip="Procesar"
                matTooltipClass="tooltip-red"
              >
                <mat-icon>settings</mat-icon> 
              </button>
              &nbsp;
              <button
                mat-flat-button
                class="bg-danger" 
                (click)="onAnular(element)"
                   [disabled]="element.stsplani === 'VAL' || element.stsplani==='COB'"
                matTooltip="Anular"
                matTooltipClass="tooltip-red"
              >
               <mat-icon>block</mat-icon> 
              </button>
              &nbsp;
              <button
                mat-flat-button
                color="primary"
                matTooltip="Medio pago"
                (click)="openMedioPago(element)"
              >
               <mat-icon>attach_money</mat-icon> 
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
