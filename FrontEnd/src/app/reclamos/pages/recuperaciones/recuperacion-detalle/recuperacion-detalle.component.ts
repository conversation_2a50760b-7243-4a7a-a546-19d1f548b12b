import { Component, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { MatTableDataSource } from '@angular/material/table';
import { ActivatedRoute, Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { RecuperacionesService } from 'src/app/reclamos/services/recuperaciones.service';
import { UserService } from 'src/app/_services/user.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-recuperacion-detalle',
  templateUrl: './recuperacion-detalle.component.html',
  styleUrls: ['./recuperacion-detalle.component.css']
})
export class RecuperacionDetalleComponent implements OnInit {

  public identity: string;
  public page_title: string;
  public status: string;
  public dataSource: any;
  public idRecuperacion: number;
  public dataRecuperacion: any;
  public dataAcreencias: any[] = [];

  tiempoTranscurrido = Date.now();
  actual = new Date(this.tiempoTranscurrido);
  fechaActual =
    this.actual.toLocaleDateString().split("/")[2] +
    "-" +
    this.actual.toLocaleDateString().split("/")[1] +
    "-" +
    this.actual.toLocaleDateString().split("/")[0];
  
  checked: boolean = false;
  
  displayedColumns: string[] = ["fechabitacora", "descripcion", "actions"];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;

  constructor(private router: Router,
    private route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private ngxSpinnerService: NgxSpinnerService,
    private recuperacionesService: RecuperacionesService,
    private _userService: UserService) { 
      this.page_title = "Detalle Recuperacion";
      this.identity = this._userService.getIdentity();
  }

  public form = this.formBuilder.group(
    {
      idbitacora_manual: [""],
      usuario: [""],
      descripcion: ["", Validators.required],
      fechabitacora: [this.fechaActual, Validators.required],
    },
    { updateOn: "change" }
  );

  

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.idRecuperacion = params["id"];
      this.getRecuperacionById(this.idRecuperacion);
      this.getBitacoraManual(this.idRecuperacion);
    });
  }

  getRecuperacionById(registro: number){
    this.ngxSpinnerService.show();
    this.recuperacionesService.getRecuperacionById(registro).subscribe(
      (response) => {
        this.dataRecuperacion = response.data;
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  onGenerarAcreencia(){
    this.ngxSpinnerService.show();
    this.recuperacionesService.getRecuperacionAcreencia(this.idRecuperacion).subscribe(
      (response) => {
        this.ngxSpinnerService.hide();
        this.getBitacoraManual(this.idRecuperacion);
        this.onGetAcreencias();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  onGetAcreencias(){
    this.ngxSpinnerService.show();
    this.recuperacionesService.getRecuperacionAcreencia(this.idRecuperacion).subscribe(
      (response) => {
        this.dataAcreencias = response.data ?? [];
        console.log(this.dataAcreencias);
        this.ngxSpinnerService.hide();
      },
      (error) => {
        console.log(<any>error);
        this.ngxSpinnerService.hide();
      }
    );
  }

  getBitacoraManual(id) {
    this.recuperacionesService.postBitacoraManual(id).subscribe(
      (response) => {
        this.dataSource = new MatTableDataSource<any>(
          response.data ?? []
        );
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  onSaveForm(registro: any) {
    this.ngxSpinnerService.show();
    if (registro.idbitacora_manual) {
      registro.idetracking = this.idRecuperacion;
      registro.usuario = this.identity;
      registro.replicar = this.checked;
      this.recuperacionesService.createBitacora(registro).subscribe(
        (response) => {
          if (response.status == "success") {
            this.status = response.status;
            Swal.fire(
              "Actualización de Datos",
              "La bitácora se ha modificado correctamente",
              "success"
            );
            this.getBitacoraManual(this.idRecuperacion);
            this.checked = false;
            this.form.patchValue({
              idbitacora_manual: "",
              descripcion: "",
            });
          } else {
            this.status = "error";
            Swal.fire(
              "Actualización de la bitácora",
              "La bitácora no se ha modificado correctamente",
              "error"
            );
          }
          this.ngxSpinnerService.hide();
        },
        (error) => {
          console.log("error", <any>error);
          this.status = "error";
          Swal.fire(
            "Actualización de la bitácora",
            "La bitácora  se ha modificado correctamente",
            "error"
          );
          this.ngxSpinnerService.hide();
        }
      );
    } else {
      registro.idetracking = this.idRecuperacion;
      registro.usuario = this.identity;
      registro.replicar = this.checked;
      this.recuperacionesService.createBitacora(registro).subscribe(
        (response) => {
          if (response.status == "success") {
            this.status = response.status;
            Swal.fire(
              "Nuevo registro",
              "La bitácora se ha creado correctamente",
              "success"
            );
            this.getBitacoraManual(this.idRecuperacion);
            this.checked = false;
            this.form.patchValue({
              idbitacora_manual: "",
              descripcion: "",
            });
          } else {
            this.status = "error";
            Swal.fire(
              "Nuevo registro",
              "La bitácora no se ha creado correctamente",
              "error"
            );
          }
          this.ngxSpinnerService.hide();
        },
        (error) => {
          console.log("error", <any>error);
          this.status = "error";
          Swal.fire(
            "Nuevo registro",
            "La bitácora no se ha creado correctamente",
            "error"
          );
          this.ngxSpinnerService.hide();
        }
      );
    }
  }

  onEdit(element: any) {
    this.form.patchValue({
      idbitacora_manual: element.idbitacora_manual,
      descripcion: element.descripcion,
    });
  }

  onClear() {
    this.checked = false;
    this.form.patchValue({
      idbitacora_manual: "",
      descripcion: "",
    });
  }

  onDelete(registro: any) {
    if (registro.idbitacora_manual) {
      this.ngxSpinnerService.show();
      this.recuperacionesService.deleteBitacora(registro).subscribe(
        (response) => {
          if (response.status == "success") {
            this.status = response.status;
            Swal.fire(
              "Eliminar la bitácora",
              "La bitácora se ha eliminado correctamente",
              "success"
            );
            this.getBitacoraManual(this.idRecuperacion);
            this.checked = false;
            this.form.patchValue({
              idbitacora_manual: "",
              descripcion: "",
            });
          } else {
            this.status = "error";
            Swal.fire(
              "Eliminar la bitácora",
              "La bitácora no se ha eliminado correctamente",
              "error"
            );
          }
          this.ngxSpinnerService.hide();
        },
        (error) => {
          console.log("error", <any>error);
          this.status = "error";
          Swal.fire(
            "Eliminar la bitácora",
            "La bitácora no se ha eliminado correctamente",
            "error"
          );
          this.ngxSpinnerService.hide();
        }
      );
    }
  }

  close() {
    this.router.navigate(['reclamos/recuperaciones']);
  }

}
