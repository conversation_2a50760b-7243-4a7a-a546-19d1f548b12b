import { Component, Inject, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { tap } from "rxjs/operators";
import { IReaseguradores } from "src/app/reaseguros/interfaces/reaseguradores.interface";
import { FichaContactosService } from "src/app/reaseguros/services/ficha-contactos.service";
import { ReaseguradoresService } from "src/app/reaseguros/services/reaseguradores.service";
import Swal from "sweetalert2";

@Component({
  selector: "app-formulario-reasegurador",
  templateUrl: "./formulario-reasegurador.component.html",
  styleUrls: ["./formulario-reasegurador.component.css"],
})
export class FormularioReaseguradorComponent implements OnInit {
  formularioReasegurador = new FormGroup({
    ficha_contacto_id: new FormControl("", Validators.required),
    estado: new FormControl("ACT", Validators.required),
    tasa: new FormControl("", Validators.required),
    isr: new FormControl("", Validators.required),
    comision: new FormControl("", Validators.required),
  });

  estados = [
    { value: "ACT", label: "Activo" },
    { value: "INA", label: "Inactivo" },
  ];

  contactos$ = this.getData();

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: { element: { data: IReaseguradores } },
    private fichaContactosService: FichaContactosService,
    private reaseguradoresService: ReaseguradoresService,
    private dialogRef: MatDialogRef<FormularioReaseguradorComponent>,
    private router: Router,
  ) {}

  ngOnInit() {
    if (this.data.element.data) {
      this.patch();
    }
  }

  getData() {
    return this.fichaContactosService.getContactos().pipe(
      tap((res) => {
        if (res.length === 0) {
          Swal.fire({
            title: "No hay contactos",
            text: "Para continuar, debe crear un contacto primero",
            icon: "error",
            confirmButtonText: "Ir a crear",
            allowOutsideClick: false,
          }).then((res) => {
            if (res.isConfirmed) {
              this.dialogRef.close();
              this.router.navigate(["reaseguros/ficha-contacto"]);
            }
          });
        }
      }),
    );
  }

  patch() {
    this.formularioReasegurador.patchValue({
      ficha_contacto_id: this.data.element.data.ficha_contacto_id,
      estado: this.data.element.data.estado,
      tasa: this.data.element.data.tasa,
      isr: this.data.element.data.isr,
      comision: this.data.element.data.comision,
    });
  }

  create() {
    this.reaseguradoresService
      .createReasegurador(this.formularioReasegurador.value)
      .subscribe(() => {
        Swal.fire("Reasegurador creado", "", "success");
        this.dialogRef.close();
      });
  }

  update() {
    this.reaseguradoresService
      .updateReasegurador(
        this.data.element.data.id,
        this.formularioReasegurador.value,
      )
      .subscribe(() => {
        Swal.fire("Reasegurador actualizado", "", "success");
        this.dialogRef.close();
      });
  }

  createOrUpdate() {
    if (this.data.element.data) {
      this.update();
    } else {
      this.create();
    }
  }
}
