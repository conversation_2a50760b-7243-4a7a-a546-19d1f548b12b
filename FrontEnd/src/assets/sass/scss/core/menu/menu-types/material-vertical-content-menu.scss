/*=========================================================================================
	File Name: vertical-content-menu.scss
	Description: A vertical style content menu with expand and collops support. It support
	light & dark version, filpped layout, right side icons, native scroll and borders menu
	item seperation.
	----------------------------------------------------------------------------------------
	Item Name: Item Name: Modern Admin -Angular 11+ Bootstrap 5 Admin Dashboard Template
	Version: 3.0
	Author: PIXINVENT
	Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/

// Core variables and mixins
@import "../../../bootstrap/functions";
@import "../../../bootstrap/mixins";
@import "../../../bootstrap-extended/mixins";

// Core variables and mixins overrides
@import "../../variables/material-variables";
@import "../../../bootstrap/variables";

@import "../../variables/components-variables";

// Import first main menu mixin
@import "../../mixins/main-menu-mixin";


//  Vertical Content Menu
//=========================
.vertical-content-menu {

    //Vertical menu [Expanded]
    &.menu-expanded {

        //Navbar expanded
        .navbar {
            .navbar-header {
                @include menu-navbar-width($content-menu-expanded-width);
            }

            &.navbar-brand-center {
                .navbar-header {
                    @include menu-navbar-width(auto);
                }

                .navbar-container {
                    @include main-menu-width(0);
                }
            }

            .navbar-container {
                @include main-menu-width($content-menu-expanded-width);
            }
        }

        // Main menu expanded
        .main-menu {
            width: $content-menu-expanded-width;
            transition: 300ms ease all;
            backface-visibility: hidden;
            top: inherit;

            .navigation {
                .navigation-header {

                    .ft-minus,
                    .nav-menu-icon {
                        display: none;
                    }
                }

                >li {
                    >a {
                        >i {
                            margin-right: 12px;
                            float: left;

                            &:before {
                                font-size: 1.6rem;
                            }
                        }

                        >span {
                            display: inline-block;

                            &.tag {
                                position: absolute;
                                right: 20px;
                            }
                        }
                    }
                }

                li {
                    @include vertical-menu-has-sub-arrow(1rem);
                }
            }

            .main-menu-footer {
                width: $content-menu-expanded-width;
            }
        }

        //Content expanded
        .content-body {
            @include main-menu-width($content-menu-expanded-width + 28);
            transition: 300ms ease all;
        }

        // Flipped menu expanded
        &.menu-flipped {

            //Content expanded flipped
            .content-body {
                @include vertical-flipped-menu($content-menu-expanded-width + 28);
                transition: 300ms ease all;
            }

            @include media-breakpoint-up(sm) {
                &:not(.boxed-layout) .main-menu {
                    right: 20px;
                }

                &.boxed-layout {
                    .main-menu {
                        float: right;
                        position: relative;
                    }
                }
            }

            //Navbar expanded flipped
            .navbar {
                .navbar-header {
                    float: right;
                }

                .navbar-container {
                    @include vertical-flipped-menu($content-menu-expanded-width);
                }
            }
        }
    }

    //Vertical menu [Collapsed]
    &.menu-collapsed {

        //Navbar collapsed
        .navbar {
            .navbar-header {
                @include menu-navbar-width($content-menu-expanded-width);
            }

            &.navbar-brand-center {
                .navbar-header {
                    @include menu-navbar-width(auto);
                }

                .navbar-container {
                    @include main-menu-width(0);
                }
            }

            .navbar-container {
                @include main-menu-width($content-menu-expanded-width);
            }
        }

        // Main menu collapsed
        .main-menu {
            width: $content-menu-collapsed-width;
            transform: translateZ(-160px) translateX(-160px);
            transform: translate3d(0, 0, 0);
            transition: 300ms ease all;
            top: inherit;

            .main-menu-header .user-content {
                padding: 20px 10px;
            }

            //hide header contect area & footer on collopse
            .main-menu-footer,
            .main-menu-header .media-body .media-heading,
            .main-menu-header .media-body .text-muted,
            .main-menu-header .media-right {
                display: none;
            }

            .main-menu-header .media-body {
                opacity: 0;
            }

            //content menu mixin
            @include menu-content($content-menu-expanded-width, $content-menu-collapsed-width, $menu-light-color, $menu-light-bg);

            //Navigation collapsed
            .navigation {
                overflow: visible;

                >li.navigation-header {
                    padding: 30px 20px 8px 20px;

                    .ft-minus {
                        display: block;
                        padding: 12px 0px;
                        text-align: center;
                        font-size: 1.6rem;
                    }

                    span {
                        display: none;
                    }
                }

                >li {
                    >a {
                        padding: 8px 20px;
                        text-align: center;

                        >span {
                            visibility: hidden;
                            opacity: 0;
                            position: absolute;
                            top: 0;
                            right: -($content-menu-expanded-width);
                            width: $content-menu-expanded-width;
                            font-weight: 600;
                            color: #fff;
                            text-align: left;
                            background-color: $primary;
                            border-color: $primary;
                            padding: 11px 20px;
                        }

                        >i {
                            margin-right: 0;
                            font-size: 1.6rem;
                            visibility: visible;
                        }
                    }

                    //hide inner span and ul for the collapsed menu
                    // &.hover{
                    &>ul {
                        display: none;
                    }

                    >a {
                        >span {
                            display: none;
                        }
                    }

                    // }
                }
            }

            .mTSWrapper {
                overflow: visible;
            }
        }

        //Content expanded
        .content-body {
            @include main-menu-width($content-menu-collapsed-width + 25);
            transition: 300ms ease all;
        }

        // Flipped menu collapsed
        &.menu-flipped {

            //Content collapsed flipped
            .content-body {
                margin-left: 0px;
                margin-right: $content-menu-collapsed-width + 25;
                transition: 300ms ease all;
            }

            @include media-breakpoint-up(sm) {
                &:not(.boxed-layout) .main-menu {
                    right: 1.5rem;

                    span.menu-title {
                        right: $content-menu-collapsed-width;
                    }

                    ul.menu-content {
                        right: $content-menu-collapsed-width;
                        left: inherit;
                    }
                }

                &.boxed-layout {
                    .main-menu {
                        float: right;
                        position: relative;
                    }

                    span.menu-title {
                        right: $content-menu-collapsed-width;
                    }

                    ul.menu-content {
                        right: $content-menu-collapsed-width;
                        left: inherit;
                    }
                }
            }

            //Navbar collapsed flipped
            .navbar {
                .navbar-header {
                    float: right;
                }

                .navbar-container {
                    // expanded width for this menu
                    @include vertical-flipped-menu($content-menu-expanded-width);
                }
            }
        }
    }

    .navbar-brand-center {

        .content,
        .footer {
            @include main-menu-width(0);
        }
    }

    //Box layout specific
    &.boxed-layout {
        &.menu-flipped {
            .main-men {
                float: right;
                position: relative;
            }
        }
    }
}

// Collapsed menu RTL
[data-textdirection="rtl"] body.vertical-layout {
    &.vertical-content-menu.menu-collapsed {
        .main-menu {
            .navigation {
                >li>a {
                    // padding: 12px 22px !important;
                    padding: 8px 12px 20px !important;
                }

                >li.navigation-header .ft-minus {
                    padding: 12px 0px;
                }
            }
        }
    }
}

// Initially menu & content width for lg and up screen
@include media-breakpoint-up(lg) {
    body {
        &.vertical-content-menu {
            .main-menu {
                width: $content-menu-expanded-width;
            }

            .navbar {
                .navbar-header {
                    width: $content-menu-expanded-width;
                }
            }

            .content-body {
                @include main-menu-width($content-menu-expanded-width + 28);
                transition: 300ms ease all;
            }
        }
    }
}

// Initially menu & content width for md and down screen
@include media-breakpoint-down(md) {
    body {
        &.vertical-content-menu {
            .main-menu {
                width: $content-menu-collapsed-width;
            }

            .navbar {
                .navbar-header {
                    width: $content-menu-collapsed-width;
                }
            }

            .content-body {
                @include main-menu-width($content-menu-collapsed-width + 25);
                transition: 300ms ease all;
            }
        }
        //box-layout
        &.vertical-overlay-menu {
             &[data-menu="vertical-content-menu"] {
                &.boxed-layout{
                   &.menu-open {
                      .main-menu {
                         transform: translate3d($content-menu-collapsed-width + 10px, 0, 0) !important;
                      }
                   }
                }
            }
        }
    }
}

// Initially menu & content width for sm and down screen
@include media-breakpoint-down(sm) {
    body {
        &.vertical-content-menu {
            .content-body {
                margin-left: 0px !important;
            }
        }
    }

    html.loading .main-menu {
        opacity: 0;
        transform: translate3d(0px, 0, 0);
    }
}

// For Material Content Menu

.material-vertical-layout {

    &.menu-collapsed {
        .main-menu.menu-light, .main-menu.menu-static {
            transition: none !important;

            .user-profile {
                .user-img {
                    width: 75% !important;
                }

                .dropdown-toggle {
                    margin-left: 0 !important;
                }

                div.text-light,
                .user-name {
                    white-space: nowrap;
                    display: none;
                }
            }

            &.expanded {
                .user-profile {
                    .user-img {
                        width: 25% !important;
                    }

                    div.text-light,
                    .name-wrapper {
                        white-space: nowrap;
                        display: block;
                    }

                    .user-name {
                        white-space: nowrap;
                        display: inline-block;
                    }
                }
            }

        }
    }
}

// Layout Boxed for down to sm screen
@media screen and (max-width : 575px){
   // main-menu for Box-layout
   body.vertical-overlay-menu {
        &[data-menu="vertical-content-menu"] {
            &.boxed-layout{
                &.menu-open {
                    .main-menu {
                        transform: translate3d($content-menu-collapsed-width, 0, 0) !important;
                    }
                }
            }
        }
    }
}


// For Material Icons in main menu
@import "material-user-profile";

.material-vertical-layout {
    &.vertical-content-menu {
        .main-menu .navigation>li>a i {
            position: relative;
            top: 0.7rem;

            &.material-icons {
                font-size: 1.6rem;
            }
        }
        &.menu-collapsed{
           .main-menu .navigation>li>a i {
                position: relative;
                top: -0.3rem;
                &.material-icons {
                    font-size: 1.6rem;
                }
            }
        }
    }
}
@media only screen and (max-width: 768px){
    .material-vertical-layout {
        &.vertical-content-menu {
            .main-menu .navigation>li>a i {
                &.material-icons {
                    font-size: 1.1rem;
                }
            }
        }
    }
}
@media only screen and (max-width: 1100px) and (min-width: 992px) {
    .material-vertical-layout {
        &.vertical-content-menu {
            .header-navbar{
                .navbar-container{
                    ul.nav{
                        a.nav-link-expand{
                            padding: 1.7rem 0.3rem;
                        }
                        .mega-dropdown {
                            .dropdown-toggle{
                                padding: 1.9rem 0.5rem;
                            }
                        }
                    }
                }
            }
        }
    }
}

@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
   body.vertical-layout {
      &.vertical-content-menu{
         &.menu-collapsed{
            .main-menu{
                .main-menu-content{
                  > span.menu-title, a.menu-title{
                     right: auto;
                     left: $menu-collapsed-width;
                     margin-left: 2.75rem;
                     margin-top: 2.2rem;
                  }
                }
                .main-menu-content{
                  > ul.menu-content{
                     left: $menu-collapsed-width;
                     margin-left: 2.75rem;
                     margin-top: 2.2rem;
                  }
               }
            }
         }
      }
   }
}

// Import vertical-overlay-menu.scss for small screen support
@import "material-vertical-overlay-menu.scss";
