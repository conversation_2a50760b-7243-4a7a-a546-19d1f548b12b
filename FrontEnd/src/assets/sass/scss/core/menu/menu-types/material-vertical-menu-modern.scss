/*=========================================================================================
    File Name: vertical-menu-modern.scss
    Description: A calssic vertical modern style menu with expand and collops support. It support
    light & dark version, filpped layout, right side icons, native scroll and borders menu
    item seperation.
    ----------------------------------------------------------------------------------------
    Item Name: Item Name: Modern Admin -Angular 11+ Bootstrap 5 Admin Dashboard Template
    Version: 3.0
    Author: PIXINVENT
    Author URL: http://www.themeforest.net/user/pixinvent
==========================================================================================*/

// Core variables and mixins
@import "../../../bootstrap/functions";
@import "../../../bootstrap/mixins";
@import "../../../bootstrap-extended/mixins";

// Core variables and mixins overrides
@import "../../variables/material-variables";
@import "../../../bootstrap/variables";

@import "../../variables/components-variables";

// Import first main menu mixin
@import "../../mixins/main-menu-mixin";

//  Vertical menu
//=========================
body.vertical-layout {
   &.vertical-menu-modern {
      .main-menu {
         .navigation {
            > li > a > i {
               font-size: 1.6rem;
            }
         }
      }
      .toggle-icon {
         margin-right: 1rem;
      }
      // Flipped menu expanded
      &.menu-flipped {
         @include media-breakpoint-up(sm) {
            .main-menu {
               right: 0;
            }
         }
      }

      .navbar {
         .navbar-brand {
            white-space: nowrap;
         }

         .navbar-header {
            .nav-toggle {
               position: absolute;
               right: 0;
            }
         }
      }

      .main-menu {
         transition: 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
         transform: translate3d(0, 0, 0);
         backface-visibility: hidden;

         li {
            a {
               span {
                  animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
               }
            }
         }
      }

      //Vertical menu [Expanded]
      &.menu-expanded {
         //Navbar expanded
         .navbar {
            .navbar-header {
               @include menu-navbar-width($menu-expanded-width);
            }

            &.navbar-brand-center {
               .navbar-header {
                  @include menu-navbar-width(auto);
               }
            }

            .navbar-container {
               @include main-menu-width($menu-expanded-width);
            }

            .navbar-brand {
               .brand-text {
                  animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
               }
            }
         }

         // Main menu expanded
         .main-menu {
            width: $menu-expanded-width;

            // main-menu-content height

            &.menu-fixed {
               height: calc(100% - #{$navbar-height});
               .main-menu-content {
                  height: calc(100% - 12rem) !important;
                  position: relative;
               }
            }

            .navigation {
               .navigation-header {
                  .ft-minus,
                  .nav-menu-icon {
                     display: none;
                  }
               }

               > li {
                  > a {
                     > i {
                        margin-right: 12px;
                        float: left;

                        &:before {
                           font-size: 1.6rem;
                        }
                     }

                     > span {
                        &.badge {
                           position: absolute;
                           right: 20px;
                        }
                     }
                  }
               }

               li {
                  @include vertical-menu-has-sub-arrow(1rem);
               }
            }

            .main-menu-footer {
               width: $menu-expanded-width;
            }
         }

         //Content expanded
         .content,
         .footer {
            @include main-menu-width($menu-expanded-width);
         }

         //Brand center content expanded
         .navbar-brand-center {
            .content,
            .footer {
               @include main-menu-width(0);
            }
         }
      }

      //Vertical menu [Collapsed]
      &.menu-collapsed {
         //Navbar collapsed
         .navbar {
            .navbar-header {
               @include menu-navbar-width($menu-collapsed-width);

               .modern-nav-toggle {
                  display: none;
               }

               &.expanded {
                  width: $menu-expanded-width;
                  z-index: 1000;

                  .modern-nav-toggle {
                     display: block;
                  }

                  .navbar-brand {
                     .brand-text {
                        animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
                     }
                  }
               }

               .navbar-brand {
                  .brand-text {
                     animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadeout;
                  }
               }
            }

            &.navbar-brand-center {
               .navbar-header {
                  @include menu-navbar-width(auto);
               }
               .navbar-brand {
                  .brand-text {
                     animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
                  }
               }
            }

            .navbar-brand {
               padding: 18px 0px;
            }

            .navbar-container {
               @include main-menu-width($menu-collapsed-width);
            }
         }

         // Main menu collapsed
         .main-menu {
            width: $menu-collapsed-width;
            transition: 300ms ease all;
            backface-visibility: hidden;
            transform: translate3d(0, 0, 0);

            &.menu-fixed {
               .main-menu-content {
                  height: calc(100% - 12rem) !important;
                  position: relative;
               }
            }

            .main-menu-footer {
               width: $menu-collapsed-width;
            }

            .main-menu-footer,
            .main-menu-header .media-body .media-heading,
            .main-menu-header .media-body .text-muted,
            .main-menu-header .media-right {
               display: none;
            }

            .main-menu-header .media-body {
               opacity: 0;
            }

            .user-content {
               padding: 20px 10px;
            }

            //Navigation collapsed
            .navigation {
               overflow: visible;

               > li.navigation-header {
                  .ft-minus {
                     display: block;
                     font-size: 1.2rem;
                  }

                  span {
                     display: none;
                  }
               }

               > li {
                  > a {
                     text-overflow: inherit;

                     > span {
                        animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadeout;

                        &.badge {
                           float: none !important;
                        }
                     }

                     i {
                        margin-right: 12px;
                        float: left;

                        &:before {
                           font-size: 1.6rem;
                        }
                     }
                  }
               }
            }

            .mTSWrapper {
               overflow: visible;
            }

            &.expanded {
               width: $menu-expanded-width;

               .navigation {
                  > li.navigation-header {
                     .ft-minus,
                     i {
                        display: none;
                     }

                     span {
                        display: block;
                     }
                  }

                  > li {
                     > a {
                        > span {
                           animation: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) 0s normal forwards 1 fadein;
                           &.badge {
                              position: absolute;
                              right: 20px;
                           }
                        }
                     }
                  }

                  li {
                     @include vertical-menu-has-sub-arrow(1rem);
                  }
               }
            }
         }

         //Brand center content collapsed
         .content,
         .footer {
            @include main-menu-width($menu-collapsed-width);
         }

         .navbar-brand-center {
            .content,
            .footer {
               @include main-menu-width(0);
            }
         }

         // Flipped menu collapsed
         &.menu-flipped {
            //Content collapsed flipped
            .content {
               @include vertical-flipped-menu($menu-collapsed-width);
            }

            @include media-breakpoint-up(sm) {
               .main-menu {
                  right: 0;

                  span.menu-title {
                     right: $menu-collapsed-width;
                  }

                  ul.menu-content {
                     right: $menu-collapsed-width;
                     left: inherit;
                  }
               }
            }

            //Navbar collapsed flipped
            .navbar {
               .navbar-header {
                  float: right;
               }

               .navbar-container {
                  @include vertical-flipped-menu($menu-collapsed-width);
               }
            }

            .footer {
               @include vertical-flipped-menu($menu-collapsed-width);
            }
         }
      }
   }
}

// Collapsed menu RTL
[data-textdirection="rtl"] body.vertical-layout {
   &.vertical-menu-modern.menu-collapsed {
      .main-menu {
         .navigation {
            > li > a {
               // padding: 12px 22px !important;
               padding: 14px 16px !important;
            }

            > li.navigation-header .ft-minus {
               padding: 12px 0px;
            }
         }
      }
   }
}

// Initially menu & content width for lg and up screen
@include media-breakpoint-up(lg) {
   body.vertical-layout {
      &.vertical-menu-modern {
         .main-menu {
            width: $menu-expanded-width;
            .navigation {
               li.navigation-header .ft-minus {
                  display: none;
               }
            }
         }
         .navbar {
            .navbar-header {
               width: $menu-expanded-width;
            }
         }
         .content,
         .footer {
            @include main-menu-width($menu-expanded-width);
         }
         &.menu-flipped {
            //Content expanded flipped
            .content {
               @include vertical-flipped-menu($menu-expanded-width);
            }

            //Navbar expanded flipped
            .navbar {
               .navbar-header {
                  float: right;
               }

               .navbar-container {
                  @include vertical-flipped-menu($menu-expanded-width);
               }
            }

            .footer {
               @include vertical-flipped-menu($menu-expanded-width);
            }
         }
      }
   }
}

// Initially menu & content width for md and down screen
@include media-breakpoint-down(md) {
   body.vertical-layout {
      &.vertical-menu-modern {
         .main-menu {
            width: 0;
         }
         .navbar {
            .navbar-header {
               width: 0;
            }
         }
         .content,
         .footer {
            @include main-menu-width(0);
         }
      }

      //Box-layout
      &.vertical-overlay-menu {
         &[data-menu="vertical-menu-modern"] {
             &.boxed-layout{
                &.menu-open {
                   .main-menu {
                        transform: translate3d(0, 0, 0) !important;
                     }
                 }
             }
         }
      }
   }
}

// Initially menu & content width for sm and down screen
@include media-breakpoint-down(sm) {
   body.vertical-layout {
      &.vertical-menu-modern {
         &.vertical-menu-modern {
            .main-menu {
               width: 0;
            }
            .navbar {
               .navbar-header {
                  width: 0;
               }
            }
            .content,
            .footer {
               @include main-menu-width(0);
            }
         }
         &.menu-expanded {
            .navbar .navbar-container {
               margin-left: 0px !important;
            }

            .content,
            .footer {
               margin-left: 0px !important;
            }
         }
      }
   }

   html.loading .main-menu {
      opacity: 0;
      transform: translate3d(0px, 0, 0);
   }
}

@keyframes fadein {
   from {
      opacity: 0;
   }

   to {
      opacity: 1;
   }
}

@keyframes fadeout {
   from {
      opacity: 1;
   }

   to {
      opacity: 0;
   }
}

// For Material Modern
@import "material-user-profile";

.material-vertical-layout {
   .main-menu.menu-dark {
      .user-profile {
         .user-info {
            background-color: rgba($color: $black, $alpha: 0.35);
         }
      }
   }

   &.menu-collapsed {
      .main-menu.menu-dark,
      .main-menu.menu-light {
         .user-profile {
            .user-img {
               width: 75% !important;
               transition: 300ms ease all;
            }

            .dropdown-toggle {
               margin-left: 0 !important;
            }

            div.text-light,
            .user-name {
               white-space: nowrap;
               display: none;
            }
         }

         &.expanded {
            .user-profile {
               .user-img {
                  width: 25% !important;
                  transition: 300ms ease all;
               }

               div.text-light,
               .name-wrapper {
                  white-space: nowrap;
                  display: block;
               }

               .user-name {
                  white-space: nowrap;
                  display: inline-block;
               }
            }
         }
      }
   }
}

//For Customizer
.material-vertical-layout {
   &.menu-collapsed {
      .main-menu.menu-light {
         transition: none !important;

         .user-profile {
            .user-img {
               width: 75% !important;
            }

            .dropdown-toggle {
               margin-left: 0 !important;
            }

            div.text-light {
               display: none;
               white-space: nowrap;
            }

            .user-name {
               display: none;
               white-space: nowrap;
            }
         }
      }
   }

   &.menu-expanded {
      div.text-light {
         white-space: nowrap;
      }

      .user-name {
         white-space: nowrap;
      }
   }
}

// For Material Icons in main menu

.material-vertical-layout {
   &.vertical-menu-modern {
      .main-menu .navigation > li > a i {
         position: relative;
         top: 0.5rem;

         &.material-icons {
            font-size: 1.5rem;
         }
      }
   }
}

// Import vertical-overlay-menu.scss for small screen support
@import "material-vertical-overlay-menu.scss";
