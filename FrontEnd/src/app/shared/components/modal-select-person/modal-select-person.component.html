<h2>Seleccione {{ data.element.title }}</h2>
<mat-form-field class="mt-2 w-100">
  <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
</mat-form-field>
<div *ngIf="data$ | async as datos">
  <icore-table
    [columns]="data.displayedColumns??defaultDisplayedColumns"
    [data]="datos.data ?? datos"
    [length]="datos.total || dadatosta.length"
    (setPage)="setPage($event)"
    [valueToSearch]="valueToSearch"
    (params)="paramsEmit($event)"
  >
    <h1>No hay datos para mostrar</h1>
  </icore-table>
</div>
