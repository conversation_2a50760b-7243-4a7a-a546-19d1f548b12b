.pagos-container {
  height: 270px;
  overflow: auto;
}

table {
  width: 100%;
}

tr.mat-footer-row {
  font-weight: bold;
  background-color: #d3cece /*#262525*/;
}

.mat-table-sticky {
  border-top: 1px solid #e0e0e0;
}

.card-payments {
  display: flex;
  gap: 10px;
  justify-content: center;
}

/*.mat-card-header {
  background-color: gold;
  padding: 5px;
}

.mat-card {
  background-color: rgb(235, 116, 237);
}*/

.monto {
  font-size: 1.25rem;
  font-weight: bold;
}

.title-payment {
  font-size: 1.23rem;
  font-weight: bold;
}

.wrapper-disable{
  opacity: 0.5;
  pointer-events:none;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}
.wrapper-disable *{
  pointer-events:none;
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}