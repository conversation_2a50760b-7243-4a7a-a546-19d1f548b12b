import { Component, OnInit, ViewChild, Input } from "@angular/core";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { UserService } from "../../../_services/user.service";
import { AcreenciaService } from "../acreencia.service";
import { DetalleAcreenciaService } from "./detalle_acreencia.service";
import { DetalleEntidadAcreedora } from "./detalle-entidad-acreedora";
import { EntidadAcreedora } from "../entidad-acreedora";

import { global } from "../../../_services/global";
import swal from "sweetalert2";

import { PageEvent } from "@angular/material/paginator";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { MatSort } from "@angular/material/sort";

import { MatDialog, MatDialogConfig } from "@angular/material/dialog";
import { FormularioDetalleEntidadComponent } from "./formulario/formulario-detalle-entidad.component";
import { Location } from "@angular/common";

@Component({
  selector: "app-detalle-entidad-acreedora",
  templateUrl: "./detalle-entidad-acreedora.component.html",
  styleUrls: ["../entidad-acreedora.component.css"],
  providers: [UserService, AcreenciaService, DetalleAcreenciaService],
})
export class DetalleEntidadAcreedoraComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public isDisabled: boolean = true;

  public dataSource = null;
  public datosAcreencia: EntidadAcreedora;
  public nombreCliente: string;
  public nombreIntermediario: string;

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  constructor(
    private _router: Router,
    private _route: ActivatedRoute,
    private _userService: UserService,
    private _acreenciaService: AcreenciaService,
    private _detalleAcreenciaService: DetalleAcreenciaService,

    private dialog: MatDialog,
    private _location: Location
  ) {
    this.page_title = "Detalle de la Acreencia";
    this.identity = this._userService.getIdentity();
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "id",
    "codclaingre",
    "codcptoacre",
    "desccptoacre",
    "mtodetacrelocal",
    "mtodetacremoneda",
    "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getDetalleAcreencias();
  }

  getDetalleAcreencias() {
    console.log("getDetalleAcreencias");
    let detalleAcreencia: any[] = [];
    let id: number;
    this._route.params.subscribe((params) => {
      id = +params["id"];
    });
    //Obtiene los valores de la acreencia
    this._acreenciaService.getAcreencia(id).subscribe(
      (response) => {
        if (response.status == "success") {
          this.datosAcreencia = response.acreencia;
          this.nombreCliente = response.acreencia.cliente.nombre
            ? response.acreencia.cliente.nombre
            : null;
          //this.nombreIntermediario = response.acreencia.intermediario.nombre ? response.acreencia.intermediario.nombre : null;

          if (response.acreencia.stsacre == "VAL") this.isDisabled = false;
          else this.isDisabled = true;
        } else {
          //this.status = 'error';
        }
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
    //Obtiene los valores del detalle de la acreencia
    this._detalleAcreenciaService.getDetalleAcreencia(id).subscribe(
      (response) => {
        if (response.status == "success") {
          response.acreencia.forEach((dato) => {
            detalleAcreencia.push({
              id: dato.id,
              numacre: dato.numacre,
              codclaingre: dato.codclaingre,
              codcptoacre: dato.codcptoacre,
              desccptoacre: dato.concepto_ingreso.desccptoacre,
              mtodetacrelocal: dato.mtodetacrelocal,
              mtodetacremoneda: dato.mtodetacremoneda,
            });
          });
        }
        this.dataSource = new MatTableDataSource<DetalleEntidadAcreedora>(
          detalleAcreencia
        );
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
      },
      (error) => {
        console.log(<any>error);
        //this.status = 'error';
      }
    );
  }

  onDelete(element) {
    //console.log('Eliminar', id);
    swal
      .fire({
        title: "¿Estas seguro?",
        text: "Una vez borrado no podrás recuperarlo!",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willDelete) => {
        if (willDelete) {
          this._detalleAcreenciaService.delete(element).subscribe(
            (response) => {
              //console.log('response', response);
              swal.fire({
                text: "Registro borrado exitosamente!",
                icon: "success",
              });
              this.getDetalleAcreencias();
            },
            (error) => {
              console.log(<any>error);
              //this.status = 'error';
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }

  onEdit(element) {
    console.log("onEdit");
    this.openDialogo(element, "edit");
  }

  onNew(element) {
    console.log("onNew");
    this.openDialogo(element, "new");
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  openDialogo(element: EntidadAcreedora = null, tipo: string): void {
    const dialogConfig = new MatDialogConfig();
    dialogConfig.data = {
      title:
        tipo == "new"
          ? `Nuevo Detalle Acreencia No.${element.id}`
          : `Modificacion de Datos de la Acreencia No. ${this.datosAcreencia.id}, Detalle No. ${element.id}`,
      acreencia: this.datosAcreencia,
      element: tipo == "new" ? null : element,
    };
    dialogConfig.autoFocus = true;
    dialogConfig.disableClose = true;
    dialogConfig.height = "600px";
    dialogConfig.width = "550px";

    const dialogRef = this.dialog.open(
      FormularioDetalleEntidadComponent,
      dialogConfig
    );
    dialogRef.afterClosed().subscribe((result) => {
      console.log(`Dialog result ${result}`);
      this.getDetalleAcreencias();
    });
  }

  onBack() {
    this._location.back();
  }

  onDisabledBtnActivated() {
    return this.isDisabled;
  }
}
