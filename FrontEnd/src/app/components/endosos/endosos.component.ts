import { Component, Inject } from "@angular/core";
import { DomSanitizer } from "@angular/platform-browser";
import { TuiPdfViewerOptions, TuiPdfViewerService } from "@taiga-ui/kit";
import { tap } from "rxjs/operators";
import { ChequesRechazadosService } from "src/app/cobros/cheques-rechazados/services/cheques-rechazados.service";
import { Buttons } from "src/app/cotizaciones/components/slip-pdf/slip-pdf.component";

@Component({
  selector: "endosos-icore",
  template: ` <ng-content></ng-content> `,
  styles: [],
})
export class EndososComponent {
  constructor(
    private chequesRechazadosService: ChequesRechazadosService,
    @Inject(DomSanitizer) private readonly sanitizer: DomSanitizer,
    @Inject(TuiPdfViewerService)
    private readonly pdfService: TuiPdfViewerService
  ) {}

  show(options: TuiPdfViewerOptions<Buttons>, query: string) {
    this.chequesRechazadosService
      .getNC64(query)
      .pipe(
        tap((value) => {
          const result = `data:application/pdf;base64, ${value.data}`;

          this.pdfService
            .open(
              this.sanitizer.bypassSecurityTrustResourceUrl(result),
              options
            )
            .subscribe();
        })
      )
      .subscribe();
  }
}
