<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="mat-elevation-z8">
        <div class="page_title">
          <h1>{{ page_title }}</h1>
        </div>
        <hr />
        <mat-form-field>
          <input matInput (keyup)="filtrar($event)" placeholder="Buscar" />
        </mat-form-field>

        <div class="container-btn-new">
          <div class="btn-new">
            <button mat-flat-button color="primary" (click)="onNew()">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <div class="table-responsive" perfectScrollbar>
          <table
            mat-table
            [dataSource]="dataSource"
            matSort
            class="mat-elevation-z8"
          >
            <!-- Id Column -->
            <ng-container matColumnDef="IDREPUESTO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Id</th>
              <td mat-cell *matCellDef="let element">
                {{ element.IDREPUESTO }}
              </td>
            </ng-container>

            <!-- CODREPUESTO Column -->
            <ng-container matColumnDef="CODREPUESTO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Código</th>
              <td mat-cell *matCellDef="let element">
                {{ element.CODREPUESTO }}
              </td>
            </ng-container>

            <!-- DESCREPUESTO Column -->
            <ng-container matColumnDef="DESCREPUESTO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Nombre</th>
              <td mat-cell *matCellDef="let element">
                {{ element.DESCREPUESTO }}
              </td>
            </ng-container>

            <!-- TIPOREPUESTO Column -->
            <ng-container matColumnDef="TIPOREPUESTO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Tipo</th>
              <td mat-cell *matCellDef="let element">
                {{ element.TIPOREPUESTO }}
              </td>
            </ng-container>

            <!-- DESCRIPCION_REPUESTO Column -->
            <ng-container matColumnDef="DESCRIPCION_REPUESTO">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Descripción Tipo
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.DESCRIPCION_REPUESTO }}
              </td>
            </ng-container>

            <!-- OBSERVACION Column -->
            <ng-container matColumnDef="OBSERVACION">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Observación
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.OBSERVACION }}
              </td>
            </ng-container>

            <!-- CODPARTE Column -->
            <ng-container matColumnDef="CODPARTE">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Clase</th>
              <td mat-cell *matCellDef="let element">{{ element.CODPARTE }}</td>
            </ng-container>

            <!-- DESCRIPCION_CLASE Column -->
            <ng-container matColumnDef="DESCRIPCION_CLASE">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>
                Descripción Clase
              </th>
              <td mat-cell *matCellDef="let element">
                {{ element.DESCRIPCION_CLASE }}
              </td>
            </ng-container>

            <!-- Action Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef mat-sort-header></th>
              <td mat-cell *matCellDef="let element">
                <button
                  mat-flat-button
                  color="primary"
                  (click)="onEdit(element)"
                  matTooltip="Editar Repuesto"
                  matTooltipClass="tooltip-red"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                &nbsp;
                <button
                  mat-flat-button
                  color="accent"
                  (click)="onDelete(element)"
                  matTooltip="Eliminar Repuesto"
                  matTooltipClass="tooltip-red"
                >
                  Eliminar<mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>
        </div>
        <mat-paginator
          [pageSizeOptions]="[5, 10, 20]"
          showFirstLastButtons
        ></mat-paginator>
      </div>
    </div>
  </div>
</div>
