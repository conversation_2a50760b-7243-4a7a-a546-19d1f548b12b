table {
    width: 100%;
  }

  .container {
    display: flex;
    justify-content: flex-end;
  }
  
  .page_title{  
      margin-top: 1rem!important;
      margin-left: 1rem;
  
  }
  
  .mat-form-field {
    font-size: 1rem;
    width: 98%;
    margin: 0 auto;
    display: block;
    margin-top: 0rem;
  }
  
  .container-btn-new {
    display: flex;
    justify-content: flex-end;  
    width: 100%;
    margin: 1rem -1rem;    
  }
  
  .btn-new {
    width: 7%;
    cursor: pointer;
    color: white;  
  }
  
  .tooltip-red {
    background: #b71c1c;
  }

  .mat-footer-row:first-child td {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
  }

    .button-group {
      display: flex;
      justify-content: flex-end;
        /* Alinea los hijos a la derecha */
      gap: 2px;
      align-items: centeR;

      /* Opcional, para que no se desborden en pantallas pequeñas */
    }