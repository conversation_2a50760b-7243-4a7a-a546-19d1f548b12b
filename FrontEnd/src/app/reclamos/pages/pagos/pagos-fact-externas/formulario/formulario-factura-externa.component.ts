import { Component, OnInit } from "@angular/core";
import { FormControl, FormGroup, Validators } from "@angular/forms";
import { MatSnackBar } from "@angular/material/snack-bar";
import { ActivatedRoute, Router } from "@angular/router";
import { ListClienteComponent } from "src/app/components/list-cliente/list-cliente.component";
import { ModalService } from "src/app/_services/modal.service";
import Swal from "sweetalert2";
import { FacturaExternaPayload, TipoFactura } from "src/app/interface/facturas-externas.interface";
import { FacturasExternasService } from "../../../../services/facturas-externas.service";

@Component({
  selector: "app-formulario-factura-externa",
  templateUrl: "./formulario-factura-externa.component.html",
  styleUrls: ["./formulario-factura-externa.component.css"],
})
export class FormularioFacturaExternaComponent implements OnInit {
  formFacturaExterna = new FormGroup({
    fecha: new FormControl("", [Validators.required]),
    persona: new FormControl("", [Validators.required]),
    facturano: new FormControl("", [Validators.required]),
    tipo: new FormControl("", [Validators.required]),
    cantidad: new FormControl("", [Validators.required, Validators.min(0)]),
    totalfactura: new FormControl("", [Validators.required, Validators.min(0)]),
    idcliente: new FormControl(""),
  });

  updating: boolean = false;
  textTooltip = "Guardar";
  tiposFactura$ = this.facturasExternasService.getTiposFactura();
  idfacturaexterna: number | null = null;

  constructor(
    private facturasExternasService: FacturasExternasService,
    private modalService: ModalService,
    private snackBar: MatSnackBar,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.idfacturaexterna = +params['id'];
        this.updating = true;
        this.textTooltip = "Actualizar";
        this.cargarFacturaExterna();
      }
    });
  }

  cargarFacturaExterna() {
    if (this.idfacturaexterna) {
      this.facturasExternasService.getFacturaExternaById(this.idfacturaexterna).subscribe({
        next: (factura) => {
          this.formFacturaExterna.patchValue({
            fecha: factura.fecha,
            persona: `${factura.idcliente} | ${factura.persona}`,
            facturano: factura.facturano,
            tipo: factura.tipo,
            cantidad: factura.cantidad,
            totalfactura: factura.totalfactura,
            idcliente: factura.idcliente?.toString(),
          });
        },
        error: () => {
          Swal.fire("Error", "Error al cargar la factura externa", "error");
          this.router.navigate(["/reclamos/pagos/facturas-externas"]);
        },
      });
    }
  }

  searchCliente() {
    const actionClose = {
      next: (data: any) => {
        this.formFacturaExterna.controls["persona"].setValue(
          `${data.idcliente} | ${data.nombre}`
        );
        this.formFacturaExterna.controls["idcliente"].setValue(data.idcliente);
      },
      error: (err: Error) => console.error("Observer got an error: ", err),
    };
    this.modalService.openDialog<ListClienteComponent>({
      title: "Listado de clientes",
      component: ListClienteComponent,
      actionClose,
    });
  }

  calcularIvaYNeto() {
    const totalFactura = this.formFacturaExterna.controls["totalfactura"].value;
    const cantidad = this.formFacturaExterna.controls["cantidad"].value;
    
    if (totalFactura && cantidad) {
      const total = parseFloat(totalFactura);
      const cant = parseFloat(cantidad);
      
      // Calcular IVA (12% del total)
      const iva = total * 0.12;
      // Calcular neto (total - iva)
      const neto = total - iva;
      
      // Estos valores se calcularán automáticamente en el backend
      // pero podemos mostrarlos como preview
    }
  }

  submitFacturaExterna() {
    if (this.formFacturaExterna.invalid) {
      Swal.fire("Error", "Por favor complete todos los campos requeridos", "error");
      return;
    }

    const idcliente = this.formFacturaExterna.controls["idcliente"].value;
    
    if (!idcliente) {
      Swal.fire("Error", "Debe seleccionar un cliente", "error");
      return;
    }

    let payload: FacturaExternaPayload = {
      fecha: this.formFacturaExterna.controls["fecha"].value!,
      idcliente: parseInt(idcliente),
      facturano: this.formFacturaExterna.controls["facturano"].value!,
      tipo: this.formFacturaExterna.controls["tipo"].value!,
      cantidad: parseFloat(this.formFacturaExterna.controls["cantidad"].value!),
      totalfactura: parseFloat(this.formFacturaExterna.controls["totalfactura"].value!),
    };

    if (!this.updating) {
      this.saveFacturaExterna(payload);
    } else {
      this.updateFacturaExterna(payload);
    }
  }

  private saveFacturaExterna(payload: FacturaExternaPayload) {
    this.facturasExternasService.createFacturaExterna(payload).subscribe({
      next: () => {
        this.snackBar.open("Factura externa guardada", "", {
          duration: 5 * 1000,
          horizontalPosition: "start",
        });
        this.router.navigate(["/reclamos/pagos/facturas-externas"]);
      },
      error: () => {
        Swal.fire("Error", "Error guardando la factura externa", "error");
      },
    });
  }

  private updateFacturaExterna(payload: FacturaExternaPayload) {
    if (this.idfacturaexterna) {
      this.facturasExternasService.updateFacturaExterna(this.idfacturaexterna, payload).subscribe({
        next: () => {
          this.snackBar.open("Factura externa actualizada", "", {
            duration: 5 * 1000,
            horizontalPosition: "start",
          });
          this.router.navigate(["/reclamos/pagos/facturas-externas"]);
        },
        error: () => {
          Swal.fire("Error", "Error actualizando la factura externa", "error");
        },
      });
    }
  }

  cancelar() {
    this.router.navigate(["/reclamos/pagos/facturas-externas"]);
  }
}
