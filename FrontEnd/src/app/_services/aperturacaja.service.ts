import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Observable } from "rxjs";
import { AperturaCaja } from "../models/apertura-caja";
import { global } from "./global";

@Injectable()
export class AperturacajaService {
    
    public url: string;

    constructor(
        private _http: HttpClient
    ) {
        this.url = global.url;
    }

    test(){
        return "Hola mundo desde el servicio aperturacaja!!!";
    }

    create(/*token,*/ aperturacaja): Observable<any>{        
        let params = aperturacaja;
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'apertura-caja', params, {headers: headers});
    }

    getCajaActiva(user): Observable<any>{        
        let params = user;
        let headers = new HttpHeaders().set('Content-Type', 'application/json');
        return this._http.post(this.url+'apertura-caja/cajaActiva', params, {headers: headers});
    }
}