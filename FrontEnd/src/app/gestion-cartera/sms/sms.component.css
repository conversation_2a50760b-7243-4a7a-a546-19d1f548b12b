.container{
    display: flex;
    flex-direction: column;
    margin-top: 2rem;
    align-items: center;
    justify-content: center;
}

form {
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
  padding: 6px 0;
  cursor: pointer;
  width: 100%;
  margin-bottom: 20px;   
}

mat-form-field{
  width: 90%;
}

.page_title {
    text-align: center;
}

.container > * {
    width: 100%;
    margin-left: 1%;
    align-items: center;
    justify-content: center;
}

.container-button{
  min-height: 80px;
}

mat-dialog-actions {
  padding: 0 0 25px 0;  
  align-items: center;
  box-sizing: content-box;  
}

.example-form {
  min-width: 150px;
  max-width: 500px;
  width: 100%;
}

.example-full-width {
  width: 100%;
}