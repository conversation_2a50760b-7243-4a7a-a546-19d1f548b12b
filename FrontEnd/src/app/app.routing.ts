
import { Routes, RouterModule } from '@angular/router';
import { PublicLayoutComponent } from './_layout/public-layout/public-layout.component';
import { PrivateLayoutComponent } from './_layout/private-layout/private-layout.component';
import { AuthGuard } from './_guards/auth.guard';
import { RegisterComponent } from './register';
import { LoginComponent } from './login';
import { ChangelogComponent } from './changelog/changelog.component';
import { EjecutivoComponent } from './ejecutivo/ejecutivo.component';
import { CajaComponent } from "./caja/caja.component";
import { CajeroComponent } from "./cajero/cajero.component";
import { DetalleCajeroComponent } from "./cobros/cajero/detalle-cajero/detalle-cajero.component";
import { AperturaCajaComponent } from "./cobros/apertura-caja/apertura-caja.component";
import { EntidadAcreedoraComponent } from "./entidad-acreedora/entidad-acreedora.component";
import { DetalleEntidadAcreedoraComponent } from "./entidad-acreedora/detalle-entidad-acreedora/detalle-entidad-acreedora.component";
import { DepositoComponent } from './deposito/deposito.component';
import { CompromisoPagoComponent } from "./compromiso-pago/compromiso-pago.component";
import { DetalleCompromisoPagoComponent } from "./compromiso-pago/detalle-compromiso-pago/detalle-compromiso-pago.component";
import { RelacionIngresoComponent } from './relacion-ingreso/relacion-ingreso.component';
import { DetalleDepositoComponent } from './deposito/detalle-deposito/detalle-deposito.component';
import { CajaGeneralComponent } from "./caja-general/caja-general.component";
import { RefraccionamientoComponent } from "./refraccionamiento/refraccionamiento.component";
import { GestionCarteraComponent } from "./gestion-cartera/gestion-cartera.component";
import { DetalleGestionComponent } from "./gestion-cartera/detalle-gestion/detalle-gestion.component";
import{FacturasAntComponent} from './facturas-ant/facturas-ant.component';
import{ PrestamoVidaIndividualComponent } from './prestamo-vida-individual/prestamo-vida-individual.component';
import { CierreVidaComponent } from './cierre-vida/cierre-vida.component';

/* Components de subasta */
import { ListadoProveedoresComponent } from './components/proveedores/listado-proveedores/listado-proveedores.component';
import { ListadoRepuestosComponent } from './components/repuestos/listado-repuestos/listado-repuestos.component';
import { ListadoCotizacionesComponent } from './components/cotizaciones/listado-cotizaciones/listado-cotizaciones.component';
import { DetalleCotizacionComponent } from './components/cotizaciones/listado-cotizaciones/detalle-cotizacion/detalle-cotizacion.component';
import { OfertantesComponent } from './components/cotizaciones/listado-cotizaciones/ofertantes/ofertantes.component';

/* Componeneste Cheques Rechazados */
import { ListadoChequesRechazadosComponent } from './cheques-rechazados/pages/listado-cheques-rechazados/listado-cheques-rechazados.component';

/* Componentes de Bitacora Manual */
import { BitacoraManualComponent } from './gestion-cartera/bitacora-manual/bitacora-manual.component';

/* Componentes de Precancelaciones */
import { PrecancelacionesComponent } from './precancelaciones/pages/precancelaciones/precancelaciones.component';
import { DetallePrecancelacionesComponent } from './precancelaciones/pages/precancelaciones/detalle-precancelaciones/detalle-precancelaciones.component';
import { NuevoPrecancelacionesComponent } from './precancelaciones/pages/precancelaciones/nuevo-precancelaciones/nuevo-precancelaciones.component';
/* Cancelaciones */
import { CancelacionesComponent } from './cancelaciones/pages/cancelaciones/cancelaciones.component';
import { DetalleCancelacionesComponent } from './cancelaciones/pages/cancelaciones/detalle-cancelaciones/detalle-cancelaciones.component';
/* Rehabilitaciones */
import { RehabilitacionesComponent } from './rehabilitaciones/pages/rehabilitaciones/rehabilitaciones.component';
/* Planillas de Cobro */
import { PlanillasCobroComponent } from './planillas-cobro/pages/planillas-cobro/planillas-cobro.component';
import { DetallePlanillaCobroComponent } from './planillas-cobro/pages/planillas-cobro/detalle-planilla-cobro/detalle-planilla-cobro.component';
/* Carga Masiva */
import { CargaMasivaComponent } from './carga-masiva/pages/carga-masiva/carga-masiva.component';
import { DetalleCargaMasivaComponent } from './carga-masiva/pages/carga-masiva/detalle-carga-masiva/detalle-carga-masiva.component';

/*Componente Clientes */
import { ClientesComponent } from './clientes/clientes.component';

/* Componente Personas */
import { PersonasComponent } from './personas/personas.component';

/* Componente Intermediarios */
import { IntermediariosComponent } from './intermediarios/intermediarios.component';

/* Componente cobradores */
import { CobradoresComponent } from './cobradores/cobradores.component';

/* Componente comisiones */
import { ComisionesComponent } from './comisiones/comisiones.component';
import { FormUnidadRamoComponent } from './comisiones/comision-unidad-ramo/form/form.component';
import { DetalleIntermediarioComponent } from './intermediarios/detalle-intermediario/detalle-intermediario.component';

//Componente de cotizaciones 
import { CotizacionesComponent } from './cotizaciones/cotizaciones.component';
import { FormulariosComponent } from './cotizaciones/formularios/formularios.component';

//Componente de polizas 
import { PolizasComponent } from './polizas/polizas.component';
import { DetailsCotizacionComponent } from './cotizaciones/pages/details-cotizacion/details-cotizacion.component';

//Certificados cotizados 
import { CertCotizacionComponent } from './cert-cotizacion/cert-cotizacion.component';
import { ActivacionComponent } from './polizas/operaciones/activacion/activacion.component';


const appRoutes: Routes = [
  // Public layout
  {
    path: '',
    component: PublicLayoutComponent,
    children: [
      { path: 'register', component: RegisterComponent },
      { path: 'login', component: LoginComponent }
    ]
  },
  
  // Private layout
  {
    path: '',
    component: PrivateLayoutComponent,
    children: [
      { path: 'logout', component: LoginComponent, canActivate: [AuthGuard] },
      // { path: 'changelog', component: ChangelogComponent, canActivate: [AuthGuard]},
      { path: 'ejecutivo', component: EjecutivoComponent, canActivate: [AuthGuard]},
      { path: 'caja' , component:CajaComponent,  canActivate: [AuthGuard]},
      //
      { path: 'cajero' , component:CajeroComponent,  canActivate: [AuthGuard]},
      { path: 'detalle-cajero/:id' , component:DetalleCajeroComponent,  canActivate: [AuthGuard]},      
      //
      { path: 'aperturacierrecaja' , component:AperturaCajaComponent,  canActivate: [AuthGuard]},
      
      { path: 'entidadacreedora' , component:EntidadAcreedoraComponent,  canActivate: [AuthGuard]},
      { path: 'entidadacreedora/detalleentidadacreedora/:id' , component:DetalleEntidadAcreedoraComponent,  canActivate: [AuthGuard]},
      { path: 'Deposito' , component:DepositoComponent,  canActivate: [AuthGuard]},
      { path: 'compromisopago' , component:CompromisoPagoComponent,  canActivate: [AuthGuard]},
      { path: 'compromisopago/detallecompromisopago/:id' , component:DetalleCompromisoPagoComponent,  canActivate: [AuthGuard]},
      { path: 'relacioningreso' , component:RelacionIngresoComponent,  canActivate: [AuthGuard]},
      { path: 'Deposito/detalledeposito/:id' , component:DetalleDepositoComponent,  canActivate: [AuthGuard]},

      { path: 'cajageneral' , component:CajaGeneralComponent,  canActivate: [AuthGuard]},
      { path: 'refraccionamiento' , component:RefraccionamientoComponent,  canActivate: [AuthGuard]},
      { path: 'gestioncartera' , component:GestionCarteraComponent,  canActivate: [AuthGuard]},
      { path: 'gestioncartera/detalle/:id' , component:DetalleGestionComponent,  canActivate: [AuthGuard]},
      { path: 'facturasant' , component:FacturasAntComponent,  canActivate: [AuthGuard]},
      { path: 'prestamovidaindividual/generar' , component:PrestamoVidaIndividualComponent,  canActivate: [AuthGuard]},      
      { path: 'cierrevida/generar' , component:CierreVidaComponent,  canActivate: [AuthGuard]},      
	  
      /* Rutas de proveedores */
      { path: 'lista-proveedores' , component:ListadoProveedoresComponent,  canActivate: [AuthGuard]},

      /* Rutas de Repuestos */
      { path: 'lista-repuestos' , component:ListadoRepuestosComponent,  canActivate: [AuthGuard]},

      /* Rutas de Cotizaciones */
      { path: 'lista-cotizaciones' , component:ListadoCotizacionesComponent,  canActivate: [AuthGuard]},
      /* Rutas de Detalle Cotizaciones */
      { path: 'lista-cotizaciones/detalle/:id' , component:DetalleCotizacionComponent,  canActivate: [AuthGuard]},
      /* Rutas de Detalle Ofertantes */
      { path: 'lista-cotizaciones/detalle/:id/ofertantes/:idrepuesto' , component: OfertantesComponent,  canActivate: [AuthGuard]},
      /* Ruta de bitacora manual */
      { path: 'gestioncartera/bitacora/:id', component: BitacoraManualComponent, canActivate: [AuthGuard] },
      /* Rutas de Gestion de Precancelaciones */
      { path: 'gestion-precancelaciones', component: PrecancelacionesComponent, canActivate: [AuthGuard] },
      { path: 'gestion-precancelaciones/:id', component: DetallePrecancelacionesComponent, canActivate: [AuthGuard] },
      { path: 'precancelaciones/nuevo', component: NuevoPrecancelacionesComponent, canActivate: [AuthGuard] },

      /* Rutas Cancelaciones */
      { path: 'cancelaciones-rehabilitacion', component: CancelacionesComponent, canActivate: [AuthGuard] },
      { path: 'cancelaciones-rehabilitacion/:id', component: DetalleCancelacionesComponent, canActivate: [AuthGuard] },

      /* Rutas de Rehabilitaciones */
      { path: 'rehabilitaciones', component: RehabilitacionesComponent, canActivate: [AuthGuard] },
      /* Planilla de Cobro */
      { path: 'planillas-cobro', component: PlanillasCobroComponent, canActivate: [AuthGuard] },
      { path: 'planillas-cobro/detalle/:id', component: DetallePlanillaCobroComponent, canActivate: [AuthGuard] },
      { path: 'planillas-cobro/nuevo', component: DetallePlanillaCobroComponent, canActivate: [AuthGuard] },
      /* Carga Masiva */
      { path: 'carga-masiva', component: CargaMasivaComponent, canActivate: [AuthGuard] },
      { path: 'carga-masiva/detalle/:id', component: DetalleCargaMasivaComponent, canActivate: [AuthGuard] },
      { path: 'carga-masiva/nuevo', component: DetalleCargaMasivaComponent, canActivate: [AuthGuard] },
      { path: 'persona', component: PersonasComponent, canActivate: [AuthGuard] },
      { path: 'cliente', component: ClientesComponent, canActivate: [AuthGuard] },
      { path: 'intermediario', component: IntermediariosComponent, canActivate: [AuthGuard] },
      { path: 'cobrador', component: CobradoresComponent, canActivate: [AuthGuard] },
      { path: 'comisiones', component: ComisionesComponent, canActivate: [AuthGuard] },
      { path: 'comisiones/:id', component: FormUnidadRamoComponent, canActivate: [AuthGuard] },
      { path: 'detalleintermediario/:id', component: DetalleIntermediarioComponent, canActivate: [AuthGuard] },
      {path: 'ejecutivo' , component:EjecutivoComponent, canActivate: [AuthGuard]},
     
      //Rutas de polizas 
      {path: 'polizas' , component: PolizasComponent, canActivate: [AuthGuard]},

      //Rutas de cotizaciones 
      {path: 'cotizador' , component: CotizacionesComponent, canActivate: [AuthGuard]},
      { path: 'cotizacion/:id', component: FormulariosComponent, canActivate: [AuthGuard] },
      { path: 'cotizacion/:id/:numcert', component: FormulariosComponent, canActivate: [AuthGuard] },
      //Listado de certificados cotizados
      { path: 'cert-cotizacion', component: CertCotizacionComponent, canActivate: [AuthGuard] },
      //Operacion - activar poliza 
      { path: 'poliza/activacion/:id', component: ActivacionComponent, canActivate: [AuthGuard] },
      { path: 'cotizacion/detail/:id', component: DetailsCotizacionComponent, canActivate:[AuthGuard] },
    ],
  },

  {
    path: '',
    component: PrivateLayoutComponent,
    children: [
      { path: 'lista-cheques-rechazados' , component: ListadoChequesRechazadosComponent,  canActivate: [AuthGuard]},
    ]
  },
  // otherwise redirect to home
  {
    path: '',
    component: PrivateLayoutComponent,
    children: [
      { path: 'logout', component: LoginComponent, canActivate: [AuthGuard] },
      { path: 'changelog', component: ChangelogComponent, canActivate: [AuthGuard] }, 
      // { path: 'ejecutivo', component: EjecutivoComponent, canActivate: [AuthGuard]}       
    ],
  },
  { path: '**', redirectTo: 'changelog' }
];

export const routing = RouterModule.forRoot(appRoutes, { scrollOffset: [0, 0], scrollPositionRestoration: 'top', relativeLinkResolution: 'legacy' });
