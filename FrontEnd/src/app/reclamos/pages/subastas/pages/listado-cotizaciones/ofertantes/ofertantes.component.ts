import { Component, OnInit, Output, ViewChild } from "@angular/core";
import { MatPaginator, PageEvent } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatTableDataSource } from "@angular/material/table";
import { ActivatedRoute, Router } from "@angular/router";
import { EventEmitter } from "events";
import { global } from "src/app/_services/global";
import { VwOfertas } from "../../../interface/vw-ofertas";
import { OfertaService } from "../ofertantes/oferta.service";
import swal from "sweetalert2";
import { Location } from "@angular/common";

@Component({
  selector: "app-ofertantes",
  templateUrl: "./ofertantes.component.html",
  styleUrls: ["./ofertantes.component.css"],
})
export class OfertantesComponent implements OnInit {
  public page_title: string;
  public status: string;
  public token;
  public identity;
  public url;
  public idcoti: number;
  public idrepuesto: number;

  public dataSource = null;

  listadoOfertas: VwOfertas[];

  // MatPaginator Inputs
  length = 100;
  pageSize = 10;
  pageSizeOptions: number[] = [5, 10, 25, 100];

  // MatPaginator Output
  pageEvent: PageEvent;

  @Output() montoMoneda = new EventEmitter();

  constructor(
    private _router: Router,
    private location: Location,
    private _route: ActivatedRoute,
    private _ofertaService: OfertaService
  ) {
    this.page_title = "Ofertantes por Repuesto";
    this.url = global.url;
  }

  displayedColumns: string[] = [
    "idoferta",
    "codigo",
    "fecha_cotizacion",
    "usuario",
    "precio_autorizado",
    "nombre",
    "monto_oferta",
    "fecha_ingreso",
    "estado",
    "actions",
  ];

  @ViewChild(MatPaginator, { static: true }) paginator: MatPaginator;
  @ViewChild(MatSort, { static: true }) sort: MatSort;
  ngOnInit() {
    this.getVwOfertantes();
  }

  getVwOfertantes() {
    let vwOfertas: VwOfertas[];

    this._route.params.subscribe((params) => {
      this.idcoti = +params["id"];
      this.idrepuesto = +params["idrepuesto"];
    });
    this._ofertaService
      .getVwOfertantesByDetalleCotizacion(this.idcoti, this.idrepuesto)
      .subscribe(
        (response) => {
          if (response.status == "success") {
            vwOfertas = response.vwOfertas;
            this.listadoOfertas = vwOfertas;
            this.listadoOfertas = this.filterFunction(
              this.listadoOfertas,
              "ACP"
            );
          }
          this.dataSource = new MatTableDataSource<VwOfertas>(vwOfertas);
          this.dataSource.paginator = this.paginator;
          this.dataSource.sort = this.sort;
        },
        (error) => {
          console.log(<any>error);
          //this.status = 'error';
        }
      );
  }

  onBack() {
    this.location.back();
  }

  setPageSizeOptions(setPageSizeOptionsInput: string) {
    if (setPageSizeOptionsInput) {
      this.pageSizeOptions = setPageSizeOptionsInput
        .split(",")
        .map((str) => +str);
    }
  }

  filtrar(event: Event) {
    const filtro = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filtro.trim().toLowerCase();
  }

  onDisabledBtnStatus(status) {
    if (this.listadoOfertas.length < 1) return false;
    else return true;
  }

  onDisabledBtnRevert(status) {
    if (this.listadoOfertas.length > 0 && status === "ACP") return false;
    else return true;
  }

  filterFunction(datos, codigo): any[] {
    return datos.filter((i) => i.estado == codigo);
  }

  onAceptar(cotizacion) {
    swal
      .fire({
        title: "¿Cambiar Estado de la Oferta?",
        text: "Deseas Cambiar el Estado a la Oferta?",
        icon: "warning",
        showCancelButton: true,
        showConfirmButton: true,
      })
      .then((willActivate) => {
        if (willActivate) {
          this._ofertaService.cambiarEstado(cotizacion).subscribe(
            (response) => {
              swal.fire({
                text: "Proceso finalizado correctamente!",
                icon: "success",
              });
              this.getVwOfertantes();
            },
            (error) => {
              swal.fire({
                text: "Proceso no pudo completarse!",
                icon: "error",
              });
            }
          );
        } else {
          swal.fire("Los datos no han cambiado");
        }
      });
  }
}
