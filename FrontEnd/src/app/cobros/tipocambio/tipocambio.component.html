<div class="app-content content">
  <div class="content-wrapper">
    <div class="content-body">
      <div class="page_title text-center">
        <h1>Cambio del dia</h1>
      </div>
      <hr />
      <div class="container-btn-new">
        <div class="btn-new">
          <button mat-flat-button color="primary" (click)="newTipoCambio()">
            <mat-icon>add</mat-icon>
          </button>
        </div>
      </div>
      <icore-table
        *ngIf="data$ | async as cambio; else loading"
        [columns]="displayedColumns"
        [data]="cambio"
        [filter]="true"
      ></icore-table>

      <ng-template #loading>
        <ngx-skeleton-loader
          [count]="4"
          appearance="line"
        ></ngx-skeleton-loader>
      </ng-template>
    </div>
  </div>
</div>
